"use client";

import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight, Zap } from 'lucide-react';

export default function CallToActionSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          {/* <Zap className="h-16 w-16 text-[#028475] mx-auto mb-6" /> */}
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            Revolutionize Your Supply Chain?
          </h2>
          <p className="text-xl text-gray-700 mb-8">
            Discover how StreamLnk's intelligent platform and expert insights can transform your supply chain operations, driving efficiency, resilience, and growth. Let's build a smarter supply chain, together.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                REQUEST DEMO
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button 
              variant="outline" 
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/solutions">
                EXPLORE SOLUTIONS
              </Link>
            </Button>
          </div>
          <div className="mt-6">
            <Button 
              variant="link" 
              className="text-[#028475] hover:text-[#004235]"
              asChild
            >
              <Link href="/contact-us">
                TALK TO EXPERT
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}