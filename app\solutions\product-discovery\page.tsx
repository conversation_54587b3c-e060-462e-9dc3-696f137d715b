"use client"

import { MainNav } from "@/components/main-nav"
import { BottomFooter } from "@/components/bottom-footer"
import { HeroSection } from "@/components/solutions/product-discovery/HeroSection"
import { ProblemStatementSection } from "@/components/solutions/product-discovery/ProblemStatementSection"
import { FeaturesSection } from "@/components/solutions/product-discovery/FeaturesSection"
import { WorkflowProcessSection } from "@/components/solutions/product-discovery/WorkflowProcessSection"
import { BenefitsSection } from "@/components/solutions/product-discovery/BenefitsSection"
import { CtaSection } from "@/components/solutions/product-discovery/CtaSection"

export default function ProductDiscoveryPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      {/* Hero Section */}
      <HeroSection />

      {/* Problem Statement Section */}
      <ProblemStatementSection />

      {/* Features Section */}
      <FeaturesSection />

      {/* Workflow Process Section */}
      <WorkflowProcessSection />

      {/* Benefits Section */}
      <BenefitsSection />

      {/* CTA Section */}
      <CtaSection />

      <BottomFooter />
    </div>
  )
}