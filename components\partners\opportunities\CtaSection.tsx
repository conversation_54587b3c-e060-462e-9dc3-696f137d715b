"use client";

import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight, Users, FileText, Download } from 'lucide-react';

export default function CtaSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Find Your Partnership Path and Let's Transform Industrial Trade Together.
          </h2>
          <p className="text-xl text-[#028475] font-semibold mb-6">
            Ready to Collaborate and Grow?
          </p>
          <p className="text-lg text-gray-700 mb-10 leading-relaxed">
            If you see an opportunity that aligns with your business, we encourage you to learn more and get in touch. StreamLnk is committed to fostering strong, mutually beneficial partnerships that drive innovation and efficiency across the global supply chain.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto group"
              size="lg"
              asChild
            >
              <Link href="/contact-us?subject=PartnershipInquiry&source=partnership-opportunities">
                <Users className="mr-2 h-5 w-5" />
                CONTACT PARTNERSHIP TEAM
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
            <Button 
              variant="outline" 
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto group"
              size="lg"
              asChild
            >
              <Link href="/partners/onboarding-process?source=partnership-opportunities">
                <FileText className="mr-2 h-5 w-5" />
                REVIEW ONBOARDING PROCESS
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
          </div>
          <div className="mt-6">
            <Button 
              variant="link" 
              className="text-[#028475] hover:text-[#004235] group"
              asChild
            >
              <Link href="/resources/downloads/partnership-overview-brochure.pdf?source=partnership-opportunities" target="_blank" rel="noopener noreferrer">
                <Download className="mr-2 h-4 w-4" />
                DOWNLOAD PARTNERSHIP BROCHURE
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}