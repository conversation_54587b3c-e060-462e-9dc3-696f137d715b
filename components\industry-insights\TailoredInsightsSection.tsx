"use client";

import Link from "next/link";
import { Factory, Beaker, Wind, Car, Package, Cog, TestTube, Building, Wheat, Recycle, Cpu, ChevronRight } from "lucide-react";

export default function TailoredInsightsSection() {
  const industries = [
    { name: "Polymers & Plastics", icon: <Factory className="h-8 w-8 text-[#028475]" />, link: "/industries/polymers-plastics" },
    { name: "Industrial Chemicals", icon: <Beaker className="h-8 w-8 text-[#028475]" />, link: "/industries/industrial-chemicals" },
    { name: "Energy", icon: <Wind className="h-8 w-8 text-[#028475]" />, link: "/industries/energy" },
    { name: "Automotive Supply Chain", icon: <Car className="h-8 w-8 text-[#028475]" />, link: "/industries/automotive" },
    { name: "Packaging Solutions", icon: <Package className="h-8 w-8 text-[#028475]" />, link: "/industries/packaging" },
    { name: "Engineering & Manufacturing", icon: <Cog className="h-8 w-8 text-[#028475]" />, link: "/industries/engineering-manufacturing" },
    { name: "Life Sciences & Healthcare Logistics", icon: <TestTube className="h-8 w-8 text-[#028475]" />, link: "/industries/life-sciences-healthcare" },
    { name: "Construction Materials", icon: <Building className="h-8 w-8 text-[#028475]" />, link: "/industries/construction-materials" },
    { name: "Agricultural Commodities", icon: <Wheat className="h-8 w-8 text-[#028475]" />, link: "/industries/agricultural-commodities" }, // Placeholder, assuming this page exists
    { name: "Recycling & Sustainable Materials", icon: <Recycle className="h-8 w-8 text-[#028475]" />, link: "/industries/recycling-sustainable-materials" }, // Placeholder
    { name: "Technology & Electronics Components", icon: <Cpu className="h-8 w-8 text-[#028475]" />, link: "/industries/technology-electronics" } // Placeholder
  ];

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Intelligence Tailored to Your Vertical
          </h2>
          <p className="text-xl text-gray-700">
            Explore Insights by Industry Sector
          </p>
          <p className="text-lg text-gray-600 mt-4">
            StreamLnk provides focused insights for the key industrial sectors we serve. Select your industry to access relevant articles, reports, and data highlights.
          </p>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4 gap-6">
          {industries.map((industry, index) => (
            <Link href={industry.link} key={index} className="group">
              <div className="bg-[#F2F2F2] p-6 rounded-lg text-center shadow-md hover:shadow-xl transition-shadow duration-300 flex flex-col items-center h-full">
                <div className="mb-3">{industry.icon}</div>
                <h3 className="font-semibold text-[#004235] text-md group-hover:text-[#028475] transition-colors duration-300">{industry.name}</h3>
              </div>
            </Link>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link href="/industries" className="inline-flex items-center text-[#028475] hover:text-[#004235] font-semibold text-lg">
            View All Industries We Serve
            <ChevronRight className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </section>
  );
}