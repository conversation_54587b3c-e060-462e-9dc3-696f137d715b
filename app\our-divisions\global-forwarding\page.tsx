import Link from "next/link"
import { CountrySelector } from "@/components/country-selector"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import { Button } from "@/components/ui/button"

export default function GlobalForwardingPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <CountrySelector />
      <MainNav />

      <main>
        {/* Hero Section */}
        <section className="bg-[#F2F2F2] py-16 md:py-24">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl">
              <div className="flex items-center mb-6">
                <div className="w-12 h-1 bg-[#028475] mr-3"></div>
                <span className="text-[#028475] font-medium">GLOBAL FORWARDING DIVISION</span>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">International Freight Forwarding</h1>
              <p className="text-lg text-gray-700 mb-8 max-w-2xl">
                Comprehensive international freight forwarding services across air, ocean, and multimodal transport with
                customs expertise.
              </p>
              <Button className="bg-[#004235] hover:bg-[#004235]/90 text-white" asChild>
                <Link href="/contact">Contact Global Forwarding Division</Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Content placeholder */}
        <section className="py-16">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-2xl font-bold text-[#004235] mb-4">Global Forwarding Division Content</h2>
            <p className="text-gray-700 mb-8">Detailed content for this division is coming soon.</p>
            <Button variant="outline" className="border-[#004235] text-[#004235]" asChild>
              <Link href="/our-divisions">Back to Divisions Overview</Link>
            </Button>
          </div>
        </section>
      </main>

      <MainFooter />
    </div>
  )
}
