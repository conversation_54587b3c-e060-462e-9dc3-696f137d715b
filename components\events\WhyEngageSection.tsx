"use client";

import { CheckCircle } from 'lucide-react';

const engagementBenefits = [
  {
    icon: <CheckCircle className="h-6 w-6 text-[#028475]" />,
    text: "Gain Expert Insights: Learn from StreamLnk leaders and industry experts on key trends in digitization, logistics, AI, sustainability, and market dynamics."
  },
  {
    icon: <CheckCircle className="h-6 w-6 text-[#028475]" />,
    text: "See Live Platform Demos: Experience firsthand how StreamLnk's integrated portals and AI-powered features can transform your operations."
  },
  {
    icon: <CheckCircle className="h-6 w-6 text-[#028475]" />,
    text: "Network with Peers & Professionals: Connect with other buyers, suppliers, agents, and service providers in your industry."
  },
  {
    icon: <CheckCircle className="h-6 w-6 text-[#028475]" />,
    text: "Stay Updated on Innovations: Be the first to hear about new StreamLnk platform features, partnerships, and strategic initiatives."
  },
  {
    icon: <CheckCircle className="h-6 w-6 text-[#028475]" />,
    text: "Ask Your Questions Directly: Engage in live Q&A sessions with our team and guest speakers."
  },
  {
    icon: <CheckCircle className="h-6 w-6 text-[#028475]" />,
    text: "Discover Best Practices: Learn how to optimize your sourcing, sales, and logistics strategies using digital tools."
  }
];

export default function WhyEngageSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Your Opportunity to Learn, Network, and Stay Ahead
          </h2>
          <p className="text-lg text-gray-700">
            Why Engage with StreamLnk Events & Webinars?
          </p>
        </div>
        <p className="text-lg text-gray-700 mb-10 max-w-4xl mx-auto text-center">
          StreamLnk is committed to fostering knowledge sharing and community building within the industrial trade ecosystem. Our events and webinars offer valuable opportunities to:
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6 max-w-4xl mx-auto">
          {engagementBenefits.map((benefit, index) => (
            <div key={index} className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-1">
                {benefit.icon}
              </div>
              <p className="text-gray-700">{benefit.text}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}