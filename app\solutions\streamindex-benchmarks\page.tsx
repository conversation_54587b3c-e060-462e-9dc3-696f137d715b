import type { Metada<PERSON> } from "next"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import HeroSection from "@/components/solutions/streamindex-benchmarks/HeroSection"
import ChallengesSection from "@/components/solutions/streamindex-benchmarks/ChallengesSection"
import WhatIsStreamIndexSection from "@/components/solutions/streamindex-benchmarks/WhatIsStreamIndexSection"
import DataDrivenSection from "@/components/solutions/streamindex-benchmarks/DataDrivenSection"
import CTASection from "@/components/solutions/streamindex-benchmarks/CTASection"

export const metadata: Metadata = {
  title: "StreamIndex™ Market Benchmarks | StreamLnk",
  description:
    "Gain a competitive edge with StreamLnk's proprietary suite of real-time market benchmarks. StreamIndex™ transforms vast ecosystem data into actionable intelligence on pricing, logistics performance, and supply chain risk.",
}

export default function StreamIndexBenchmarksPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <WhatIsStreamIndexSection />

      <DataDrivenSection />

      <CTASection />

      <MainFooter />
    </div>
  )
}