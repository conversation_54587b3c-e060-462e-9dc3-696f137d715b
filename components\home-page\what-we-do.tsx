import Image from "next/image"
import { Button } from "@/components/ui/button"

export function WhatWeDo() {
  return (
    <div className="bg-white">
      {/* Global Reach Section */}
      <section className="py-24 md:py-32 bg-[#172d2d]">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-16 lg:gap-20 items-center">
            <div className="flex justify-center">
              <div className="relative w-80 h-80 lg:w-96 lg:h-96">
                <Image
                  src="/images/homepage/Expand Your Global Reach.svg"
                  alt="Expand Your Global Reach"
                  width={384}
                  height={384}
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
            <div className="space-y-8">
              <p className="text-[#18b793] text-sm font-semibold uppercase tracking-wider">Global Energy Trading</p>
              <h2 className="text-4xl lg:text-5xl font-bold text-white leading-tight">Expand Your Global Reach</h2>
              <p className="text-gray-300 text-lg leading-relaxed">
                StreamLnk connects energy suppliers and buyers across the globe through our advanced digital platform. Access new markets, optimize your supply chain, and scale your operations with confidence in our secure, transparent marketplace.
              </p>
              <Button className="bg-transparent border-2 border-white !text-white hover:bg-white hover:!text-black px-8 py-3 rounded-full font-semibold transition-all duration-300">
                Explore Global Markets
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Cost Optimization Section */}
      <section className="py-24 md:py-32 bg-[#172d2d]">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <p className="text-[#18b793] text-sm font-semibold uppercase tracking-wider">Smart Operations</p>
              <h2 className="text-4xl lg:text-5xl font-bold text-white leading-tight">
                Cost Optimization & Supply Chain Transparency
              </h2>
              <p className="text-gray-300 text-lg leading-relaxed">
                Leverage AI-powered analytics and real-time market data to minimize procurement costs and optimize logistics. Our platform provides complete supply chain visibility with live tracking, automated reporting, and predictive insights for smarter decision-making.
              </p>
              <Button className="bg-transparent border-2 border-white !text-white hover:bg-white hover:!text-black px-8 py-3 rounded-full font-semibold transition-all duration-300">
                Optimize Operations
              </Button>
            </div>
            <div className="flex justify-center">
              <div className="relative w-80 h-80 lg:w-96 lg:h-96">
                <Image
                  src="/images/homepage/Smart Cost Optimization & Supply Chain Transparency.svg"
                  alt="Smart Cost Optimization & Supply Chain Transparency"
                  width={384}
                  height={384}
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Data Insights Section */}
      <section className="py-24 md:py-32 bg-[#172d2d]">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="flex justify-center">
              <div className="relative w-80 h-80 lg:w-96 lg:h-96">
                <Image
                  src="/images/homepage/Real-Time Data Insights & Digital Sourcing.svg"
                  alt="Real-Time Data Insights & Digital Sourcing"
                  width={384}
                  height={384}
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
            <div className="space-y-6">
              <p className="text-[#18b793] text-sm font-semibold uppercase tracking-wider">Data-Driven Trading</p>
              <h2 className="text-4xl lg:text-5xl font-bold text-white leading-tight">
                Real-Time Market Intelligence & Digital Sourcing
              </h2>
              <p className="text-gray-300 text-lg leading-relaxed">
                Make informed trading decisions with live market data, price analytics, and trend forecasting. Connect with a verified network of global energy suppliers through our AI-powered matching system that streamlines sourcing and accelerates deal closure.
              </p>
              <Button className="bg-transparent border-2 border-white !text-white hover:bg-white hover:!text-black px-8 py-3 rounded-full font-semibold transition-all duration-300">
                Explore Platform Features
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Live Trading Section */}
      <section className="py-24 md:py-32 bg-[#172d2d]">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <p className="text-[#18b793] text-sm font-semibold uppercase tracking-wider">Advanced Trading</p>
              <h2 className="text-4xl lg:text-5xl font-bold text-white leading-tight">
                Live Auctions & Technical Excellence
              </h2>
              <p className="text-gray-300 text-lg leading-relaxed">
                Participate in dynamic, real-time auctions with competitive bidding and instant execution. Access comprehensive technical services including market analysis, risk assessment, quality verification, and logistics coordination to ensure successful energy transactions.
              </p>
              <Button className="bg-transparent border-2 border-white !text-white hover:bg-white hover:!text-black px-8 py-3 rounded-full font-semibold transition-all duration-300">
                Start Trading Now
              </Button>
            </div>
            <div className="flex justify-center">
              <div className="relative w-80 h-80 lg:w-96 lg:h-96">
                <Image
                  src="/images/homepage/Real-Time Auctions & Technical Services.svg"
                  alt="Real-Time Auctions & Technical Services"
                  width={384}
                  height={384}
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
