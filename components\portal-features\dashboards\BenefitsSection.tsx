import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { CheckC<PERSON>cle2, ThumbsUp, CircleCheckBig, BarChartHorizontalBig, BriefcaseBusiness, Eye } from 'lucide-react';

export default function BenefitsSection() {
  return (
    <section className="w-full py-12 md:py-24 bg-[#f3f4f6]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center space-y-4 text-center mb-10">
          <h2 className="text-2xl font-bold tracking-tighter sm:text-3xl md:text-4xl text-[#004235]">
            Your Personalized Command Center for Optimal Performance
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="flex flex-col items-start p-6 bg-white rounded-lg shadow-md">
            <div className="bg-[#004235]/10 p-3 rounded-full mb-4 inline-block">
              <CheckCircle2 className="text-[#028475] h-8 w-8" />
            </div>
            <h3 className="text-lg font-bold mb-2 text-[#004235]">Increased Productivity</h3>
            <p className="text-gray-700">Find critical information and perform key tasks faster.</p>
          </div>

          <div className="flex flex-col items-start p-6 bg-white rounded-lg shadow-md">
            <div className="bg-[#004235]/10 p-3 rounded-full mb-4 inline-block">
              <ThumbsUp className="text-[#028475] h-8 w-8" />
            </div>
            <h3 className="text-lg font-bold mb-2 text-[#004235]">Improved Decision-Making</h3>
            <p className="text-gray-700">Act on real-time data and relevant insights.</p>
          </div>

          <div className="flex flex-col items-start p-6 bg-white rounded-lg shadow-md">
            <div className="bg-[#004235]/10 p-3 rounded-full mb-4 inline-block">
              <CircleCheckBig className="text-[#028475] h-8 w-8" />
            </div>
            <h3 className="text-lg font-bold mb-2 text-[#004235]">Proactive Management</h3>
            <p className="text-gray-700">Stay ahead of deadlines, alerts, and opportunities.</p>
          </div>

          <div className="flex flex-col items-start p-6 bg-white rounded-lg shadow-md">
            <div className="bg-[#004235]/10 p-3 rounded-full mb-4 inline-block">
              <BarChartHorizontalBig className="text-[#028475] h-8 w-8" />
            </div>
            <h3 className="text-lg font-bold mb-2 text-[#004235]">Enhanced Focus</h3>
            <p className="text-gray-700">Minimize distractions and concentrate on high-impact activities.</p>
          </div>

          <div className="flex flex-col items-start p-6 bg-white rounded-lg shadow-md">
            <div className="bg-[#004235]/10 p-3 rounded-full mb-4 inline-block">
              <BriefcaseBusiness className="text-[#028475] h-8 w-8" />
            </div>
            <h3 className="text-lg font-bold mb-2 text-[#004235]">Streamlined Workflows</h3>
            <p className="text-gray-700">Access integrated tools and information without switching platforms.</p>
          </div>

          <div className="flex flex-col items-start p-6 bg-white rounded-lg shadow-md">
            <div className="bg-[#004235]/10 p-3 rounded-full mb-4 inline-block">
              <Eye className="text-[#028475] h-8 w-8" />
            </div>
            <h3 className="text-lg font-bold mb-2 text-[#004235]">Enhanced Visibility</h3>
            <p className="text-gray-700">Gain a comprehensive view of your operations and key metrics.</p>
          </div>
        </div>
      </div>
    </section>
  );
}