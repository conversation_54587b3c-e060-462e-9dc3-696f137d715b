import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>aker, Building2, ChevronRight, CircuitBoard, Cog, Droplet, FileBox, Leaf, Pill, Recycle, Truck, Wind } from "lucide-react"

export default function IndustryCardsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Polymers & Plastics */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="bg-[#004235]/10 p-4 rounded-full mr-4">
                  <Droplet className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Polymers & Plastics</h3>
              </div>
              <p className="text-gray-700 mb-6">
                Focus: Sourcing resins, recycled plastics, additives; managing packaging & logistics; polymer market insights.
              </p>
              <Button 
                variant="outline" 
                className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white w-full justify-between"
                asChild
              >
                <Link href="/industries/polymers-plastics">
                  Explore Polymers Solutions
                  <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Industrial Chemicals */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="bg-[#004235]/10 p-4 rounded-full mr-4">
                  <Beaker className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Industrial Chemicals</h3>
              </div>
              <p className="text-gray-700 mb-6">
                Focus: Sourcing bulk & specialty chemicals; managing hazmat logistics & compliance (REACH/GHS); chemical market intelligence.
              </p>
              <Button 
                variant="outline" 
                className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white w-full justify-between"
                asChild
              >
                <Link href="/industries/industrial-chemicals">
                  Explore Chemicals Solutions
                  <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Energy */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="bg-[#004235]/10 p-4 rounded-full mr-4">
                  <Wind className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Energy (Oil, Gas, Renewables)</h3>
              </div>
              <p className="text-gray-700 mb-6">
                Focus: Trading & logistics for crude, fuels, feedstocks, RECs; sourcing materials for renewables infrastructure.
              </p>
              <Button 
                variant="outline" 
                className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white w-full justify-between"
                asChild
              >
                <Link href="/industries/energy">
                  Explore Energy Solutions
                  <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Automotive - THIS IS THE NEWLY ADDED CARD */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="bg-[#004235]/10 p-4 rounded-full mr-4">
                  <Truck className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Automotive</h3>
              </div>
              <p className="text-gray-700 mb-6">
                Focus: Sourcing polymers, metals, components; JIT logistics; compliance (IATF 16949); EV material supply chains.
              </p>
              <Button 
                variant="outline" 
                className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white w-full justify-between"
                asChild
              >
                <Link href="/industries/automotive">
                  Explore Automotive Solutions
                  <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Packaging */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="bg-[#004235]/10 p-4 rounded-full mr-4">
                  <FileBox className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Packaging</h3>
              </div>
              <p className="text-gray-700 mb-6">
                Focus: Sourcing packaging raw materials (resins, films); logistics for finished packaging; sustainable packaging supply chains.
              </p>
              <Button 
                variant="outline" 
                className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white w-full justify-between"
                asChild
              >
                <Link href="/industries/packaging">
                  Explore Packaging Solutions
                  <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Engineering & Manufacturing */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="bg-[#004235]/10 p-4 rounded-full mr-4">
                  <Cog className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Engineering & Manufacturing</h3>
              </div>
              <p className="text-gray-700 mb-6">
                Focus: Sourcing raw materials, components, MRO; complex BOM logistics; quality & compliance.
              </p>
              <Button 
                variant="outline" 
                className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white w-full justify-between"
                asChild
              >
                <Link href="/industries/engineering-manufacturing">
                  Explore Manufacturing Solutions
                  <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Life Sciences & Healthcare */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="bg-[#004235]/10 p-4 rounded-full mr-4">
                  <Pill className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Life Sciences & Healthcare</h3>
              </div>
              <p className="text-gray-700 mb-6">
                Focus: Sourcing specialized chemicals/materials; cold chain logistics; stringent regulatory compliance (FDA/GDP).
              </p>
              <Button 
                variant="outline" 
                className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white w-full justify-between"
                asChild
              >
                <Link href="/industries/life-sciences-healthcare">
                  Explore Healthcare Solutions
                  <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Construction Materials */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="bg-[#004235]/10 p-4 rounded-full mr-4">
                  <Building2 className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Construction Materials</h3>
              </div>
              <p className="text-gray-700 mb-6">
                Focus: Sourcing bulk materials (cement, aggregates, steel); project-based logistics; green building materials.
              </p>
              <Button 
                variant="outline" 
                className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white w-full justify-between"
                asChild
              >
                <Link href="/industries/construction-materials">
                  Explore Materials Solutions
                  <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Recycling & Sustainable Materials */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="bg-[#004235]/10 p-4 rounded-full mr-4">
                  <Recycle className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Recycling & Sustainable Materials</h3>
              </div>
              <p className="text-gray-700 mb-6">
                Focus: Trading recycled polymers, metals, paper; traceability; ESG reporting.
              </p>
              <Button 
                variant="outline" 
                className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white w-full justify-between"
                asChild
              >
                <Link href="/industries/recycling-sustainable-materials">
                  Explore Recycling Solutions
                  <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Agricultural Commodities */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="bg-[#004235]/10 p-4 rounded-full mr-4">
                  <Leaf className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Agricultural Commodities</h3>
              </div>
              <p className="text-gray-700 mb-6">
                Focus: Trading bulk agricultural products; specialized logistics & storage; quality control.
              </p>
              <Button 
                variant="outline" 
                className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white w-full justify-between"
                asChild
              >
                <Link href="/industries/agricultural-commodities">
                  Explore Commodities Solutions
                  <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Technology & Electronics Components */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="bg-[#004235]/10 p-4 rounded-full mr-4">
                  <CircuitBoard className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Technology & Electronics Components</h3>
              </div>
              <p className="text-gray-700 mb-6">
                Focus: Sourcing specialized materials & components; global logistics for high-value goods; traceability.
              </p>
              <Button 
                variant="outline" 
                className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white w-full justify-between"
                asChild
              >
                <Link href="/industries/technology-electronics">
                  Explore Electronics Solutions
                  <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}