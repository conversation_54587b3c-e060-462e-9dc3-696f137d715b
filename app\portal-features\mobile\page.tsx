// app/portal-features/mobile/page.tsx
import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/portal-features/mobile/HeroSection";
import NeedForMobileSection from "@/components/portal-features/mobile/NeedForMobileSection";
import MobileCapabilitiesSection from "@/components/portal-features/mobile/MobileCapabilitiesSection";
import BenefitsSection from "@/components/portal-features/mobile/BenefitsSection";
import ComingSoonSection from "@/components/portal-features/mobile/ComingSoonSection";
import InTheMeantimeSection from "@/components/portal-features/mobile/InTheMeantimeSection";

export default function MobilePortalPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <NeedForMobileSection />

      <MobileCapabilitiesSection />

      <BenefitsSection />

      <ComingSoonSection />

      <InTheMeantimeSection />

      <BottomFooter />
    </div>
  );
}