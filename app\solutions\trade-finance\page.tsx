"use client"

import Link from "next/link"
import Image from "next/image"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import HeroSection from "@/components/solutions/trade-finance/HeroSection"
import ChallengesSection from "@/components/solutions/trade-finance/ChallengesSection"
import SolutionOverviewSection from "@/components/solutions/trade-finance/SolutionOverviewSection"
import BenefitsSection from "@/components/solutions/trade-finance/BenefitsSection"
import CTASection from "@/components/solutions/trade-finance/CTASection"

export default function TradeFinancePage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <SolutionOverviewSection />

      <BenefitsSection />

      <CTASection />

      <MainFooter />
    </div>
  )
}