"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import Image from "next/image";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            {/* <Handshake className="h-16 w-16 text-[#028475] mb-6" /> Optional: Consider if icon fits here or better elsewhere */}
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Forge the Future, Together: Strategic Alliances with StreamLnk
            </h1>
            <p className="text-xl text-gray-700 mb-10 leading-relaxed">
              StreamLnk is seeking visionary industry leaders, major corporations, and influential organizations to form strategic alliances. Let's collaborate to accelerate the digitization of global industrial trade, co-develop groundbreaking solutions, and unlock unprecedented market opportunities.
            </p>
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white px-8 py-3 text-lg font-semibold"
              size="lg"
              asChild
            >
              <Link href="/contact-us?subject=Strategic+Alliance+Discussion&interest=LeadershipDiscussion">
                DISCUSS ALLIANCE
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="relative w-full h-[300px] md:h-[450px] rounded-lg overflow-hidden shadow-xl mt-12 lg:mt-0">
            <Image
              src="/images/placeholder-strategic-alliances.webp" // TODO: User to replace with a relevant image for strategic alliances
              alt="StreamLnk Strategic Alliances"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: User to replace with a relevant image for strategic alliances */}
          </div>
        </div>
      </div>
    </section>
  );
}