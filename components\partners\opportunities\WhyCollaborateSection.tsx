"use client";

import { Globe, Zap, Users, BarChartBig } from 'lucide-react';

export default function WhyCollaborateSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Why Collaborate with the Future of Industrial Commerce?
          </h2>
          <p className="text-xl text-[#028475] font-semibold mb-6">
            Partnering with StreamLnk – A Strategic Move for Growth
          </p>
          <p className="text-lg text-gray-700 leading-relaxed">
            Joining the StreamLnk partner network provides access to a dynamic, growing B2B ecosystem, cutting-edge technology, and a shared vision for a more efficient and transparent global trade landscape.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
          <div className="bg-[#f3f4f6] p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center text-center">
            <Globe className="h-12 w-12 text-[#028475] mb-4" />
            <h3 className="text-xl font-semibold text-[#004235] mb-2">Access to a Global Marketplace</h3>
            <p className="text-gray-600 text-sm leading-relaxed">Connect with thousands of buyers and sellers across diverse industrial sectors.</p>
          </div>
          <div className="bg-[#f3f4f6] p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center text-center">
            <Zap className="h-12 w-12 text-[#028475] mb-4" />
            <h3 className="text-xl font-semibold text-[#004235] mb-2">Advanced Digital Tools</h3>
            <p className="text-gray-600 text-sm leading-relaxed">Leverage our AI-powered portals, data insights, and integrated workflow solutions.</p>
          </div>
          <div className="bg-[#f3f4f6] p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center text-center">
            <BarChartBig className="h-12 w-12 text-[#028475] mb-4" />
            <h3 className="text-xl font-semibold text-[#004235] mb-2">New Revenue & Growth Channels</h3>
            <p className="text-gray-600 text-sm leading-relaxed">Expand your business reach, tap into new markets, and unlock fresh opportunities.</p>
          </div>
          <div className="bg-[#f3f4f6] p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center text-center">
            <Users className="h-12 w-12 text-[#028475] mb-4" />
            <h3 className="text-xl font-semibold text-[#004235] mb-2">A Collaborative Environment</h3>
            <p className="text-gray-600 text-sm leading-relaxed">Work with an innovative team to co-create value and shape the future of trade.</p>
          </div>
        </div>
      </div>
    </section>
  );
}