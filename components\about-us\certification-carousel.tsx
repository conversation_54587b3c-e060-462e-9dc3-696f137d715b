"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface Certification {
  src: string
  alt: string
}

interface CertificationCarouselProps {
  certifications: Certification[]
  autoPlay?: boolean
  autoPlayInterval?: number
}

export function CertificationCarousel({ 
  certifications, 
  autoPlay = true, 
  autoPlayInterval = 3000 
}: CertificationCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isHovered, setIsHovered] = useState(false)

  // Auto-play functionality
  useEffect(() => {
    if (!autoPlay || isHovered) return

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === certifications.length - 1 ? 0 : prevIndex + 1
      )
    }, autoPlayInterval)

    return () => clearInterval(interval)
  }, [autoPlay, autoPlayInterval, isHovered, certifications.length])

  const goToPrevious = () => {
    const isFirstSlide = currentIndex === 0
    const newIndex = isFirstSlide ? certifications.length - 1 : currentIndex - 1
    setCurrentIndex(newIndex)
  }

  const goToNext = () => {
    const isLastSlide = currentIndex === certifications.length - 1
    const newIndex = isLastSlide ? 0 : currentIndex + 1
    setCurrentIndex(newIndex)
  }

  const goToSlide = (slideIndex: number) => {
    setCurrentIndex(slideIndex)
  }

  // Calculate visible slides (show 3 on desktop, 2 on tablet, 1 on mobile)
  const getVisibleSlides = () => {
    if (typeof window !== 'undefined') {
      if (window.innerWidth >= 1024) return 6 // lg breakpoint
      if (window.innerWidth >= 768) return 4  // md breakpoint
      if (window.innerWidth >= 640) return 3  // sm breakpoint
      return 2 // mobile
    }
    return 6 // default for SSR
  }

  const [visibleSlides, setVisibleSlides] = useState(6)

  useEffect(() => {
    const handleResize = () => {
      setVisibleSlides(getVisibleSlides())
    }

    handleResize() // Set initial value
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const maxIndex = Math.max(0, certifications.length - visibleSlides)
  const adjustedCurrentIndex = Math.min(currentIndex, maxIndex)

  return (
    <div 
      className="relative w-full"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="overflow-hidden">
        <div 
          className="flex transition-transform duration-500 ease-in-out"
          style={{
            transform: `translateX(-${adjustedCurrentIndex * (100 / visibleSlides)}%)`
          }}
        >
          {certifications.map((cert, index) => (
            <div 
              key={index}
              className="flex-shrink-0 px-3 group" // Added group class
              style={{ width: `${100 / visibleSlides}%` }}
            >
              <div className="rounded-3xl p-6 flex items-center justify-center aspect-square hover:bg-gray-100 transition-colors duration-300">
                <Image
                  src={cert.src}
                  alt={cert.alt}
                  width={150}
                  height={150}
                  className="object-contain max-w-full max-h-full filter grayscale group-hover:filter-none transition-all duration-300" // Added filter classes
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Arrows */}
      {certifications.length > visibleSlides && (
        <>
          <button
            onClick={goToPrevious}
            className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 bg-[#028475] rounded-full p-3 text-white shadow-lg hover:bg-[#004235] transition-colors duration-300 z-10"
            aria-label="Previous certifications"
            disabled={adjustedCurrentIndex === 0}
          >
            <ChevronLeft className="h-6 w-6" />
          </button>

          <button
            onClick={goToNext}
            className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 bg-[#028475] rounded-full p-3 text-white shadow-lg hover:bg-[#004235] transition-colors duration-300 z-10"
            aria-label="Next certifications"
            disabled={adjustedCurrentIndex >= maxIndex}
          >
            <ChevronRight className="h-6 w-6" />
          </button>
        </>
      )}

      {/* Dot Indicators */}
      {certifications.length > visibleSlides && (
        <div className="flex justify-center mt-8 gap-2">
          {Array.from({ length: maxIndex + 1 }).map((_, slideIndex) => (
            <div
              key={slideIndex}
              onClick={() => goToSlide(slideIndex)}
              className={`w-3 h-3 rounded-full cursor-pointer transition-all duration-300 ${
                slideIndex === adjustedCurrentIndex 
                  ? "bg-[#028475] w-6" 
                  : "bg-gray-300 hover:bg-gray-400"
              }`}
            ></div>
          ))}
        </div>
      )}
    </div>
  )
}