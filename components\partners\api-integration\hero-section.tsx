import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export function HeroSection() {
  return (
    <section className="relative bg-[#F2F2F2] py-16 md:py-24 overflow-hidden">
      <div className="absolute inset-0 z-0">
        <Image
          src="/images/partner/api-integration/data integrations.webp"
          alt="API Integration Background"
          width={1920}
          height={1080}
          className="w-full h-full object-cover opacity-15"
          priority
        />
      </div>
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <div className="w-20 h-1 bg-[#028475] mx-auto mb-6"></div>
          <h1 className="text-3xl md:text-5xl font-bold text-[#004235] mb-6 leading-tight">
            Power the Future of Industrial Trade Intelligence: Integrate with StreamLnk
          </h1>

          <p className="text-lg md:text-xl text-gray-700 mb-8 leading-relaxed">
            Collaborate with StreamLnk through robust API and data integrations. Enhance real-time pricing, elevate
            freight visibility, refine credit risk management, and contribute to global analytics, shaping a more
            connected and intelligent industrial ecosystem.
          </p>

          <div className="flex flex-wrap justify-center gap-4">
            <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white px-8 py-3 text-base md:px-8 md:py-6 md:text-lg rounded-md" asChild>
              <Link href="#get-started">Apply as a Partner</Link>
            </Button>
            <Button variant="outline" size="lg" className="border-[#004235] text-[#004235] hover:bg-[#004235]/10 px-8 py-3 text-base md:px-8 md:py-6 md:text-lg rounded-md" asChild>
              <Link href="#partnerships">Explore Partnership Types</Link>
            </Button>
          </div>
        </div>
      </div>
      <div className="absolute bottom-0 left-0 w-full h-16 bg-gradient-to-t from-white to-transparent"></div>
    </section>
  )
}
