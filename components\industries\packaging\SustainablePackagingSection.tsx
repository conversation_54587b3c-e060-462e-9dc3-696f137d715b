"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, Recycle } from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function SustainablePackagingSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <Leaf className="h-16 w-16 text-[#028475] mx-auto mb-6" />
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Powering the Circular Economy & Sustainable Packaging Choices
          </h2>
          <p className="text-xl text-[#028475] mb-8">
            Focus on Sustainable Packaging Solutions
          </p>
          <p className="text-lg text-gray-700 mb-10">
            StreamLnk is committed to supporting the shift towards more sustainable packaging by:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-left">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <h3 className="font-semibold text-[#004235] text-lg mb-2">Promoting Recycled & Bio-Based Materials</h3>
              <p className="text-gray-600">Our platform makes it easier to find and source certified recycled polymers (rPET, rPE, rPP) and innovative bio-plastics.</p>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <h3 className="font-semibold text-[#004235] text-lg mb-2">Enhancing Transparency</h3>
              <p className="text-gray-600">Providing visibility into material provenance and sustainability certifications.</p>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <h3 className="font-semibold text-[#004235] text-lg mb-2">Facilitating a Market for Recyclates</h3>
              <p className="text-gray-600">Connecting suppliers of post-consumer resin (PCR) and post-industrial resin (PIR) with buyers.</p>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <h3 className="font-semibold text-[#004235] text-lg mb-2">ESG Tracking</h3>
              <p className="text-gray-600">Helping brands track the recycled content and carbon footprint of their packaging supply chain.</p>
            </div>
          </div>
          <Button
            variant="outline"
            className="mt-10 border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-6"
            size="lg"
            asChild
          >
            <Link href="/solutions/sustainable-materials"> {/* Assuming this link exists or will be created */}
              DISCOVER SUSTAINABLE SOLUTIONS
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}