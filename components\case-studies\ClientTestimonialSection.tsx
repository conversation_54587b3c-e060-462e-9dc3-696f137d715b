"use client";

import Image from "next/image";

interface ClientTestimonialProps {
  quote: string;
  author: string;
  title: string;
  companyName?: string; // Optional, for alt text if logo is used
  companyLogoUrl?: string;
}

export default function ClientTestimonialSection({
  quote,
  author,
  title,
  companyName,
  companyLogoUrl,
}: ClientTestimonialProps) {
  return (
    <section className="my-12 md:my-16">
      <div className="bg-gradient-to-r from-[#004235] to-[#028475] p-8 md:p-12 rounded-lg shadow-xl text-white">
        {companyLogoUrl && companyLogoUrl !== '/images/placeholder-image.svg' && (
          <div className="flex justify-center mb-6">
            <Image 
              src={companyLogoUrl} 
              alt={`${companyName || 'Client'} logo`} 
              width={160} 
              height={55} 
              className="object-contain filter brightness-0 invert" 
            />
          </div>
        )}
        <blockquote className="text-xl md:text-2xl italic font-light text-center mb-6 leading-relaxed relative">
          <span className="absolute -top-4 -left-4 text-5xl md:text-6xl opacity-30 font-serif">“</span>
          {quote}
          <span className="absolute -bottom-4 -right-4 text-5xl md:text-6xl opacity-30 font-serif">”</span>
        </blockquote>
        <p className="text-md font-semibold text-center tracking-wide">{author}</p>
        <p className="text-sm text-gray-300 text-center">{title}</p>
      </div>
    </section>
  );
}