import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";
import HeroSection from "@/components/finance-payments/wire-payment/hero-section";
import PaymentMethodsSection from "@/components/finance-payments/wire-payment/payment-methods-section";
import HowItWorksSection from "@/components/finance-payments/wire-payment/how-it-works-section";
import IdealForSection from "@/components/finance-payments/wire-payment/ideal-for-section";
import DashboardSection from "@/components/finance-payments/wire-payment/dashboard-section";
import CtaSection from "@/components/finance-payments/wire-payment/cta-section";

export default function WirePaymentPage() {
  return (
    <main className="flex flex-col min-h-screen\">
      <MainNav />
      <HeroSection />
      <PaymentMethodsSection />
      <HowItWorksSection />
      <IdealForSection />
      <DashboardSection />
      <CtaSection />
      <MainFooter />
    </main>
  );
}
