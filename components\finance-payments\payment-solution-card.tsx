import Link from "next/link";
import { ArrowRight } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface PaymentSolutionCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
}

export default function PaymentSolutionCard({ title, description, icon, href }: PaymentSolutionCardProps) {
  return (
    <Card className="h-full transition-all hover:shadow-lg hover:border-[#028475]">
      <CardHeader>
        <div className="mb-2">{icon}</div>
        <CardTitle className="text-xl text-[#004235]">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <CardDescription className="text-gray-600 mb-4">{description}</CardDescription>
        <Link href={href} className="inline-flex items-center text-[#028475] hover:text-[#004235] font-medium">
          Learn more <ArrowRight className="ml-1 h-4 w-4" />
        </Link>
      </CardContent>
    </Card>
  );
}