import { Bar<PERSON>hart3, BrainCircuit, Users, Globe, Target } from "lucide-react";

const approachFeatures = [
  {
    icon: <BarChart3 className="h-8 w-8 text-[#028475]" />,
    title: "Proprietary Ecosystem Data",
    description: "Insights derived from thousands of real-time transactions, quotes, and logistics movements across our platform."
  },
  {
    icon: <BrainCircuit className="h-8 w-8 text-[#028475]" />,
    title: "Advanced AI & ML Modeling",
    description: "Used for trend identification, forecasting, and risk assessment."
  },
  {
    icon: <Users className="h-8 w-8 text-[#028475]" />,
    title: "Expert Analyst Team",
    description: "Our team of industry specialists and data scientists interpret the data and provide contextual insights."
  },
  {
    icon: <Globe className="h-8 w-8 text-[#028475]" />,
    title: "Global Coverage",
    description: "Data aggregated from diverse international markets."
  },
  {
    icon: <Target className="h-8 w-8 text-[#028475]" />,
    title: "Focus on Actionability",
    description: "We aim to provide intelligence that directly supports strategic and operational decision-making."
  }
];

export default function AnalyticalApproachSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Grounded in Data, Powered by AI, Delivered by Experts
          </h2>
          <p className="text-lg text-gray-700">
            Our Analytical Approach & Data Sources
          </p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-12 max-w-5xl mx-auto">
          {approachFeatures.map((feature, index) => (
            <div key={index} className="flex flex-col items-center text-center md:items-start md:text-left">
              <div className="bg-[#E6F3F1] p-3 rounded-full mb-4">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">{feature.title}</h3>
              <p className="text-gray-600 text-sm">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}