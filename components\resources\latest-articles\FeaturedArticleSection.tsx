"use client";

import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export default function FeaturedArticleSection() {
  return (
    <section className="py-12 md:py-16 bg-white">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center md:text-left">
          Featured Article
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12 items-center bg-[#f3f4f6] p-6 md:p-8 rounded-lg shadow-md">
          <div className="relative w-full h-64 md:h-96 rounded-md overflow-hidden">
            <Image
              src="/images/placeholder-large.jpg" // Placeholder image
              alt="Featured Article Image"
              fill
              className="object-cover"
            />
          </div>
          <div>
            <span className="inline-block bg-[#028475] text-white text-xs font-semibold px-2 py-1 rounded-full mb-3">
              Market Trends
            </span>
            <h3 className="text-2xl md:text-3xl font-semibold text-[#004235] mb-3">
              The Future of AI in Polymer Procurement: Predictions for 2025
            </h3>
            <p className="text-gray-700 mb-4 text-sm md:text-base">
              Discover how artificial intelligence is set to revolutionize the polymer procurement landscape, offering unprecedented efficiencies and insights. Our experts predict key changes by 2025.
            </p>
            <p className="text-xs text-gray-500 mb-6">
              By Dr. Alex Chen | October 26, 2023
            </p>
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white"
              size="lg"
              asChild
            >
              <Link href="/resources/latest-articles/slug-to-featured-article"> {/* Placeholder Link */}
                READ FULL ARTICLE
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}