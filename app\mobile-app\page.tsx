import type { Metada<PERSON> } from "next";
import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";
import HeroSection from "@/components/mobile-app/hero-section";
import WhyMobileSection from "@/components/mobile-app/why-mobile-section";
import KeyFeaturesSection from "@/components/mobile-app/key-features-section";
import BenefitsSection from "@/components/mobile-app/benefits-section";
import ComingSoonSection from "@/components/mobile-app/coming-soon-section";
import MobileWebAccessSection from "@/components/mobile-app/mobile-web-access-section";

export const metadata: Metadata = {
  title: "StreamLnk Mobile App | Your Global Trade Command Center",
  description:
    "Stay connected and in control of your industrial sourcing, sales, and logistics with the StreamLnk mobile app. Coming soon to iOS and Android!",
};

export default function MobileAppPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <MainNav />
      <main>
        <HeroSection />
        <WhyMobileSection />
        <KeyFeaturesSection />
        <BenefitsSection />
        <ComingSoonSection />
        <MobileWebAccessSection />
      </main>
      <MainFooter />
    </div>
  );
}