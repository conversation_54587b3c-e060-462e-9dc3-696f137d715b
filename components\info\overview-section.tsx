import { Users, Network, DollarSign, ShieldCheck, DatabaseZap, Cpu } from "lucide-react";

const corePillars = [
  {
    icon: <Users className="h-8 w-8 text-[#028475]" />,
    title: "Centralized Marketplace",
    description: "Connecting verified global buyers and suppliers for efficient product discovery, quoting (including AI-assisted pricing), and auctions."
  },
  {
    icon: <Network className="h-8 w-8 text-[#028475]" />,
    title: "Integrated Logistics Network",
    description: "Seamless coordination of multi-modal freight (land, sea), customs clearance, packaging, and warehousing via specialized, interconnected portals."
  },
  {
    icon: <DollarSign className="h-8 w-8 text-[#028475]" />,
    title: "Embedded Fintech Solutions",
    description: "Secure multi-currency payments, B2B Buy Now Pay Later (BNPL), Escrow services, and automated AR/AP management."
  },
  {
    icon: <ShieldCheck className="h-8 w-8 text-[#028475]" />,
    title: "Robust Compliance & Risk Management",
    description: "KYC/AML verification for all users, automated document tracking, and our proprietary iScore™ partner rating system."
  },
  {
    icon: <DatabaseZap className="h-8 w-8 text-[#028475]" />,
    title: "Data Intelligence Engine (StreamResources+)",
    description: "Leveraging platform-wide data to provide proprietary market benchmarks (StreamIndex™), predictive analytics, and actionable insights for strategic decision-making."
  },
  {
    icon: <Cpu className="h-8 w-8 text-[#028475]" />,
    title: "AI-Driven Automation",
    description: "Artificial intelligence and machine learning optimize processes, enhance matching, power predictive features, and improve efficiency across the platform."
  },
];

export default function OverviewSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">What is StreamLnk? A Platform Overview</h2>
          <p className="text-lg text-gray-700">
            One Ecosystem, End-to-End Solutions
          </p>
        </div>
        <p className="text-lg text-gray-700 mb-12 max-w-4xl mx-auto">
          StreamLnk is a next-generation, AI-powered B2B digital platform built to revolutionize how industrial raw
          materials (starting with polymers and expanding to chemicals, energy, and more) are sourced, sold,
          financed, and transported globally. We replace fragmented, manual processes with a single, integrated, and
          transparent ecosystem.
        </p>
        <h3 className="text-2xl font-semibold text-[#004235] mb-8 text-center">Core Pillars of the StreamLnk Platform:</h3>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {corePillars.map(pillar => (
            <div key={pillar.title} className="bg-gray-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow flex flex-col items-center text-center">
              <div className="flex justify-center mb-4">{pillar.icon}</div>
              <h4 className="text-xl font-semibold text-[#004235] mb-2">{pillar.title}</h4>
              <p className="text-gray-600 text-sm">{pillar.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}