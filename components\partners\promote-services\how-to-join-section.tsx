import { Card, CardContent } from "@/components/ui/card"

export function HowToJoinSection() {
  const steps = [
    {
      number: "01",
      title: "Apply Online",
      description:
        "Complete our service provider application form, detailing your company information and compliance documentation.",
    },
    {
      number: "02",
      title: "Define Your Services",
      description:
        "Clearly select your service categories, geographic focus, and any industry verticals you specialize in.",
    },
    {
      number: "03",
      title: "Upload Supporting Documents",
      description:
        "Submit your brochures, relevant SLAs, insurance certificates, licenses, and any quality certifications.",
    },
    {
      number: "04",
      title: "Verification & Approval",
      description:
        "Our dedicated operations and compliance team will review your application and supporting documents.",
    },
    {
      number: "05",
      title: "Go Live & Get Noticed",
      description:
        "Once approved, your enhanced service profile goes live, and you begin receiving workflow integration opportunities and quote notifications.",
    },
  ]

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            How to Join the StreamLnk Service Network & Promote Your Business:
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-12">
            {steps.map((step, index) => (
              <Card key={index} className="border-none shadow-md hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex items-start mb-4">
                    <div className="text-4xl font-bold text-[#028475] mr-4 leading-none">{step.number}</div>
                    <div>
                      <h3 className="text-xl font-semibold text-[#004235] mb-2">{step.title}</h3>
                      <p className="text-gray-600">{step.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
