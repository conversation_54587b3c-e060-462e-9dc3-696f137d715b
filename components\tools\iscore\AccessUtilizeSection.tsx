"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>R<PERSON>, LogIn, Settings, ShoppingCart, Eye } from "lucide-react";
import Link from "next/link";

export default function AccessUtilizeSection() {
  return (
    <section className="py-16 bg-white" id="access"> {/* Added id for internal linking from Hero */}
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Gaining Your Partner Intelligence Edge
          </h2>
          <p className="text-xl text-gray-700 mb-10 text-center">
            How to Access and Utilize the iScore™ Tool
          </p>

          <div className="space-y-8">
            {/* Basic iScore™ Badges */}
            <div className="bg-[#F2F2F2] p-8 rounded-lg shadow-sm">
              <div className="flex items-center mb-3">
                <Eye className="text-[#028475] h-8 w-8 mr-3 flex-shrink-0" />
                <h3 className="text-2xl font-semibold text-[#004235]">Basic iScore™ Visibility</h3>
              </div>
              <p className="text-gray-700 mb-4">
                Simplified iScore™ indicators are visible on supplier/partner profiles within MyStreamLnk, E-Stream, and other operational portals to provide initial trust signals. All users can see their own detailed iScore™ breakdown and contributing factors.
              </p>
            </div>

            {/* Full iScore™ Tool Access */}
            <div className="bg-[#F2F2F2] p-8 rounded-lg shadow-sm">
              <div className="flex items-center mb-3">
                <LogIn className="text-[#028475] h-8 w-8 mr-3 flex-shrink-0" />
                <h3 className="text-2xl font-semibold text-[#004235]">Full iScore™ Tool Access (Premium)</h3>
              </div>
              <p className="text-gray-700 mb-4">
                Comprehensive search, profile views, and detailed report downloads are premium features available to subscribers of StreamResources+ (Professional Analyst and Enterprise Intelligence tiers).
              </p>
              <ol className="list-decimal list-inside text-gray-700 space-y-2 mb-6 pl-5">
                <li><strong>Subscribe:</strong> Choose a StreamResources+ plan that includes the desired level of iScore™ access.</li>
                <li><strong>Login:</strong> Access the iScore™ module within your StreamResources+ portal.</li>
                <li><strong>Search & Analyze:</strong> Look up partners, review their profiles, and download reports.</li>
              </ol>
              <Button
                className="bg-[#004235] hover:bg-[#028475] text-white"
                asChild
              >
                <Link href="/solutions/streamresources#subscription-plans"> {/* Link to subscription plans */}
                  EXPLORE STREAMRESOURCES+ TIERS
                  <ShoppingCart className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>

            {/* Integrate (Enterprise Tier) */}
            <div className="bg-[#F2F2F2] p-8 rounded-lg shadow-sm">
              <div className="flex items-center mb-3">
                <Settings className="text-[#028475] h-8 w-8 mr-3 flex-shrink-0" />
                <h3 className="text-2xl font-semibold text-[#004235]">Integrate (Enterprise Tier)</h3>
              </div>
              <p className="text-gray-700 mb-4">
                Use the DaaS API to pull iScore™ data into your own risk management or procurement systems for seamless integration and automated workflows.
              </p>
              <Button
                variant="outline"
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
                asChild
              >
                <Link href="/contact-us?interest=iscore-api"> {/* Link to contact for API */}
                  LEARN ABOUT API ACCESS
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}