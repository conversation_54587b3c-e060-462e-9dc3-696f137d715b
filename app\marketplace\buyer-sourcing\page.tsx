"use client";

import Link from "next/link";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/marketplace/buyer-sourcing/HeroSection";
import TraditionalPainsSection from "@/components/marketplace/buyer-sourcing/TraditionalPainsSection";
import SolutionSection from "@/components/marketplace/buyer-sourcing/SolutionSection";
import JourneySection from "@/components/marketplace/buyer-sourcing/JourneySection";
import BenefitsSection from "@/components/marketplace/buyer-sourcing/BenefitsSection";
import CTASection from "@/components/marketplace/buyer-sourcing/CTASection";

export default function BuyerSourcingPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />
      <HeroSection />
      <TraditionalPainsSection />
      <SolutionSection />
      <JourneySection />
      <BenefitsSection />
      <CTASection />
      <BottomFooter />
    </div>
  );
}