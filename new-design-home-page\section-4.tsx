import { ChevronLeft, ChevronRight, Calendar, FileText, BookOpen } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"

export default function Component() {
  return (
    <div className="min-h-screen bg-[#ffffff]">
      {/* Header Section */}
      <section className="px-6 py-16 max-w-7xl mx-auto">
        <div className="flex justify-between items-start">
          <div className="max-w-2xl">
            <h1 className="text-4xl font-bold text-[#000000] mb-4">Any Industry, Anywhere</h1>
            <p className="text-[#404040] text-lg">
              Achieve compliance, reduce fraud and build customer trust across all industries around the world.
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="icon" className="rounded-full">
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="rounded-full">
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </section>

      {/* Service Cards Section */}
      <section className="px-6 pb-16 max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Banking Card */}
          <Card className="bg-[#eaf6f9] border-0 p-6">
            <CardContent className="p-0">
              <div className="mb-6">
                <div className="grid grid-cols-4 gap-1 w-12 h-12">
                  {Array.from({ length: 16 }).map((_, i) => (
                    <div key={i} className="w-2 h-2 bg-[#000000] rounded-full"></div>
                  ))}
                </div>
              </div>
              <h3 className="text-xl font-semibold text-[#000000] mb-4">Banking</h3>
              <p className="text-[#404040] text-sm mb-6">
                Keep pace and remain compliant with evolving KYC and AML regulations.
              </p>
              <button className="text-[#000000] text-sm font-medium flex items-center gap-2">
                <ChevronRight className="h-4 w-4" />
                Explore bank account verification
              </button>
            </CardContent>
          </Card>

          {/* Payment Service Providers Card */}
          <Card className="bg-[#eaf6f9] border-0 p-6">
            <CardContent className="p-0">
              <div className="mb-6">
                <div className="grid grid-cols-4 gap-1 w-12 h-12">
                  {Array.from({ length: 16 }).map((_, i) => (
                    <div key={i} className="w-2 h-2 bg-[#000000] rounded-full"></div>
                  ))}
                </div>
              </div>
              <h3 className="text-xl font-semibold text-[#000000] mb-4">Payment Service Providers</h3>
              <p className="text-[#404040] text-sm mb-6">
                Deliver comprehensive identity verification that protects your payments business and customers.
              </p>
              <button className="text-[#000000] text-sm font-medium flex items-center gap-2">
                <ChevronRight className="h-4 w-4" />
                Explore payments verification
              </button>
            </CardContent>
          </Card>

          {/* Marketplaces Card */}
          <Card className="bg-[#eaf6f9] border-0 p-6">
            <CardContent className="p-0">
              <div className="mb-6">
                <div className="grid grid-cols-4 gap-1 w-12 h-12">
                  {Array.from({ length: 16 }).map((_, i) => (
                    <div key={i} className="w-2 h-2 bg-[#000000] rounded-full"></div>
                  ))}
                </div>
              </div>
              <h3 className="text-xl font-semibold text-[#000000] mb-4">Marketplaces</h3>
              <p className="text-[#404040] text-sm mb-6">
                Create a trusted ecosystem with verified vendors and customers to gain a competitive advantage.
              </p>
              <button className="text-[#000000] text-sm font-medium flex items-center gap-2">
                <ChevronRight className="h-4 w-4" />
                Explore marketplace verification
              </button>
            </CardContent>
          </Card>

          {/* Online Trading Card */}
          <Card className="bg-[#eaf6f9] border-0 p-6">
            <CardContent className="p-0">
              <div className="mb-6">
                <div className="grid grid-cols-4 gap-1 w-12 h-12">
                  {Array.from({ length: 16 }).map((_, i) => (
                    <div key={i} className="w-2 h-2 bg-[#000000] rounded-full"></div>
                  ))}
                </div>
              </div>
              <h3 className="text-xl font-semibold text-[#000000] mb-4">Online Trading</h3>
              <p className="text-[#404040] text-sm mb-6">
                Provide a fast, smooth customer experience while meeting extensive compliance obligations worldwide.
              </p>
              <button className="text-[#000000] text-sm font-medium flex items-center gap-2">
                <ChevronRight className="h-4 w-4" />
                Explore online trading verification
              </button>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Testimonial/Slider Section */}
      <section className="bg-[#004c45] py-24">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <div className="mb-12">
            <div className="text-6xl text-[#a4dcb4] mb-8">"</div>
            <h2 className="text-4xl font-bold text-[#ffffff]">Slider Here</h2>
          </div>
          <div className="flex justify-center items-center gap-4">
            <div className="flex gap-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className={`w-2 h-2 rounded-full ${i === 2 ? "bg-[#ffffff]" : "bg-[#a4dcb4]"}`}></div>
              ))}
            </div>
            <div className="flex gap-2 ml-8">
              <Button
                variant="outline"
                size="icon"
                className="rounded-full border-[#a4dcb4] text-[#a4dcb4] hover:bg-[#a4dcb4] hover:text-[#004c45]"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="rounded-full border-[#a4dcb4] text-[#a4dcb4] hover:bg-[#a4dcb4] hover:text-[#004c45]"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Insights Section */}
      <section className="px-6 py-16 max-w-7xl mx-auto">
        <h2 className="text-3xl font-bold text-[#000000] mb-12">Insights to Grow Your Business</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Individual Verification Card */}
          <Card className="border-0 shadow-sm">
            <div className="aspect-video bg-[#004c45] relative overflow-hidden">
              <Image
                src="/placeholder.svg?height=200&width=300"
                alt="Individual Verification illustration"
                width={300}
                height={200}
                className="object-cover w-full h-full"
              />
            </div>
            <CardContent className="p-6">
              <div className="text-sm text-[#404040] mb-2">• Individual Verification (KYC)</div>
              <div className="text-sm text-[#a4dcb4] mb-4">White Paper</div>
              <h3 className="text-xl font-semibold text-[#000000]">
                Strengthen Your Global Enterprise With Digital Identity Verification
              </h3>
            </CardContent>
          </Card>

          {/* Identity Platform Card */}
          <Card className="border-0 shadow-sm">
            <div className="aspect-video relative overflow-hidden">
              <Image
                src="/placeholder.svg?height=200&width=300"
                alt="Identity Platform"
                width={300}
                height={200}
                className="object-cover w-full h-full"
              />
            </div>
            <CardContent className="p-6">
              <div className="text-sm text-[#404040] mb-2">• Identity Platform</div>
              <div className="text-sm text-[#a4dcb4] mb-4">White Paper</div>
              <h3 className="text-xl font-semibold text-[#000000]">
                Finding Value in the Complex Identity Verification Market
              </h3>
            </CardContent>
          </Card>

          {/* Trulioo Card */}
          <Card className="border-0 shadow-sm">
            <div className="aspect-video relative overflow-hidden">
              <Image
                src="/placeholder.svg?height=200&width=300"
                alt="Trulioo office workspace"
                width={300}
                height={200}
                className="object-cover w-full h-full"
              />
            </div>
            <CardContent className="p-6">
              <div className="text-sm text-[#404040] mb-2">• Trulioo</div>
              <div className="text-sm text-[#a4dcb4] mb-4">White Paper</div>
              <h3 className="text-xl font-semibold text-[#000000]">The World's Identity Platform</h3>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Footer Section */}
      <section className="px-6 py-16 max-w-7xl mx-auto border-t border-[#f5f5f0]">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          {/* Upcoming Events */}
          <div>
            <div className="flex items-center gap-3 mb-4">
              <Calendar className="h-6 w-6 text-[#404040]" />
              <h3 className="text-xl font-semibold text-[#000000]">Upcoming Events</h3>
            </div>
            <p className="text-[#404040] text-sm mb-4">Stay up to date with trade shows and industry events.</p>
            <button className="text-[#000000] text-sm font-medium flex items-center gap-2">
              <ChevronRight className="h-4 w-4" />
              See events
            </button>
          </div>

          {/* Blog */}
          <div>
            <div className="flex items-center gap-3 mb-4">
              <FileText className="h-6 w-6 text-[#404040]" />
              <h3 className="text-xl font-semibold text-[#000000]">Blog</h3>
            </div>
            <p className="text-[#404040] text-sm mb-4">
              Gain important insights with industry updates, best practices and top trends.
            </p>
            <button className="text-[#000000] text-sm font-medium flex items-center gap-2">
              <ChevronRight className="h-4 w-4" />
              See blog
            </button>
          </div>

          {/* Resource Library */}
          <div>
            <div className="flex items-center gap-3 mb-4">
              <BookOpen className="h-6 w-6 text-[#404040]" />
              <h3 className="text-xl font-semibold text-[#000000]">Resource Library</h3>
            </div>
            <p className="text-[#404040] text-sm mb-4">
              Find insights and information to help you verify across the globe.
            </p>
            <button className="text-[#000000] text-sm font-medium flex items-center gap-2">
              <ChevronRight className="h-4 w-4" />
              See resources
            </button>
          </div>
        </div>
      </section>
    </div>
  )
}
