import Image from "next/image"
import { Button } from "@/components/ui/button"

export default function Component() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-16 px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <p className="text-[#f8775f] text-sm font-medium">Verify the World</p>
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                Capitalize on Industry-Leading Verification Match Rates
              </h1>
              <p className="text-gray-600 text-lg leading-relaxed">
                Identity verification is as eclectic as the people you onboard. A strategy for one country or
                demographic can hamstring match rate proficiency elsewhere. Apply, market expertise and continuous data
                optimization are your greatest allies. T<PERSON>lioo delivers them through a full suite of in-house
                verification capabilities.
              </p>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <Image
                src="/placeholder.svg?height=300&width=250"
                alt="Professional meeting"
                width={250}
                height={300}
                className="rounded-lg object-cover"
              />
              <div className="space-y-4">
                <Image
                  src="/placeholder.svg?height=140&width=250"
                  alt="Business discussion"
                  width={250}
                  height={140}
                  className="rounded-lg object-cover"
                />
                <Image
                  src="/placeholder.svg?height=140&width=250"
                  alt="Team collaboration"
                  width={250}
                  height={140}
                  className="rounded-lg object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Global Reach Section */}
      <section className="py-16 px-6 lg:px-8 bg-[#172d2d]">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="flex justify-center">
              <div className="relative">
                <div className="w-64 h-64 rounded-full bg-[#aadce6] flex items-center justify-center">
                  <div className="w-48 h-48 rounded-full bg-[#67c8d6] flex items-center justify-center relative">
                    <div className="absolute top-8 left-8 w-12 h-12 bg-[#539882] rounded-lg transform -rotate-12"></div>
                    <div className="absolute top-12 right-12 w-8 h-8 bg-[#a1dcb2] rounded transform rotate-45"></div>
                  </div>
                </div>
              </div>
            </div>
            <div className="space-y-6">
              <p className="text-[#539882] text-sm font-medium">Drive Growth</p>
              <h2 className="text-4xl lg:text-5xl font-bold text-white leading-tight">Expand Your Global Reach</h2>
              <p className="text-gray-300 text-lg leading-relaxed">
                Embrace new markets with nimble identity and business verification. Open the door to more than 5 billion
                consumers in 195 countries. Different countries present different identification challenges. One
                platform unlocks your access to more than 450 data sources worldwide to turn your global ambition into
                reality.
              </p>
              <Button className="bg-transparent border border-[#539882] text-[#539882] hover:bg-[#539882] hover:text-white px-6 py-2 rounded-full">
                Expand to new markets
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Compliance Section */}
      <section className="py-16 px-6 lg:px-8 bg-[#172d2d]">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <p className="text-[#539882] text-sm font-medium">Ensure Compliance</p>
              <h2 className="text-4xl lg:text-5xl font-bold text-white leading-tight">
                Fortify Your KYC, KYB and AML Compliance Across the Globe
              </h2>
              <p className="text-gray-300 text-lg leading-relaxed">
                Meet FinCEN, FINRA, FCA, FINTRAC, CySEC and other regulatory requirements with a full suite of in-house
                verification capabilities. Stay compliant by mixing and matching state-of-the-art verification tools and
                global and local data sources to achieve the highest onboarding assurance.
              </p>
              <Button className="bg-transparent border border-[#539882] text-[#539882] hover:bg-[#539882] hover:text-white px-6 py-2 rounded-full">
                Achieve compliance
              </Button>
            </div>
            <div className="flex justify-center">
              <div className="relative">
                <div className="w-48 h-64 bg-[#539882] rounded-t-full flex items-end justify-center pb-8">
                  <div className="w-8 h-8 bg-[#172d2d] rounded-full"></div>
                </div>
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 w-24 h-16 bg-white rounded-t-full"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Integration Section */}
      <section className="py-16 px-6 lg:px-8 bg-[#172d2d]">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="flex justify-center">
              <div className="relative space-y-4">
                <div className="flex space-x-4">
                  <div className="w-24 h-16 bg-[#a1dcb2] rounded"></div>
                  <div className="w-24 h-16 bg-[#aadce6] rounded"></div>
                </div>
                <div className="relative">
                  <div className="w-32 h-24 bg-[#f8775f] rounded-lg transform -rotate-12"></div>
                  <div className="absolute top-4 left-8 w-16 h-12 bg-white rounded shadow-lg"></div>
                </div>
                <div className="flex space-x-4">
                  <div className="w-24 h-16 bg-[#aadce6] rounded"></div>
                  <div className="w-24 h-16 bg-[#a1dcb2] rounded"></div>
                </div>
              </div>
            </div>
            <div className="space-y-6">
              <p className="text-[#539882] text-sm font-medium">Integrate and Scale</p>
              <h2 className="text-4xl lg:text-5xl font-bold text-white leading-tight">
                Simplify Verification Worldwide
              </h2>
              <p className="text-gray-300 text-lg leading-relaxed">
                A single API unlocks a comprehensive verification platform. The Trulioo API delivers streamlined,
                scalable integration through a single token and endpoint, giving organizations access to a suite of
                verification services fine-tuned for regions across the globe.
              </p>
              <Button className="bg-transparent border border-[#539882] text-[#539882] hover:bg-[#539882] hover:text-white px-6 py-2 rounded-full">
                Explore the Trulioo identity platform
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Section */}
      <section className="py-16 px-6 lg:px-8 bg-[#172d2d]">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <p className="text-[#539882] text-sm font-medium">Build Trust</p>
              <h2 className="text-4xl lg:text-5xl font-bold text-white leading-tight">
                Minimize Risk, Maximize Confidence
              </h2>
              <p className="text-gray-300 text-lg leading-relaxed">
                Ensure the right balance between identity verification and user experience to create trusted,
                sustainable relationships with your customers. Leverage risk mitigation, fraud intelligence, and trust
                and safety programs to stop bad actors before they can do damage.
              </p>
              <Button className="bg-transparent border border-[#539882] text-[#539882] hover:bg-[#539882] hover:text-white px-6 py-2 rounded-full">
                Find your risk balance
              </Button>
            </div>
            <div className="flex justify-center">
              <div className="relative">
                <div className="flex items-center space-x-8">
                  <div className="w-24 h-32 bg-[#f8775f] rounded-full flex items-end justify-center pb-4">
                    <div className="w-8 h-8 bg-[#172d2d] rounded-full"></div>
                  </div>
                  <div className="w-24 h-32 bg-[#539882] rounded-full flex items-end justify-center pb-4">
                    <div className="w-8 h-8 bg-[#172d2d] rounded-full"></div>
                  </div>
                </div>
                <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-16 h-8 bg-[#a1dcb2] rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
