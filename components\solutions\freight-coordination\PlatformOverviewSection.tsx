"use client";

import { CheckCircle, Users, BarChart3, DollarSign, Globe, FileText, AlertCircle, ShieldCheck } from "lucide-react";

export default function PlatformOverviewSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Your Central Hub for Seamless Multi-Modal Freight Management
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            StreamLnk simplifies and optimizes your freight operations by integrating access to a global network of vetted land and sea carriers through our specialized portals, all orchestrated by our intelligent platform:
          </p>

          <div className="space-y-8">
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Users className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Access to Vetted Carrier Network</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                      <span><span className="font-semibold">StreamFreight (Land Freight):</span> Connect with thousands of qualified trucking companies and rail operators for domestic and cross-border land transport.</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                      <span><span className="font-semibold">StreamGlobe (Sea Freight):</span> Integrate directly (often via API) with major global ocean freight carriers for sea-bound cargo.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">AI-Driven Carrier Selection & Route Optimization</h3>
                  <p className="text-gray-700 mb-4">
                    Our system analyzes your shipment requirements (origin, destination, product type, urgency, cost parameters) and suggests the most efficient and cost-effective carrier(s) and routes.
                  </p>
                  <p className="text-gray-700">
                    Considers multi-modal options for optimal end-to-end solutions.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <DollarSign className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Real-Time Freight Quoting & Booking</h3>
                  <p className="text-gray-700 mb-4">
                    Receive instant or rapidly generated freight quotes from multiple carriers within the platform.
                  </p>
                  <p className="text-gray-700">
                    Book shipments directly through StreamLnk, eliminating manual follow-ups.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Globe className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Centralized Shipment Management</h3>
                  <p className="text-gray-700">
                    Track all your freight movements (land and sea) in one unified dashboard within MyStreamLnk or E-Stream.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <FileText className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Automated Document Handling</h3>
                  <p className="text-gray-700">
                    StreamLnk facilitates the flow of necessary shipping documents (B/L, waybills, manifests) between you, the carriers, and customs agents.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <AlertCircle className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Proactive Alerting & Exception Management</h3>
                  <p className="text-gray-700">
                    Get notified of potential delays, transshipment issues, or other freight-related exceptions.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <ShieldCheck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Performance Analytics for Carriers</h3>
                  <p className="text-gray-700">
                    Our iScore™ system helps rate carrier performance, ensuring you work with reliable partners.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}