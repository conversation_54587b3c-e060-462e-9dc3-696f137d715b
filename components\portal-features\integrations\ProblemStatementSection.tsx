import { RefreshCw, Database, FileText, Layers, Briefcase } from "lucide-react";

export default function ProblemStatementSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Are Data Silos and Manual Transfers Slowing You Down?
          </h2>
          <div className="w-20 h-1 bg-[#028475] mx-auto mb-10"></div>
          <p className="text-lg text-gray-700 mb-10">
            While StreamLnk provides a comprehensive end-to-end solution, many businesses rely on established internal systems for broader enterprise management. Operating these in isolation from your trade platform can lead to:
          </p>

          <div className="space-y-6">
            <div className="flex items-start p-6 bg-[#f3f4f6] rounded-lg">
              <RefreshCw className="h-6 w-6 text-[#028475] mt-1 mr-4 flex-shrink-0" />
              <p className="text-gray-700">
                Manual re-entry of order, shipment, and invoice data between StreamLnk and your ERP, SCM, or accounting software, leading to errors and wasted time.
              </p>
            </div>

            <div className="flex items-start p-6 bg-[#f3f4f6] rounded-lg">
              <Database className="h-6 w-6 text-[#028475] mt-1 mr-4 flex-shrink-0" />
              <p className="text-gray-700">
                Inconsistent data across different systems, making accurate reporting and analysis difficult.
              </p>
            </div>

            <div className="flex items-start p-6 bg-[#f3f4f6] rounded-lg">
              <FileText className="h-6 w-6 text-[#028475] mt-1 mr-4 flex-shrink-0" />
              <p className="text-gray-700">
                Delayed updates to inventory, financial records, or customer information.
              </p>
            </div>

            <div className="flex items-start p-6 bg-[#f3f4f6] rounded-lg">
              <Layers className="h-6 w-6 text-[#028475] mt-1 mr-4 flex-shrink-0" />
              <p className="text-gray-700">
                Lack of a unified view of your overall business operations.
              </p>
            </div>

            <div className="flex items-start p-6 bg-[#f3f4f6] rounded-lg">
              <Briefcase className="h-6 w-6 text-[#028475] mt-1 mr-4 flex-shrink-0" />
              <p className="text-gray-700">
                Inefficient workflows requiring staff to switch between multiple applications.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}