import { Users, MapPin, <PERSON>ert<PERSON>riangle, Clock, CheckCircle } from "lucide-react";

export default function WhyMobileSection() {
  const reasons = [
    {
      icon: <MapPin className="h-8 w-8 text-[#028475]" />,
      title: "On the Move",
      description:
        "Sales agents visiting clients, procurement managers inspecting facilities, logistics coordinators at ports or warehouses.",
    },
    {
      icon: <AlertTriangle className="h-8 w-8 text-[#028475]" />,
      title: "Needing Real-Time Updates",
      description: "Tracking urgent shipments, responding to critical alerts, or monitoring market changes.",
    },
    {
      icon: <CheckCircle className="h-8 w-8 text-[#028475]" />,
      title: "Requiring Field Functionality",
      description:
        "Drivers needing to upload Proof of Delivery, or warehouse staff performing quick inventory checks.",
    },
    {
      icon: <Clock className="h-8 w-8 text-[#028475]" />,
      title: "Seeking Convenience",
      description: "Managing key tasks without being tied to a desktop computer.",
    },
  ];

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            The Speed of Business Demands Mobile Access
          </h2>
          <p className="text-lg text-gray-700">
            Why a Mobile App for Industrial Trade? In today's fast-paced global environment, decisions need to be made
            quickly, and information must be accessible instantly. The StreamLnk mobile app is designed to empower users
            who are:
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
          {reasons.map((reason, index) => (
            <div key={index} className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 flex flex-col items-center text-center">
              <div className="rounded-full bg-[#028475]/10 w-16 h-16 flex items-center justify-center mb-4">
                {reason.icon}
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">{reason.title}</h3>
              <p className="text-gray-600 text-sm">{reason.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}