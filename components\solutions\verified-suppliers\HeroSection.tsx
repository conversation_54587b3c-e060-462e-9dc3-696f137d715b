"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"

export function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl">
          <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
            Your Trusted Gateway to a Global Network of Verified Suppliers
          </h1>
          <p className="text-xl md:text-2xl text-[#028475] mb-8">
            Stop wasting time vetting unknown vendors. StreamLnk connects you directly to pre-qualified industrial material suppliers, rigorously assessed for quality, compliance, and reliability.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/solutions/verified-suppliers/categories">
                Browse Supplier Categories
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button 
              variant="outline" 
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                Request a Demo to See How
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}