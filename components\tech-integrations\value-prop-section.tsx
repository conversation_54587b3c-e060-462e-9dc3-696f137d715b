import { AlertTriangle } from "lucide-react";

export function ValuePropSection() {
  const painPoints = [
    "Data silos and inconsistent information across platforms.",
    "Manual data re-entry, leading to errors and inefficiencies.",
    "Fragmented workflows requiring users to switch between multiple applications.",
    "Missed opportunities for automation and intelligent decision-making.",
    "Difficulty in achieving a true end-to-end view of operations.",
  ];

  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            The Value of an Integrated Tech Stack in B2B Trade
          </h2>
          <p className="text-xl text-[#028475] mb-6">
            Why Seamless Technology Integration is Key to Modern Industrial Operations
          </p>
          <p className="text-gray-700 text-lg">
            Businesses today rely on a multitude of specialized software solutions. When these systems don't communicate, it creates:
          </p>
        </div>

        <div className="max-w-2xl mx-auto space-y-6">
          {painPoints.map((point, index) => (
            <div key={index} className="flex items-start p-4 bg-red-50 border-l-4 border-red-400 rounded-md shadow">
              <AlertTriangle className="h-6 w-6 text-red-500 mr-4 flex-shrink-0 mt-1" />
              <p className="text-gray-700">{point}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}