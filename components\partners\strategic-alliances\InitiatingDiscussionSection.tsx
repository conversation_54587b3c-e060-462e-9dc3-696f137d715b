"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowRight, Mail, MessageSquare, FileText, Users, CheckSquare, Rocket } from 'lucide-react'; // Example icons

const processSteps = [
  { 
    icon: <FileText className="h-6 w-6 text-[#028475] mr-3 flex-shrink-0" />,
    text: "Initial Contact & NDA."
  },
  {
    icon: <MessageSquare className="h-6 w-6 text-[#028475] mr-3 flex-shrink-0" />,
    text: "Exploratory Discussions to identify mutual strategic fit and potential areas of collaboration."
  },
  {
    icon: <Users className="h-6 w-6 text-[#028475] mr-3 flex-shrink-0" />,
    text: "Joint Development of a Partnership Framework & Objectives."
  },
  {
    icon: <CheckSquare className="h-6 w-6 text-[#028475] mr-3 flex-shrink-0" />,
    text: "Formal Agreement & Launch."
  }
];

export default function InitiatingDiscussionSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <Rocket className="h-16 w-16 text-[#028475] mx-auto mb-6" /> 
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Let's Explore How We Can Reshape Industrial Trade, Together.
          </h2>
          <p className="text-xl text-gray-700 mb-10 leading-relaxed">
            If your organization is interested in exploring a high-impact strategic alliance with StreamLnk, we invite you to connect with our corporate development team. We are eager to discuss how our combined strengths can create unparalleled value in the global marketplace.
          </p>

          <div className="bg-white p-8 md:p-10 rounded-xl shadow-xl text-left space-y-8">
            <div>
              <h3 className="text-2xl font-semibold text-[#004235] mb-5 text-center md:text-left">
                Initiating a Strategic Alliance Discussion
              </h3>
              
              <h4 className="text-lg font-semibold text-[#004235] mb-3">Process:</h4>
              <ul className="space-y-3 mb-6">
                {processSteps.map((step, index) => (
                  <li key={index} className="flex items-start">
                    {step.icon}
                    <span className="text-gray-700 leading-relaxed">{step.text}</span>
                  </li>
                ))}
              </ul>

              <h4 className="text-lg font-semibold text-[#004235] mb-3">Contact:</h4>
              <div className="space-y-2 mb-8">
                <p className="flex items-center text-gray-700">
                  <Mail className="h-5 w-5 text-[#028475] mr-2 flex-shrink-0" />
                  Email: <a href="mailto:<EMAIL>" className="text-[#028475] hover:underline ml-1"><EMAIL></a>
                </p>
                <p className="flex items-center text-gray-700">
                  <ArrowRight className="h-5 w-5 text-[#028475] mr-2 flex-shrink-0" />
                  Or, use our <Link href="/contact-us?subject=Strategic+Alliance+Inquiry&interest=HighLevelPartnership" className="text-[#028475] hover:underline ml-1">dedicated contact form for high-level partnerships</Link>.
                </p>
              </div>
            </div>

            <div className="text-center">
              <Button
                className="bg-[#004235] hover:bg-[#028475] text-white px-8 py-3 text-lg font-semibold w-full sm:w-auto"
                size="lg"
                asChild
              >
                <Link href="/contact-us?subject=Propose+Strategic+Partnership&interest=PartnershipProposal">
                  PROPOSE A STRATEGIC PARTNERSHIP
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}