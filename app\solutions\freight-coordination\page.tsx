"use client";

import Link from "next/link";
import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/solutions/freight-coordination/HeroSection";
import ChallengesSection from "@/components/solutions/freight-coordination/ChallengesSection";
import PlatformOverviewSection from "@/components/solutions/freight-coordination/PlatformOverviewSection";
import HowItWorksSection from "@/components/solutions/freight-coordination/HowItWorksSection";
import BenefitsSection from "@/components/solutions/freight-coordination/BenefitsSection";
import CallToActionSection from "@/components/solutions/freight-coordination/CallToActionSection";

export default function FreightCoordinationPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <PlatformOverviewSection />

      <HowItWorksSection />

      <BenefitsSection />

      <CallToActionSection />

      <BottomFooter />
    </div>
  );
}