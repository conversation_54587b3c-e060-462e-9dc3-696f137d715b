"use client";

import { <PERSON>, BarChart, FileText, Users } from "lucide-react";

export default function UnderstandingIScoreSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Understanding Partner Performance Beyond Face Value
          </h2>
          <p className="text-xl text-gray-700 mb-10 text-center">
            What is the iScore™ Tool?
          </p>
          <p className="text-lg text-gray-700 mb-12 text-center">
            The iScore™ tool is the interactive interface for accessing the proprietary ratings and detailed reports generated by StreamLnk's comprehensive partner assessment system. It helps you:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <Search className="text-[#028475] h-8 w-8 mr-4 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-[#004235] text-lg mb-1">Search & Discover</h3>
                <p className="text-gray-600 text-sm">Look up iScore™ profiles for any registered supplier, buyer, or service provider on the StreamLnk platform.</p>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <BarChart className="text-[#028475] h-8 w-8 mr-4 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-[#004235] text-lg mb-1">View Overall & Categorical Scores</h3>
                <p className="text-gray-600 text-sm">See a composite iScore™ and drill down into performance across Operational Excellence, Compliance & Reliability, Financial Trustworthiness (where applicable), and Communication.</p>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <BarChart className="text-[#028475] h-8 w-8 mr-4 mt-1 flex-shrink-0" /> {/* Consider a different icon for trends if available */} 
              <div>
                <h3 className="font-semibold text-[#004235] text-lg mb-1">Analyze Performance Trends</h3>
                <p className="text-gray-600 text-sm">Track how a partner's iScore™ has evolved over time.</p>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <FileText className="text-[#028475] h-8 w-8 mr-4 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-[#004235] text-lg mb-1">Access Detailed Reports (Premium)</h3>
                <p className="text-gray-600 text-sm">Download comprehensive reports that explain the factors contributing to a score.</p>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start md:col-span-2">
              <Users className="text-[#028475] h-8 w-8 mr-4 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-[#004235] text-lg mb-1">Benchmark & Compare (Premium)</h3>
                <p className="text-gray-600 text-sm">Anonymously compare a potential partner's iScore™ against industry or regional averages.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}