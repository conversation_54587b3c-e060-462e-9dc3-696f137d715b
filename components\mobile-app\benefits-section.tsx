import { Wifi, Zap, Users, Smartphone, BarChart, CheckCircle } from "lucide-react";

const benefits = [
  {
    icon: <Wifi className="h-8 w-8 text-[#004235]" />,
    title: "Stay Connected 24/7",
    description: "Never miss a critical update or urgent request.",
  },
  {
    icon: <Zap className="h-8 w-8 text-[#004235]" />,
    title: "Act Faster",
    description: "Respond to RFQs, approve orders, or update statuses immediately.",
  },
  {
    icon: <Users className="h-8 w-8 text-[#004235]" />,
    title: "Increase Field Efficiency",
    description: "Empower your on-the-ground teams (drivers, warehouse staff, and field sales agents) with essential tools.",
  },
  {
    icon: <Smartphone className="h-8 w-8 text-[#004235]" />,
    title: "Enhanced Convenience",
    description: "Manage key aspects of your industrial trade operations from the palm of your hand.",
  },
  {
    icon: <BarChart className="h-8 w-8 text-[#004235]" />,
    title: "Improved Decision-Making",
    description: "Access real-time data and alerts to make timely choices.",
  },
  {
    icon: <CheckCircle className="h-8 w-8 text-[#004235]" />,
    title: "Streamlined Workflows",
    description: "Perform essential tasks like POD scanning directly through the app.",
  },
];

export default function BenefitsSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Productivity, Responsiveness, and Control – Anytime, Anywhere
          </h2>
          <p className="text-lg text-gray-700">Benefits of Using the StreamLnk Mobile App</p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {benefits.map((benefit, index) => (
            <div key={index} className="bg-white rounded-lg p-6 shadow-sm border-2 border-[#F2F2F2] hover:shadow-md transition-shadow">
              <div className="rounded-full bg-[#004235]/10 w-16 h-16 flex items-center justify-center mb-4 mx-auto">
                {benefit.icon}
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-3 text-center">{benefit.title}</h3>
              <p className="text-gray-600 text-center text-sm">{benefit.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}