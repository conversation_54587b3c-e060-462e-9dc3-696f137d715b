import { Card, CardContent } from "@/components/ui/card"

interface CurrencyCardProps {
  code: string
  name: string
}

export default function CurrencyCard({ code, name }: CurrencyCardProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
      <CardContent className="p-4 flex flex-col items-center text-center">
        <div className="text-xl font-bold text-[#004235] mb-1">{code}</div>
        <div className="text-sm text-gray-600">{name}</div>
      </CardContent>
    </Card>
  )
}
