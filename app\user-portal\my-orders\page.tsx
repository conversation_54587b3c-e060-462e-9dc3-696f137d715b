import { Metadata } from 'next';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Search, FileText, Download, MessageSquare, Edit, Eye, Trash2, PlusCircle, Filter, XCircle, FileDown, MapPin, History, AlertCircle, CheckCircle, Clock, Truck, Anchor, Building, ShoppingCart, User, Calendar, Tag, DollarSign, ChevronLeft, ChevronRight, Settings2, HelpCircle, Info, Mail } from 'lucide-react';

export const metadata: Metadata = {
  title: 'My Orders | StreamLnk Portal',
  description: 'View, track, and manage all your industrial material orders on StreamLnk.',
};

// Mock data - replace with actual data fetching
const userName = "Daniel"; // Replace with actual user data

const orderSummaryData = {
  totalOrders: 78,
  totalValue: 1250675.50,
  currency: 'USD',
  onTimeDeliveryRate: 92,
  averageOrderCycleTime: 15,
};

const sampleOrders = [
  {
    id: 'ORD12345',
    status: 'Shipped',
    statusColor: 'text-green-600',
    icon: <Truck className="h-4 w-4 mr-1 inline-block" />,
    product: 'HDPE Resin (Polyethylene)',
    quantity: '50 MT',
    supplierCustomer: 'PolymerChem Corp.',
    originDestination: 'Shanghai - Houston',
    currentStage: 'In Transit (Vessel)',
    eta: 'Nov 5',
    actions: [{ label: 'View Details', variant: 'outline', icon: <Eye className="h-4 w-4 mr-1" /> }, { label: 'Track', variant: 'outline', icon: <MapPin className="h-4 w-4 mr-1" /> }],
  },
  {
    id: 'ORD12346',
    status: 'Pending Payment',
    statusColor: 'text-yellow-600',
    icon: <Clock className="h-4 w-4 mr-1 inline-block" />,
    product: 'Specialty Chemical X',
    quantity: '1,000 kg',
    supplierCustomer: 'GlobalChem Ltd.',
    originDestination: 'Frankfurt - New York',
    currentStage: 'Delivered (Awaiting Payment)',
    eta: 'Oct 25',
    actions: [{ label: 'View Invoice', variant: 'outline', icon: <FileText className="h-4 w-4 mr-1" /> }, { label: 'Pay Now', variant: 'default', className: 'bg-[#028475] hover:bg-[#004235]', icon: <DollarSign className="h-4 w-4 mr-1" /> }],
  },
  {
    id: 'ORD12347',
    status: 'In Production',
    statusColor: 'text-blue-600',
    icon: <Settings2 className="h-4 w-4 mr-1 inline-block" />,
    product: 'PP Compound (Automotive)',
    quantity: '20 MT',
    supplierCustomer: 'AutoPlast Solutions',
    originDestination: 'Seoul - Detroit',
    currentStage: 'Production Started',
    eta: 'Nov 18',
    actions: [{ label: 'View Details', variant: 'outline', icon: <Eye className="h-4 w-4 mr-1" /> }, { label: 'Contact Supplier', variant: 'outline', icon: <Mail className="h-4 w-4 mr-1" /> }],
  },
  {
    id: 'ORD12348',
    status: 'Customs Cleared',
    statusColor: 'text-green-600',
    icon: <CheckCircle className="h-4 w-4 mr-1 inline-block" />,
    product: 'Building Aggregates',
    quantity: '200 CY',
    supplierCustomer: 'Construmate Inc.',
    originDestination: 'Mexico City - Dallas',
    currentStage: 'Ready for Local Pickup',
    eta: 'Nov 1',
    actions: [{ label: 'View Customs Docs', variant: 'outline', icon: <FileText className="h-4 w-4 mr-1" /> }, { label: 'Schedule Pickup', variant: 'outline', icon: <Calendar className="h-4 w-4 mr-1" /> }],
  },
  {
    id: 'RFQ98765',
    status: 'RFQ Sent',
    statusColor: 'text-gray-600',
    icon: <Info className="h-4 w-4 mr-1 inline-block" />,
    product: 'Bio-PET Pellets',
    quantity: '10 MT',
    supplierCustomer: 'BioGreen Materials',
    originDestination: 'London - Paris',
    currentStage: 'Awaiting Supplier Quote',
    eta: 'Nov 8',
    actions: [{ label: 'View RFQ', variant: 'outline', icon: <FileText className="h-4 w-4 mr-1" /> }, { label: 'Withdraw RFQ', variant: 'destructive', icon: <XCircle className="h-4 w-4 mr-1" /> }],
  },
  {
    id: 'ORD12344',
    status: 'Delivered',
    statusColor: 'text-green-600',
    icon: <CheckCircle className="h-4 w-4 mr-1 inline-block" />,
    product: 'Adhesive XYZ',
    quantity: '500 L',
    supplierCustomer: 'ChemFast Supplies',
    originDestination: 'Lyon - Berlin',
    currentStage: 'Completed',
    eta: 'Oct 20',
    actions: [{ label: 'Confirm Receipt', variant: 'outline', icon: <CheckCircle className="h-4 w-4 mr-1" /> }, { label: 'Rate Supplier', variant: 'outline', icon: <Tag className="h-4 w-4 mr-1" /> }, { label: 'View Documents', variant: 'outline', icon: <FileText className="h-4 w-4 mr-1" /> }],
  },
  {
    id: 'ORD12349',
    status: 'On Hold',
    statusColor: 'text-red-600',
    icon: <AlertCircle className="h-4 w-4 mr-1 inline-block" />,
    product: '[Product Name]',
    quantity: '[Qty]',
    supplierCustomer: '[Supplier/Customer Name]',
    originDestination: '[Origin-Dest]',
    currentStage: 'Payment Overdue',
    eta: '-',
    actions: [{ label: 'Resolve Issue', variant: 'default', className: 'bg-red-600 hover:bg-red-700', icon: <AlertCircle className="h-4 w-4 mr-1" /> }, { label: 'View Details', variant: 'outline', icon: <Eye className="h-4 w-4 mr-1" /> }],
  },
];

// Mock data for detailed order view
const detailedOrder = {
  id: 'ORD12345',
  status: 'In Transit (Vessel)',
  estimatedDelivery: 'November 5, 2023',
  productName: 'HDPE Resin (Polyethylene)',
  productGrade: 'Film Grade F123',
  quantity: '50 Metric Tons (MT)',
  packaging: '25kg Bags',
  supplier: 'PolymerChem Corp.',
  supplierIScore: '92/100',
  buyer: 'Your Company Name',
  totalValue: '65,000.00',
  currency: 'USD',
  incoterms: 'FOB Shanghai',
  paymentTerms: 'Net 30',
  shipmentProgress: [
    { stage: 'Order Confirmed', date: 'Oct 10, 2023', completed: true },
    { stage: 'Production Complete', date: 'Oct 15, 2023', completed: true },
    { stage: 'Ready for Pickup', date: 'Oct 16, 2023', completed: true },
    { stage: 'Picked Up by Carrier (Land)', date: 'Oct 17, 2023', completed: true },
    { stage: 'Container Gate-In (Port)', date: 'Oct 18, 2023', completed: true },
    { stage: 'Loaded on Vessel', details: 'Vessel: Evergreen Marine, Voyage: V0123, Date: Oct 20, 2023', completed: true },
    { stage: 'Vessel Sailed', date: 'Oct 21, 2023', completed: true },
    { stage: 'In Transit (Ocean)', details: 'Current Location: Pacific Ocean, ETA Port: Nov 3, 2023', completed: true, current: true },
    { stage: 'Customs Submitted', date: 'Nov 4, 2023 (Est.)', details: 'Status: Pending Review', completed: false },
    { stage: 'Customs Cleared', date: '', completed: false },
    { stage: 'Discharged at Port', date: '', completed: false },
    { stage: 'Ready for Final Delivery', date: '', completed: false },
    { stage: 'Delivered', date: '', completed: false },
  ],
  documents: [
    { name: 'Purchase Order (PO) - ORD12345.pdf', icon: <FileText className="h-5 w-5 text-red-500 mr-2" /> },
    { name: 'Commercial Invoice - INV98765.pdf', icon: <FileText className="h-5 w-5 text-red-500 mr-2" /> },
    { name: 'Packing List - PKG98765.pdf', icon: <FileText className="h-5 w-5 text-red-500 mr-2" /> },
    { name: 'Bill of Lading (B/L) - BL12345.pdf', icon: <FileText className="h-5 w-5 text-red-500 mr-2" /> },
    { name: 'Certificate of Analysis (CoA) - COA98765.pdf', icon: <FileText className="h-5 w-5 text-red-500 mr-2" /> },
    { name: 'Certificate of Origin (COO) - COO98765.pdf', icon: <FileText className="h-5 w-5 text-red-500 mr-2" /> },
    { name: 'Material Safety Data Sheet (MSDS/SDS) - MSDS_HDPE.pdf', icon: <FileText className="h-5 w-5 text-red-500 mr-2" /> },
  ],
  communicationLog: [
    { date: 'Oct 28, 2023', sender: 'StreamLnk Ops', message: 'Customs documents submitted.' },
    { date: 'Oct 25, 2023', sender: 'Supplier', message: 'Production is 80% complete, estimated finish Oct 27.' },
    { date: 'Oct 10, 2023', sender: 'Buyer', message: 'Confirming order details and requesting CoA.' },
  ],
};

// For now, we will not implement the detailed view logic, just the main list page.
// const [selectedOrder, setSelectedOrder] = useState<typeof detailedOrder | null>(null);

export default function MyOrdersPage() {
  return (
    <div className="container mx-auto px-4 py-8 bg-gray-50 min-h-screen">
      <header className="mb-8">
        <h1 className="text-3xl font-bold text-[#004235]">My Orders: Your Centralized Order Management Hub</h1>
        <p className="text-lg text-gray-700 mt-2">
          Welcome, <span className="font-semibold text-[#028475]">{userName}</span>! Here you can view, track, and manage all your industrial material orders. Get real-time updates, access documents, and stay in control of your supply chain.
        </p>
      </header>

      <Card className="mb-8 shadow-lg">
        <CardHeader>
          <CardTitle className="text-xl text-[#004235] flex items-center">
            <Filter className="h-5 w-5 mr-2 text-[#028475]" /> Quick Actions & Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center mb-6 space-x-2">
            <Input
              type="search"
              placeholder="Search Orders by ID, Product, Supplier/Customer..."
              className="flex-grow"
            />
            <Button className="bg-[#004235] hover:bg-[#028475] text-white">
              <Search className="h-4 w-4 mr-2" /> Search
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 mb-6">
            <Select>
              <SelectTrigger className="w-full"><SelectValue placeholder="Status: All" /></SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="pending_confirmation">Pending Confirmation</SelectItem>
                <SelectItem value="in_production">In Production</SelectItem>
                <SelectItem value="shipped">Shipped</SelectItem>
                <SelectItem value="customs_cleared">Customs Cleared</SelectItem>
                <SelectItem value="out_for_delivery">Out for Delivery</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="canceled">Canceled</SelectItem>
                <SelectItem value="on_hold">On Hold</SelectItem>
                <SelectItem value="partially_delivered">Partially Delivered</SelectItem>
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger className="w-full"><SelectValue placeholder="Date Range: Last 30 Days" /></SelectTrigger>
              <SelectContent>
                <SelectItem value="last_30_days">Last 30 Days</SelectItem>
                <SelectItem value="last_90_days">Last 90 Days</SelectItem>
                <SelectItem value="this_year">This Year</SelectItem>
                <SelectItem value="custom_range">Custom Range</SelectItem>
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger className="w-full"><SelectValue placeholder="Supplier / Customer" /></SelectTrigger>
              <SelectContent>
                {/* Populate with dynamic data */}
                <SelectItem value="supplier1">PolymerChem Corp.</SelectItem>
                <SelectItem value="supplier2">GlobalChem Ltd.</SelectItem>
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger className="w-full"><SelectValue placeholder="Product Category" /></SelectTrigger>
              <SelectContent>
                <SelectItem value="polymers">Polymers</SelectItem>
                <SelectItem value="chemicals">Chemicals</SelectItem>
                <SelectItem value="energy">Energy</SelectItem>
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger className="w-full"><SelectValue placeholder="Value: All" /></SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="lt_10k">&lt;$10K</SelectItem>
                <SelectItem value="10k_100k">$10K - $100K</SelectItem>
                <SelectItem value="gt_100k">&gt;$100K</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center space-x-2">
            <Button className="bg-[#004235] hover:bg-[#028475] text-white">
              <Filter className="h-4 w-4 mr-2" /> Apply Filters
            </Button>
            <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white">
              <XCircle className="h-4 w-4 mr-2" /> Clear Filters
            </Button>
            <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white">
              <FileDown className="h-4 w-4 mr-2" /> Export Orders
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-8 shadow-lg">
        <CardHeader>
          <CardTitle className="text-xl text-[#004235] flex items-center">
            <Info className="h-5 w-5 mr-2 text-[#028475]" /> Order Summary (Past 30 Days)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <p className="text-2xl font-semibold text-[#028475]">{orderSummaryData.totalOrders}</p>
              <p className="text-sm text-gray-600">Total Orders</p>
            </div>
            <div>
              <p className="text-2xl font-semibold text-[#028475]">{orderSummaryData.currency} {orderSummaryData.totalValue.toLocaleString()}</p>
              <p className="text-sm text-gray-600">Total Value</p>
            </div>
            <div>
              <p className="text-2xl font-semibold text-[#028475]">{orderSummaryData.onTimeDeliveryRate}%</p>
              <p className="text-sm text-gray-600">On-Time Delivery</p>
            </div>
            <div>
              <p className="text-2xl font-semibold text-[#028475]">{orderSummaryData.averageOrderCycleTime} Days</p>
              <p className="text-sm text-gray-600">Avg. Cycle Time</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-xl text-[#004235] flex items-center">
            <ShoppingCart className="h-5 w-5 mr-2 text-[#028475]" /> My Active & Recent Orders
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Status</TableHead>
                <TableHead>Order ID</TableHead>
                <TableHead>Product</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Supplier / Customer</TableHead>
                <TableHead>Origin - Dest.</TableHead>
                <TableHead>Current Stage</TableHead>
                <TableHead>ETA</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sampleOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className={`font-medium ${order.statusColor}`}>{order.icon}{order.status}</TableCell>
                  <TableCell>{order.id}</TableCell>
                  <TableCell>{order.product}</TableCell>
                  <TableCell>{order.quantity}</TableCell>
                  <TableCell>{order.supplierCustomer}</TableCell>
                  <TableCell>{order.originDestination}</TableCell>
                  <TableCell>{order.currentStage}</TableCell>
                  <TableCell>{order.eta}</TableCell>
                  <TableCell className="text-right space-x-1">
                    {order.actions.map(action => (
                      <Button key={action.label} variant={action.variant as any} size="sm" className={action.className}>
                        {action.icon}{action.label}
                      </Button>
                    ))}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <div className="mt-6 flex justify-center">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious href="#" />
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink href="#">1</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink href="#" isActive>
                    2
                  </PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink href="#">3</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationEllipsis />
                </PaginationItem>
                <PaginationItem>
                  <PaginationNext href="#" />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Order View - To be implemented as a modal or separate section */}
      {/* For now, this section is commented out. It will be a significant piece of work. */}
      {/* 
      <Separator className="my-12" />
      <section id="detailed-order-view" className="mb-8 p-6 bg-white rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold text-[#004235] mb-2">Detailed Order View</h2>
        <p className="text-sm text-gray-500 mb-4">Order ID: <span className="font-medium text-black">{detailedOrder.id}</span></p>
        <p className="text-lg font-semibold mb-4 ${detailedOrder.status === 'In Transit (Vessel)' ? 'text-blue-600' : 'text-gray-700'}">
          Status: {detailedOrder.status} - Estimated Delivery: {detailedOrder.estimatedDelivery}
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h3 className="text-lg font-semibold text-[#004235] mb-2">Order Details:</h3>
            <ul className="space-y-1 text-sm text-gray-700">
              <li><strong>Product:</strong> {detailedOrder.productName} - Grade {detailedOrder.productGrade}</li>
              <li><strong>Quantity:</strong> {detailedOrder.quantity}</li>
              <li><strong>Packaging:</strong> {detailedOrder.packaging}</li>
              <li><strong>Supplier:</strong> {detailedOrder.supplier} (<Link href="#" className="text-[#028475] hover:text-[#004235]">View Profile</Link> | iScore: {detailedOrder.supplierIScore})</li>
              <li><strong>Buyer:</strong> {detailedOrder.buyer}</li>
              <li><strong>Total Value:</strong> ${detailedOrder.totalValue} {detailedOrder.currency}</li>
              <li><strong>Incoterms:</strong> {detailedOrder.incoterms}</li>
              <li><strong>Payment Terms:</strong> {detailedOrder.paymentTerms}</li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-[#004235] mb-2">Shipment Progress:</h3>
            <div className="relative pl-4 border-l-2 border-[#028475]">
              {detailedOrder.shipmentProgress.map((item, index) => (
                <div key={index} className={`mb-4 pl-4 relative ${item.completed ? '' : 'opacity-60'}`}>
                  <div className={`absolute -left-[0.6rem] top-1 w-4 h-4 rounded-full ${item.completed ? (item.current ? 'bg-yellow-400 ring-4 ring-yellow-200' : 'bg-[#028475]') : 'bg-gray-300'}`}></div>
                  <p className={`font-medium ${item.completed ? 'text-[#004235]' : 'text-gray-500'}`}>{item.stage}</p>
                  {item.date && <p className="text-xs text-gray-500">{item.date}</p>}
                  {item.details && <p className="text-xs text-gray-500">{item.details}</p>}
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="mb-6">
          <h3 className="text-lg font-semibold text-[#004235] mb-3 flex items-center">
            <MapPin className="h-5 w-5 mr-2 text-[#028475]" /> Live Tracking:
          </h3>
          <div className="space-x-2">
            <Button className="bg-[#004235] hover:bg-[#028475] text-white">
              <MapPin className="h-4 w-4 mr-2" /> Track on Map
            </Button>
            <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white">
              <History className="h-4 w-4 mr-2" /> View Tracking History
            </Button>
          </div>
        </div>

        <div className="mb-6">
          <h3 className="text-lg font-semibold text-[#004235] mb-3 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-[#028475]" /> Associated Documents:
          </h3>
          <ul className="space-y-2 mb-3">
            {detailedOrder.documents.map(doc => (
              <li key={doc.name} className="flex items-center text-sm text-gray-700 hover:text-[#028475] cursor-pointer">
                {doc.icon} {doc.name}
              </li>
            ))}
          </ul>
          <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white">
            <Download className="h-4 w-4 mr-2" /> Download All Documents
          </Button>
        </div>

        <div className="mb-6">
          <h3 className="text-lg font-semibold text-[#004235] mb-3 flex items-center">
            <MessageSquare className="h-5 w-5 mr-2 text-[#028475]" /> Communication Log:
          </h3>
          <div className="space-y-3 text-sm border p-4 rounded-md bg-gray-50">
            {detailedOrder.communicationLog.map((log, index) => (
              <div key={index}>
                <p className="text-gray-500 text-xs">{log.date} - <span className="font-medium text-gray-700">{log.sender}</span>:</p>
                <p className="text-gray-800">{log.message}</p>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold text-[#004235] mb-3">Actions for this Order:</h3>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white">
              <Mail className="h-4 w-4 mr-2" /> Contact Support
            </Button>
            <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white">
              <DollarSign className="h-4 w-4 mr-2" /> View Payment / Pay
            </Button>
            <Button variant="outline" className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white">
              <AlertCircle className="h-4 w-4 mr-2" /> Dispute Order
            </Button>
            <Button className="bg-[#028475] hover:bg-[#004235] text-white">
              <PlusCircle className="h-4 w-4 mr-2" /> Reorder This Product
            </Button>
          </div>
        </div>
      </section>
      */}

      <Separator className="my-12" />

      <footer className="text-center py-8 bg-[#F2F2F2] rounded-lg shadow-md">
        <h2 className="text-xl font-semibold text-[#004235] mb-4">Need Help with Your Orders?</h2>
        <div className="space-x-4">
          <Link href="/faqs/order-management" className="text-[#028475] hover:text-[#004235] hover:underline">
            <HelpCircle className="h-5 w-5 inline mr-1" /> Visit our FAQs
          </Link>
          <Link href="/support/shipment-tracking" className="text-[#028475] hover:text-[#004235] hover:underline">
            <Info className="h-5 w-5 inline mr-1" /> Learn about Tracking
          </Link>
          <Link href="/contact-us" className="text-[#028475] hover:text-[#004235] hover:underline">
            <Mail className="h-5 w-5 inline mr-1" /> Contact Customer Service
          </Link>
        </div>
      </footer>
    </div>
  );
}