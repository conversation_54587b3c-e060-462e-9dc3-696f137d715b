import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { CheckCircle } from "lucide-react";

const partnerTypes = [
  {
    title: "ERP & Accounting System Providers",
    examples: "(e.g., SAP, Oracle NetSuite, Microsoft Dynamics, QuickBooks, Xero)",
    integrationFocus: "Syncing orders, invoices, payments, customer/supplier master data between StreamLnk and enterprise financial/operational systems.",
    benefit: "Eliminate manual data entry, ensure data consistency, streamline quote-to-cash and procure-to-pay processes."
  },
  {
    title: "SCM (Supply Chain Management) Software Providers",
    integrationFocus: "Exchanging shipment data, inventory levels, demand/supply signals, and logistics updates between StreamLnk and specialized SCM planning and execution tools.",
    benefit: "Provide users with a holistic view of their supply chain, incorporating StreamLnk's real-time trade data."
  },
  {
    title: "Fintech & Payment Solution Providers",
    integrationFocus: "Embedding BNPL for B2B, escrow services, FX management, trade credit insurance, and advanced payment processing solutions directly into StreamLnk's transaction workflows.",
    benefit: "Offer StreamLnk users enhanced financial flexibility and security; expand your reach to a high-volume B2B transaction flow."
  },
  {
    title: "Compliance & Risk Management Technology Providers",
    integrationFocus: "Connecting KYC/AML verification tools, sanctions screening services, ESG reporting platforms, and specialized trade compliance software with StreamLnk's onboarding and transaction processes.",
    benefit: "Enhance the security and regulatory adherence of the StreamLnk ecosystem."
  },
  {
    title: "Data Analytics & Business Intelligence (BI) Platforms",
    examples: "(e.g., Tableau, Power BI, Snowflake)",
    integrationFocus: "Enabling users and data partners to pull aggregated, anonymized data from StreamResources+ via API into their preferred BI tools for custom analysis and dashboarding.",
    benefit: "Allow deeper, customized insights from StreamLnk's rich data sets."
  },
  {
    title: "Logistics Technology & Freight Visibility Platforms",
    examples: "(e.g., Project44, FourKites - for deeper data exchange beyond standard carrier APIs)",
    integrationFocus: "Enhancing real-time shipment tracking, predictive ETAs, and logistics performance analytics within StreamLnk.",
    benefit: "Provide best-in-class visibility for complex global shipments."
  },
  {
    title: "Industry-Specific Software",
    examples: "(e.g., Lab Information Management Systems (LIMS) for chemicals, Farm Management Software for agri-commodities)",
    integrationFocus: "Connecting specialized vertical software with StreamLnk for seamless data flow related to quality, provenance, or production.",
    benefit: "Create highly tailored end-to-end solutions for specific industries."
  }
];

export function ApproachSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <div className="flex items-center justify-center mb-4">
            <div className="w-10 h-1 bg-[#028475] mr-3"></div>
            <span className="text-[#028475] font-medium">OUR APPROACH</span>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            Connect, Automate, Innovate: Partnering with StreamLnk's Technology Layer
          </h2>
          <p className="text-lg text-gray-700 mb-4">
            StreamLnk is designed with connectivity at its core. We offer robust, well-documented APIs and integration pathways to enable technology partners to seamlessly connect their solutions with our ecosystem.
          </p>
          <p className="text-lg text-gray-700">
            Our goal is to create a richer, more powerful experience for all users.
          </p>
        </div>

        <div className="mb-16">
          <h3 className="text-2xl md:text-3xl font-bold text-[#004235] mb-10 text-center">
            Types of Technology Integration Partners We Seek:
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {partnerTypes.map((partner, index) => (
              <Card key={index} className="border-[#004235] border-t-4 shadow-lg hover:shadow-xl transition-shadow duration-300 flex flex-col">
                <CardHeader>
                  <CardTitle className="text-[#004235] text-xl">{partner.title}</CardTitle>
                  {partner.examples && <p className="text-sm text-gray-500 italic">{partner.examples}</p>}
                </CardHeader>
                <CardContent className="flex-grow">
                  <div className="mb-4">
                    <h4 className="font-semibold text-[#028475] mb-1">Integration Focus:</h4>
                    <p className="text-gray-700 text-sm">{partner.integrationFocus}</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-[#028475] mb-1">Benefit:</h4>
                    <p className="text-gray-700 text-sm">{partner.benefit}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}