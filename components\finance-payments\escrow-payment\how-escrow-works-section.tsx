import { Shield, CheckCircle2, <PERSON>Key<PERSON> } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export default function HowEscrowWorksSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">How StreamLnk Escrow Works</h2>
          <p className="text-gray-600 max-w-3xl">
            Our escrow system provides security and peace of mind for both buyers and suppliers throughout the
            transaction process.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
            <CardContent className="pt-6">
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="p-3 rounded-full bg-[#004235]/10">
                  <LockKeyhole className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-semibold text-[#004235]">Funds Held Securely</h3>
                <p className="text-gray-600">
                  Buyer deposits funds into a protected third-party escrow account, ensuring payment is available but
                  protected.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
            <CardContent className="pt-6">
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="p-3 rounded-full bg-[#004235]/10">
                  <CheckCircle2 className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-semibold text-[#004235]">Release on Milestone Completion</h3>
                <p className="text-gray-600">
                  Funds are released automatically or manually as key delivery or documentation milestones are met.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
            <CardContent className="pt-6">
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="p-3 rounded-full bg-[#004235]/10">
                  <Shield className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-semibold text-[#004235]">Dispute Resolution Protocols</h3>
                <p className="text-gray-600">
                  In case of discrepancies, StreamLnk offers built-in resolution pathways with time-stamped logs and
                  document audits.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}