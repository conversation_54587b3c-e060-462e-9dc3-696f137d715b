// components/portal-features/mobile/NeedForMobileSection.tsx
import { Clock, FileCheck, Map, Zap } from "lucide-react";

export default function NeedForMobileSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">The Need for Mobile Access in Modern Industrial Trade</h2>
          <h3 className="text-xl font-semibold text-[#028475] mb-6 text-center">Why Your Supply Chain Can't Be Tied to a Desk</h3>
          <p className="text-lg text-gray-700 mb-8">
            In today's fast-moving industrial landscape, professionals need to access information and take action from anywhere:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <Map className="h-5 w-5 text-white" />
              </div>
              <p className="text-gray-700"><span className="font-semibold">On the Move:</span> Sales agents visiting clients, procurement managers inspecting facilities, logistics coordinators at ports or warehouses.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <Clock className="h-5 w-5 text-white" />
              </div>
              <p className="text-gray-700"><span className="font-semibold">Needing Real-Time Updates:</span> Tracking urgent shipments, responding to critical alerts, or monitoring market changes.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <FileCheck className="h-5 w-5 text-white" />
              </div>
              <p className="text-gray-700"><span className="font-semibold">Requiring Field Functionality:</span> Drivers needing to upload Proof of Delivery, or warehouse staff performing quick inventory checks.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <Zap className="h-5 w-5 text-white" />
              </div>
              <p className="text-gray-700"><span className="font-semibold">Seeking Convenience:</span> Managing key tasks without being tied to a desktop computer.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}