import { CheckCircle2, UserCog, Network, TrendingUp, MessageSquare, Brain, ShieldAlert } from "lucide-react";

const idealCandidateTraits = [
  {
    icon: <UserCog className="h-6 w-6 text-[#004235]" />,
    text: "Proven experience in B2B sales or business development within industrial sectors (polymers, chemicals, manufacturing, logistics, energy).",
  },
  {
    icon: <Network className="h-6 w-6 text-[#004235]" />,
    text: "Strong existing network of contacts with potential buyers and/or suppliers.",
  },
  {
    icon: <Brain className="h-6 w-6 text-[#004235]" />,
    text: "Deep understanding of regional market dynamics and business practices.",
  },
  {
    icon: <TrendingUp className="h-6 w-6 text-[#004235]" />,
    text: "Entrepreneurial mindset and a drive to succeed.",
  },
  {
    icon: <MessageSquare className="h-6 w-6 text-[#004235]" />,
    text: "Excellent communication and relationship-building skills.",
  },
  {
    icon: <CheckCircle2 className="h-6 w-6 text-[#004235]" />,
    text: "Ability to understand and articulate the value of a digital B2B platform.",
  },
  {
    icon: <ShieldAlert className="h-6 w-6 text-[#004235]" />,
    text: "Commitment to ethical business practices and StreamLnk's values.",
  },
];

export default function IdealCandidatesSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Ideal Candidates for Our Channel Partner Program
          </h2>
          <p className="text-xl font-semibold text-[#028475]">
            Who We're Looking For
          </p>
        </div>

        <div className="max-w-3xl mx-auto bg-white p-8 md:p-10 rounded-lg shadow-lg">
          <ul className="space-y-5">
            {idealCandidateTraits.map((trait, index) => (
              <li key={index} className="flex items-start">
                <div className="flex-shrink-0 mr-3 mt-1 bg-[#028475]/10 p-2 rounded-full">
                  {trait.icon}
                </div>
                <span className="text-gray-700 text-lg">{trait.text}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  );
}