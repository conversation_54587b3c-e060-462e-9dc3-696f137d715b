import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react"; // Removed Download, kept ArrowRight
import Link from "next/link";

export default function CtaSection() {
  return (
    <section className="py-16 bg-white"> {/* Standardized padding */}
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center"> {/* Removed bg, padding, rounded, shadow */}
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6"> {/* Matched H2 style */}
            Partner with StreamLnk
          </h2>
          <p className="text-xl text-gray-700 mb-8"> {/* Matched P style */}
            Join the StreamLnk Partner Network. Take the next step to becoming a valued Channel Partner and build a successful business on the future of global commerce.
          </p>
          {/* Merged paragraph content */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center"> {/* Standardized button group div */}
            <Button 
              size="lg" 
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto" // Matched primary button
              asChild
            >
              <Link href="/partners/channel-reseller/apply-agent"> 
                APPLY AS AGENT
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto" // Matched outline button
              asChild
            >
              <Link href="/partners/channel-reseller/inquire-distributor"> 
                INQUIRE DISTRIBUTOR
              </Link>
            </Button>
          </div>
          <div className="mt-6"> {/* Added div for link button as per reference */}
            <Button
              variant="link"
              className="text-[#028475] hover:text-[#004235]" // Matched link button
              asChild
            >
              <Link href="/resources/channel-partner-program-overview.pdf" target="_blank"> 
                DOWNLOAD OVERVIEW
                <ArrowRight className="ml-2 h-4 w-4" /> {/* Changed icon to ArrowRight */}
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}