"use client";

import Link from 'next/link';
import { Brain, Zap, BarChart3, Landmark, Settings2, Share2, ShieldCheck, Lightbulb, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

const trends = [
  {
    icon: <Brain className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Artificial Intelligence (AI) & Machine Learning (ML) in Supply Chain",
    focus: "Predictive analytics for demand/supply/pricing (StreamIndex™), smart logistics routing, AI-assisted quoting, fraud detection, intelligent risk assessment (iScore™), automated document processing.",
    angle: "How AI is moving beyond hype to deliver tangible ROI in industrial operations.",
    link: "/solutions/ai-in-supply-chain", // Placeholder, update as needed
    linkText: "Articles on AI"
  },
  {
    icon: <Share2 className="h-10 w-10 text-[#028475] mb-4" />,
    title: "End-to-End Supply Chain Digitization & Platformization",
    focus: "The shift from siloed systems to integrated digital ecosystems like StreamLnk. Benefits of unified platforms for sourcing, logistics, finance, and compliance.",
    angle: "Showcasing the power of a 'single source of truth' and interconnected portals.",
    link: "/solutions/platform-overview", // Placeholder, update as needed
    linkText: "Insights on Digitization"
  },
  {
    icon: <BarChart3 className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Big Data & Advanced Analytics for Industrial Trade",
    focus: "Leveraging transactional and operational data for market intelligence (StreamResources+), performance benchmarking, and strategic foresight. The role of DaaS.",
    angle: "How to turn supply chain data into a competitive asset.",
    link: "/solutions/streamindex-benchmarks",
    linkText: "Our Data Analytics"
  },
  {
    icon: <Landmark className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Fintech Integration in B2B Commerce",
    focus: "The rise of embedded finance – BNPL for B2B, digital escrow, multi-currency payment solutions, and automated AR/AP management in industrial trade.",
    angle: "Reducing financial friction and unlocking working capital in global supply chains.",
    link: "/solutions/trade-finance",
    linkText: "Fintech in B2B Trade"
  },
  {
    icon: <Settings2 className="h-10 w-10 text-[#028475] mb-4" />,
    title: "IoT (Internet of Things) in Logistics & Warehousing",
    subtitle: "(Future Focus for StreamLnk)",
    focus: "Real-time tracking of cargo conditions (temperature, humidity, shock), smart warehouse management, predictive maintenance for logistics assets.",
    angle: "Exploring how IoT data can further enhance visibility and risk management on our platform.",
    link: "#", // To be updated when content is available
    linkText: "Insights on IoT (Soon)",
    disabled: true
  },
  {
    icon: <ShieldCheck className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Blockchain for Traceability & Security",
    subtitle: "(Future Focus for StreamLnk)",
    focus: "Enhancing material provenance, chain of custody for high-value or regulated goods, secure document exchange, smart contracts for payments.",
    angle: "Investigating practical applications of blockchain to build greater trust and transparency.",
    link: "#", // To be updated when content is available
    linkText: "Blockchain Thoughts (Soon)",
    disabled: true
  }
];

export default function KeyTechTrendsSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <Lightbulb className="h-12 w-12 text-[#004235] mx-auto mb-4" />
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            StreamLnk's Perspective on Transformative Technologies
          </h2>
          <p className="text-xl text-gray-700">
            Key Technology Trends Explored by StreamLnk
          </p>
        </div>
        <p className="text-lg text-gray-600 mb-12 max-w-4xl mx-auto text-center">
          At StreamLnk, we are not only users but also innovators and commentators on the technologies reshaping our industry. We focus on understanding and applying:
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {trends.map((trend, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-lg flex flex-col">
              <div className="flex-shrink-0">{trend.icon}</div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">{trend.title}</h3>
              {trend.subtitle && <p className="text-sm text-gray-500 mb-2">{trend.subtitle}</p>}
              <p className="text-sm text-gray-600 mb-1"><strong className='text-gray-700'>Focus:</strong> {trend.focus}</p>
              <p className="text-sm text-gray-600 mb-4 flex-grow"><strong className='text-gray-700'>StreamLnk's Angle:</strong> {trend.angle}</p>
              <Button 
                variant="link"
                className={`text-[#028475] hover:text-[#004235] p-0 justify-start ${trend.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                asChild
                disabled={trend.disabled}
              >
                <Link href={trend.disabled ? "#" : trend.link}>
                  {trend.linkText}
                  {!trend.disabled && <ArrowRight className="ml-2 h-4 w-4" />}
                </Link>
              </Button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}