// app/portal-features/integrations/page.tsx
import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";
import HeroSection from "@/components/portal-features/integrations/HeroSection";
import ProblemStatementSection from "@/components/portal-features/integrations/ProblemStatementSection";
import IntegrationCapabilitiesSection from "@/components/portal-features/integrations/IntegrationCapabilitiesSection";
import BenefitsSection from "@/components/portal-features/integrations/BenefitsSection";
import GettingStartedSection from "@/components/portal-features/integrations/GettingStartedSection";

export default function IntegrationsPage() {
  return (
    <div className="bg-white min-h-screen">
      <MainNav />
      <HeroSection />
      <ProblemStatementSection />
      <IntegrationCapabilitiesSection />
      <BenefitsSection />
      <GettingStartedSection />
      <MainFooter />
    </div>
  );
}