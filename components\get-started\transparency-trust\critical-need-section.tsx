import { Alert<PERSON>riangle, XCircle, DollarSign, MessageSquareWarning, UserX, SearchSlash } from "lucide-react";

export default function CriticalNeedSection() {
  const issues = [
    {
      icon: <UserX className="h-6 w-6 text-red-600 mr-3 flex-shrink-0" />,
      text: "Uncertainty about supplier/buyer legitimacy and reliability.",
    },
    {
      icon: <SearchSlash className="h-6 w-6 text-red-600 mr-3 flex-shrink-0" />,
      text: "Opaque pricing and hidden fees, making true cost calculation difficult.",
    },
    {
      icon: <AlertTriangle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0" />,
      text: "Risk of fraud, non-compliance, and payment defaults.",
    },
    {
      icon: <MessageSquareWarning className="h-6 w-6 text-red-600 mr-3 flex-shrink-0" />,
      text: "Inefficient communication and disputes arising from unclear terms or processes.",
    },
    {
      icon: <XCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0" />,
      text: "Hesitancy to engage with new or international trading partners.",
    },
    {
      icon: <DollarSign className="h-6 w-6 text-red-600 mr-3 flex-shrink-0" />,
      text: "Wasted resources on extensive manual due diligence.",
    },
  ];

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-xl md:text-2xl font-semibold text-[#028475] mb-2">
              Why Transparency and Trust are the Cornerstones of Successful Global Commerce
            </h2>
            <h3 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
              The Critical Need for Transparency & Trust in B2B Trade
            </h3>
            <p className="text-lg text-gray-700">
              Lack of transparency and trust in industrial B2B transactions leads to:
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-x-8 gap-y-6">
            {issues.map((item, index) => (
              <div key={index} className="flex items-start p-4 bg-red-50/50 rounded-lg border border-red-200">
                {item.icon}
                <p className="text-gray-700 text-sm">{item.text}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}