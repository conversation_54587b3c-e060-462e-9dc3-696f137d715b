import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowRight } from "lucide-react"; // Added ArrowRight import

export default function CtaSection() {
  return (
    <section className="py-16 bg-white"> {/* Standardized padding */}
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center"> {/* Standardized max-width */}
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            Join StreamLnk: Get Started
          </h2>
          <p className="text-xl text-gray-700 mb-8"> {/* Standardized text size and margin */}
            Ready to Experience a New Standard in B2B Trade?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto" // Standardized primary button
              asChild
            >
              <Link href="/request-demo">
                REQUEST DEMO
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button 
              variant="outline" // Changed to outline
              size="lg" 
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto" // Standardized outline button
              asChild
            >
              <Link href="/register">
                CREATE ACCOUNT 
              </Link>
            </Button>
          </div>
          <div className="mt-6"> {/* Added new div for link button */}
            <Button
              variant="link" // Changed to link
              className="text-[#028475] hover:text-[#004235]"
              asChild
            >
              <Link href="/security-compliance">
                LEARN SECURITY
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}