"use client";

import { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Mail, Send } from "lucide-react";

export default function EventSubscriptionCtaSection() {
  const [email, setEmail] = useState('');
  const [interestWebinars, setInterestWebinars] = useState(false);
  const [interestInPerson, setInterestInPerson] = useState(false);
  const [interestIndustry, setInterestIndustry] = useState(false);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Handle event subscription logic here
    console.log({
      email,
      interests: {
        webinars: interestWebinars,
        inPersonEvents: interestInPerson,
        industrySpecific: interestIndustry,
      }
    });
    alert(`Subscribed ${email} for event updates!`);
    setEmail('');
    setInterestWebinars(false);
    setInterestIn<PERSON>erson(false);
    setInterestIndustry(false);
  };

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center bg-[#F2F2F2] p-8 md:p-12 rounded-xl shadow-xl">
          <Mail className="h-12 w-12 text-[#028475] mb-6 mx-auto" />
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Be the First to Know About Upcoming StreamLnk Engagements
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Stay Notified About Future Events. Subscribe to our dedicated Events & Webinars mailing list.
          </p>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Input
                type="email"
                placeholder="Your Email Address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full p-3 text-lg border-gray-300 focus:border-[#004235] focus:ring-[#004235] rounded-md placeholder-gray-500"
                aria-label="Email for event updates"
              />
            </div>

            <div className="space-y-3 text-left">
              <p className="text-sm font-medium text-gray-700 mb-2 text-center">Optional: Tell us your interests</p>
              <div className="flex items-center space-x-2 justify-center sm:justify-start">
                <Checkbox
                  id="interestWebinars"
                  checked={interestWebinars}
                  onCheckedChange={() => setInterestWebinars(!interestWebinars)}
                  aria-labelledby="interestWebinarsLabel"
                />
                <label
                  htmlFor="interestWebinars"
                  id="interestWebinarsLabel"
                  className="text-sm font-medium text-gray-700 cursor-pointer"
                >
                  Webinars
                </label>
              </div>
              <div className="flex items-center space-x-2 justify-center sm:justify-start">
                <Checkbox
                  id="interestInPerson"
                  checked={interestInPerson}
                  onCheckedChange={() => setInterestInPerson(!interestInPerson)}
                  aria-labelledby="interestInPersonLabel"
                />
                <label
                  htmlFor="interestInPerson"
                  id="interestInPersonLabel"
                  className="text-sm font-medium text-gray-700 cursor-pointer"
                >
                  Local In-Person Events
                </label>
              </div>
              <div className="flex items-center space-x-2 justify-center sm:justify-start">
                <Checkbox
                  id="interestIndustry"
                  checked={interestIndustry}
                  onCheckedChange={() => setInterestIndustry(!interestIndustry)}
                  aria-labelledby="interestIndustryLabel"
                />
                <label
                  htmlFor="interestIndustry"
                  id="interestIndustryLabel"
                  className="text-sm font-medium text-gray-700 cursor-pointer"
                >
                  Specific Industry Events
                </label>
              </div>
            </div>

            <div>
              <Button
                type="submit"
                className="bg-[#004235] hover:bg-[#028475] text-white w-full text-lg py-3"
                size="lg"
              >
                SUBSCRIBE FOR EVENT UPDATES
                <Send className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
}