import { CheckCircle } from "lucide-react";

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Expand Globally with Financial Confidence
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[
              {
                title: "Reduced FX Costs",
                description: "Access more competitive exchange rates than typically offered by traditional banks."
              },
              {
                title: "Increased Transparency",
                description: "Clear visibility into exchange rates used for transactions."
              },
              {
                title: "Simplified Operations",
                description: "Manage international payments without needing multiple foreign currency bank accounts."
              },
              {
                title: "Faster Settlements",
                description: "Optimized payment routing can lead to quicker receipt of funds."
              },
              {
                title: "Enhanced Global Reach",
                description: "Easily transact with buyers and suppliers in their preferred currencies, removing a key barrier to international trade."
              },
              {
                title: "Improved Cash Flow Management",
                description: "Better predictability of landed costs and received amounts."
              },
              {
                title: "Reduced Administrative Burden",
                description: "Automated reconciliation and clear reporting simplify accounting."
              }
            ].map((benefit, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-start">
                  <div className="bg-[#028475]/10 p-2 rounded-full mr-4 mt-1">
                    <CheckCircle className="h-5 w-5 text-[#028475]" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-[#004235] text-lg mb-2">{benefit.title}</h3>
                    <p className="text-gray-700">{benefit.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}