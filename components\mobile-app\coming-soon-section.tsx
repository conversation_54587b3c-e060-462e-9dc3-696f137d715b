"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { sendEmail } from "@/app/actions/email-actions";
import { useState, useTransition } from "react";
import { Loader2 } from "lucide-react";

export default function ComingSoonSection() {
  const { toast } = useToast();
  const [isPending, startTransition] = useTransition();
  const [email, setEmail] = useState("");
  const [isIosUser, setIsIosUser] = useState(false);
  const [isAndroidUser, setIsAndroidUser] = useState(false);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!email) {
      toast({
        title: "Email Required",
        description: "Please enter your email address.",
        variant: "destructive",
      });
      return;
    }
    if (!isIosUser && !isAndroidUser) {
      toast({
        title: "Platform Selection Required",
        description: "Please select at least one platform (iOS or Android).",
        variant: "destructive",
      });
      return;
    }

    startTransition(async () => {
      // TODO: Update sendEmail in email-actions.ts to handle app notification type and data
      const result = await sendEmail({
        to: email,
        type: "welcome", // Placeholder type, needs proper implementation in email-actions.ts
        data: { isIosUser, isAndroidUser },
      });
      if (result.success) {
        toast({
          title: "Subscription Successful!",
          description: "We'll notify you about the app launch.",
        });
        setEmail("");
        setIsIosUser(false);
        setIsAndroidUser(false);
      } else {
        toast({
          title: "Subscription Failed",
          description: result.error || "Could not subscribe for notifications. Please try again.",
          variant: "destructive",
        });
      }
    });
  };

  return (
    <section id="notify-app-launch" className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            The StreamLnk Mobile App – Coming Soon to Your Device!
          </h2>
          <p className="text-lg text-gray-700 mb-2">
            We are diligently working on developing our native mobile applications for both iOS and Android to provide
            you with the best possible on-the-go experience.
          </p>
          <p className="text-md text-gray-600 font-semibold mb-6">Expected Launch Timeline: [Targeting launch in QX 202X]</p> {/* TODO: Update this date */}
          <p className="text-lg text-gray-700">
            Be the First to Know: Sign up below to receive exclusive updates on app development progress, beta testing
            opportunities, and official launch announcements.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="max-w-lg mx-auto bg-white p-8 rounded-lg shadow-md">
          <div className="space-y-6">
            <div>
              <Label htmlFor="email-notify" className="text-[#004235]">Your Email Address</Label>
              <Input 
                id="email-notify" 
                type="email" 
                placeholder="<EMAIL>" 
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="mt-1"
                disabled={isPending}
              />
            </div>
            <div className="space-y-3">
                <p className="text-sm font-medium text-[#004235]">Which platform(s) are you interested in?</p>
                <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                        <Checkbox 
                            id="ios-user" 
                            checked={isIosUser} 
                            onCheckedChange={(checked) => setIsIosUser(checked as boolean)}
                            disabled={isPending} 
                        />
                        <Label htmlFor="ios-user" className="text-gray-700">iOS User</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Checkbox 
                            id="android-user" 
                            checked={isAndroidUser} 
                            onCheckedChange={(checked) => setIsAndroidUser(checked as boolean)}
                            disabled={isPending} 
                        />
                        <Label htmlFor="android-user" className="text-gray-700">Android User</Label>
                    </div>
                </div>
            </div>
            <Button 
              type="submit" 
              className="w-full bg-[#004235] hover:bg-[#028475] text-white py-3 text-lg"
              disabled={isPending}
            >
              {isPending ? <Loader2 className="mr-2 h-5 w-5 animate-spin" /> : "Notify Me About App Launch"}
            </Button>
          </div>
        </form>
      </div>
    </section>
  );
}