"use client";

import Link from 'next/link';
import Image from 'next/image'; // Assuming you might want images for event cards
import { Button } from '@/components/ui/button';
import { CalendarClock, MapPin, Users, ChevronRight, ExternalLink } from 'lucide-react';

interface EventCardProps {
  slug: string;
  title: string;
  date: string;
  time: string;
  description: string;
  speaker?: string;
  imageUrl?: string; // Optional image for the card
  registrationLink: string;
  learnMoreLink?: string; // For events that might have a separate info page
}

const featuredEvents: EventCardProps[] = [
  {
    slug: "mastering-cross-border-logistics",
    title: "Mastering Cross-Border Logistics: A Deep Dive into Customs Automation",
    date: "November 20, 2023",
    time: "10:00 AM EST",
    description: "Join our experts for a live webinar on simplifying international customs clearance and avoiding common delays with StreamLnk StreamGlobe+.",
    speaker: "[Speaker Name], Head of Global Customs Solutions",
    imageUrl: "/images/placeholder-event-1.jpg", // Placeholder
    registrationLink: "#register-logistics-webinar",
  },
  {
    slug: "ai-in-industrial-procurement",
    title: "Driving Efficiency: The Future of AI in Industrial Procurement",
    date: "December 5, 2023",
    time: "2:00 PM GMT",
    description: "Explore how AI-powered tools like StreamIndex™ and iScore™ are transforming sourcing, pricing, and supplier vetting in the industrial sector.",
    speaker: "[Speaker Name], StreamLnk CTO",
    imageUrl: "/images/placeholder-event-2.jpg", // Placeholder
    registrationLink: "#register-ai-webinar",
    learnMoreLink: "/events/ai-procurement-details", // Example learn more link
  },
];

const upcomingEventsList = [
  {
    date: "November 20, 2023",
    title: "Mastering Cross-Border Logistics (Webinar)",
    type: "Webinar",
    location: "Online",
    link: "#register-logistics-webinar",
  },
  {
    date: "December 5, 2023",
    title: "Driving Efficiency: The Future of AI in Industrial Procurement (Webinar)",
    type: "Webinar",
    location: "Online",
    link: "#register-ai-webinar",
  },
  {
    date: "January 15-17, 2024",
    title: "Global Industrial Supply Chain Conference",
    type: "Trade Show",
    location: "Berlin, Germany",
    link: "/events/global-supply-chain-conference-details",
  },
];

const EventCard: React.FC<EventCardProps> = ({ title, date, time, description, speaker, imageUrl, registrationLink, learnMoreLink }) => {
  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden flex flex-col h-full">
      {imageUrl && (
        <div className="relative w-full h-48">
          <Image src={imageUrl} alt={title} fill className="object-cover" />
        </div>
      )}
      <div className="p-6 flex flex-col flex-grow">
        <h3 className="text-xl font-semibold text-[#004235] mb-2">{title}</h3>
        <div className="flex items-center text-sm text-gray-600 mb-1">
          <CalendarClock className="h-4 w-4 mr-2 text-[#028475]" />
          <span>{date}, {time}</span>
        </div>
        {speaker && (
          <div className="flex items-center text-sm text-gray-600 mb-3">
            <Users className="h-4 w-4 mr-2 text-[#028475]" />
            <span>Speaker: {speaker}</span>
          </div>
        )}
        <p className="text-gray-700 text-sm mb-4 flex-grow">{description}</p>
        <div className="mt-auto flex flex-col sm:flex-row gap-3">
          <Button className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto" asChild>
            <Link href={registrationLink}>REGISTER NOW</Link>
          </Button>
          {learnMoreLink && (
            <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto" asChild>
              <Link href={learnMoreLink}>LEARN MORE</Link>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default function UpcomingEventsSection() {
  return (
    <section id="upcoming-events" className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-3">
            Mark Your Calendar: Don't Miss These Opportunities
          </h2>
          <p className="text-lg text-gray-700">Upcoming Events & Webinars</p>
        </div>

        {/* Featured Events/Webinars Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {featuredEvents.map((event) => (
            <EventCard key={event.slug} {...event} />
          ))}
        </div>

        {/* Interactive Events Calendar Placeholder */}
        <div className="mb-16 p-8 bg-white rounded-lg shadow-md text-center">
          <h3 className="text-2xl font-semibold text-[#004235] mb-4">Interactive Events Calendar</h3>
          <p className="text-gray-600 mb-4">
            (Placeholder for an embedded calendar widget or a link to a full calendar page)
            <br />
            Display events by month, filterable by Event Type (Webinar, Conference, Trade Show, Workshop), Region, and Industry Focus.
            <br />
            Each calendar entry clickable for more details and registration.
          </p>
          <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white" asChild>
            <Link href="/events/calendar"> {/* Placeholder link to full calendar page */}
              VIEW FULL CALENDAR
              <ExternalLink className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>

        {/* List View of Upcoming Events */}
        <div className="mb-12">
          <h3 className="text-2xl font-semibold text-[#004235] mb-6 text-center md:text-left">List View of Upcoming Events</h3>
          <div className="overflow-x-auto bg-white rounded-lg shadow-md">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event Title</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location/Online</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details & Registration</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {upcomingEventsList.map((event, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{event.date}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[#004235]">{event.title}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{event.type}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                      {event.location === "Online" ? (
                        <span className="flex items-center">
                          <CalendarClock className="h-4 w-4 mr-1.5 text-green-600" /> Online
                        </span>
                      ) : (
                        <span className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1.5 text-blue-600" /> {event.location}
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <Link href={event.link} className="text-[#028475] hover:text-[#004235] font-medium flex items-center">
                        Details & Registration <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="text-center">
          <Button className="bg-[#004235] hover:bg-[#028475] text-white" size="lg" asChild>
            <Link href="/events/calendar"> {/* Placeholder link to full calendar page */}
              VIEW FULL EVENTS CALENDAR
            </Link>
          </Button>
        </div>

      </div>
    </section>
  );
}