import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON> } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Extend Your Reach, Enhance Your Offering: Integrate with the StreamLnk Ecosystem
            </h1>
            <p className="text-xl md:text-2xl text-gray-700 mb-8">
              StreamLnk is building an open, API-driven platform for industrial trade. We invite technology providers to connect their solutions, creating seamless workflows for our mutual customers and unlocking new value across the supply chain.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white transition-colors px-8 py-3" asChild>
                <Link href="#explore-api">
                  EXPLORE APIS <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white transition-colors px-8 py-3"
                asChild
              >
                <Link href="#developer-portal">VISIT PORTAL</Link>
              </Button>
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[450px] rounded-lg overflow-hidden shadow-xl mt-12 lg:mt-0">
            <Image
              src="/images/placeholder-tech-integrations.webp" // TODO: User to replace with a relevant image for technology integrations
              alt="StreamLnk Technology Integrations"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: User to replace with a relevant image for technology integrations */}
          </div>
        </div>
      </div>
    </section>
  );
}