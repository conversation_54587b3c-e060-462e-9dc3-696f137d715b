"use client"

import { CheckCircle } from "lucide-react"

import { WorkflowTimeline, WorkflowStep } from "@/components/ui/WorkflowTimeline";

export function WorkflowProcessSection() {
  const timelineSteps: WorkflowStep[] = [
    {
      number: 1,
      title: "Define Your Needs",
      description: "Use our intuitive search and filter tools to specify your exact material requirements."
    },
    {
      number: 2,
      title: "Explore Matches",
      description: "Instantly view a curated list of products and suppliers that meet your criteria."
    },
    {
      number: 3,
      title: "Compare & Evaluate",
      description: "Dive into detailed product specifications, certifications, and supplier profiles."
    },
    {
      number: 4,
      title: "Request Quotes",
      description: "Directly request quotes from selected suppliers through the platform."
    },
    {
      number: 5,
      title: "Source with Confidence",
      description: "Proceed to order with verified partners."
    }
  ];

  return (
    <WorkflowTimeline
      title="A Seamless Path from Search to Sourced"
      steps={timelineSteps}
    />
  )
}