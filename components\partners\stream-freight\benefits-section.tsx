import { Check } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export function BenefitsSection() {
  const benefits = [
    "Access a Live Bid Pool: Tap into a continuous stream of shipment opportunities from E-Stream suppliers and MyStreamLnk buyers.",
    "Gain Real-Time Visibility: Monitor shipment statuses from pickup to delivery, ensuring transparency for all stakeholders.",
    "Streamline Document Management: Easily upload and manage crucial documents like insurance, permits, and invoices within a secure system.",
    "Transparent Earnings & Payment Tracking: Review payment timelines, access detailed earnings history, and manage your financials efficiently.",
    "Stay Ahead with AI-Powered Reminders: Our smart system sends automated reminders for expiring documents, ensuring you remain compliant and operational.",
    "Integrate Seamlessly: Connect directly with customs, packaging, and warehousing workflows within the StreamLnk ecosystem for smoother operations.",
  ]

  return (
    <section className="py-16 bg-white" id="learn-more">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-8 text-center">Why Join StreamFreight?</h2>
        <p className="text-lg text-gray-700 max-w-4xl mx-auto mb-12 text-center">
          StreamFreight is StreamLnk's dedicated portal designed for the backbone of the industrial supply chain: our
          freight and rail partners. Whether you operate a large trucking fleet, specialize in rail transport, or manage
          regional dispatch, StreamFreight connects you directly to a consistent flow of verified shipment jobs. We
          empower you to:
        </p>

        <div className="grid md:grid-cols-2 gap-6">
          {benefits.map((benefit, index) => (
            <Card key={index} className="border-l-4 border-l-[#028475]">
              <CardContent className="p-6 flex">
                <Check className="h-6 w-6 text-[#028475] mr-4 flex-shrink-0 mt-1" />
                <p className="text-gray-700">{benefit}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <p className="text-lg text-gray-700 max-w-4xl mx-auto mt-8 text-center">
          By joining StreamFreight, you become an essential part of a network delivering critical materials to thousands
          of buyers and suppliers worldwide.
        </p>
      </div>
    </section>
  )
}
