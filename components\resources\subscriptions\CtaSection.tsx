"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, MessageSquare, BarChart3 } from "lucide-react";

export default function CtaSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Don't Just Participate in the Market – Understand It. Lead It. With StreamResources+.
          </h2>
          <p className="text-xl text-gray-700 mb-10">
            Ready to Unlock Your Data Advantage?
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/request-demo?solution=streamresources-plus&source=cta-bottom">
                REQUEST DEMO
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>

            <Button
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="#subscription-tiers"> {/* Links to the tiers section on the same page */}
                VIEW PLANS
                <BarChart3 className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            
            <Button
              variant="link"
              className="text-[#028475] hover:text-[#004235] w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/contact-us?interest=streamresources-specialist">
                TALK TO SPECIALIST
                <MessageSquare className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}