"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';

export default function HowIscoreEmpowersSection() {
  const benefits = [
    {
      icon: <Eye className="h-10 w-10 text-[#004235] mb-3" />,
      userType: "For Buyers (MyStreamLnk):",
      points: [
        "Select Suppliers with Confidence: Easily identify reliable, high-performing suppliers based on their objective iScore™.",
        "Reduce Risk: Minimize exposure to poor quality, late deliveries, or non-compliant suppliers."
      ]
    },
    {
      icon: <TrendingUp className="h-10 w-10 text-[#004235] mb-3" />,
      userType: "For Suppliers (E-Stream):",
      points: [
        "Build Your Reputation: A high iScore™ serves as a trusted credential, attracting more buyers.",
        "Identify Areas for Improvement: Understand how your performance is perceived and where you can enhance your operations."
      ]
    },
    {
      icon: <Handshake className="h-10 w-10 text-[#004235] mb-3" />,
      userType: "For Service Providers (StreamFreight, StreamGlobe+, StreamPak):",
      points: [
        "Become a Preferred Partner: High iScores can lead to more job assignments and better tier benefits.",
        "Demonstrate Reliability: Showcase your commitment to quality service within the StreamLnk ecosystem."
      ]
    },
    {
      icon: <Users className="h-10 w-10 text-[#004235] mb-3" />,
      userType: "For All Platform Users:",
      points: [
        "Increased Trust & Transparency: Fosters a more reliable and accountable trading environment.",
        "Reduced DueDiligence Time: Provides a quick, data-backed assessment of potential partners."
      ]
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">
            How iScore™ Empowers You
          </h2>
          <p className="text-xl text-gray-700 mb-12 text-center">
            Make Informed Decisions, Mitigate Risk, Foster Quality Partnerships
          </p>

          <div className="grid md:grid-cols-2 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="bg-[#f3f4f6] p-6 rounded-lg shadow-md border border-gray-200 flex flex-col items-center text-center">
                {benefit.icon}
                <h3 className="text-xl font-semibold text-[#004235] mb-3">{benefit.userType}</h3>
                <ul className="space-y-2 text-gray-600 text-sm list-disc list-inside text-left">
                  {benefit.points.map((point, pIndex) => (
                    <li key={pIndex}>{point}</li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}