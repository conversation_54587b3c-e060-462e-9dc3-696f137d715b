"use client";

"use client";

import React from 'react'; // Added React for React.cloneElement
import { MessageSquare, FileSearch, TestTubeDiagonal, Rocket, PlayCircle, Settings2 } from 'lucide-react';
import { Timeline } from "@/components/ui/timeline";

const processSteps = [
  {
    icon: <MessageSquare /> /* className will be applied by adapter */,
    title: "Initial Discussion & Agreement",
    description: "Our Global Forwarding team discusses partnership terms and technical requirements."
  },
  {
    icon: <FileSearch /> /* className will be applied by adapter */,
    title: "Technical Scoping & API Review",
    description: "Joint review of API documentation and data mapping."
  },
  {
    icon: <TestTubeDiagonal /> /* className will be applied by adapter */,
    title: "Sandbox Development & Testing",
    description: "Collaborative testing of API connections in a dedicated sandbox environment."
  },
  {
    icon: <PlayCircle /> /* className will be applied by adapter */,
    title: "Pilot Phase (Optional)",
    description: "Live testing with a limited volume of StreamLnk shipments."
  },
  {
    icon: <Rocket /> /* className will be applied by adapter */,
    title: "Production Go-Live",
    description: "Full activation of API integrations."
  },
  {
    icon: <Settings2 /> /* className will be applied by adapter */,
    title: "Ongoing Monitoring & Support",
    description: "Continuous monitoring of API performance and dedicated technical support."
  }
];

export default function OnboardingProcessSection() {
  // Ensure icons have text-white for consistency with the Timeline component's icon background
  const adaptedProcessSteps = processSteps.map(step => ({
    ...step,
    icon: React.cloneElement(step.icon, { className: "h-8 w-8 text-white" }),
  }));

  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]"> {/* Changed background to match get-started-section.tsx */}
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          {/* Optional: Add a small line element like in get-started-section if desired */}
          {/* <div className="flex items-center justify-center mb-4">
            <div className="w-10 h-1 bg-[#028475]"></div>
          </div> */}
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6"> {/* Changed text color */}
            A Structured Path to Seamless Connectivity
          </h2>
          <p className="text-xl text-[#028475] font-semibold"> {/* Ensured text color matches */}
            The Onboarding & Integration Process
          </p>
          {/* Optional: keep or remove the underline based on desired consistency */}
          <div className="w-20 h-1 bg-[#028475] mx-auto mt-2"></div>
        </div>

        <div className="max-w-4xl mx-auto">
          <Timeline steps={adaptedProcessSteps} />
        </div>
      </div>
    </section>
  );
}