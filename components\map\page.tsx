"use client"

import { useRef, useMemo } from "react"
import * as d3 from "d3"
import { TransformWrapper, TransformComponent } from "react-zoom-pan-pinch"
import { Globe } from "lucide-react"

// Import components
import { MapHeader } from "@/components/map/map-header"
import { RealTimeControls } from "@/components/map/real-time-controls"
import { MapControls } from "@/components/map/map-controls"
import { MapCanvas } from "@/components/map/map-canvas"
import { SidebarLayout } from "@/components/map/layout/sidebar-layout"
import { OverlayPanels } from "@/components/map/layout/overlay-panels"
import { But<PERSON> } from "@/components/ui/button"

// Import hooks
import { useMapData } from "@/hooks/use-map-data"
import { useMapState } from "@/hooks/use-map-state"
import { useRealTimeData } from "@/hooks/use-real-time-data"

// Import data and types
import { partners, routes, regions } from "@/data/map-data"
import type { Region } from "@/types/map-types"

export default function InteractiveGlobalMap() {
  const transformRef = useRef<any>(null)

  // Custom hooks for data and state management
  const { worldData, isLoading } = useMapData()
  const mapState = useMapState()
  const { realTimeData } = useRealTimeData({
    isRealTimeEnabled: mapState.isRealTimeEnabled,
    updateInterval: mapState.updateInterval,
    animationSpeed: mapState.animationSpeed,
    showAssets: mapState.showAssets,
  })

  // Map dimensions
  const width = 1200
  const height = 1000

  // Stable projection
  const projection = useMemo(
    () =>
      d3
        .geoMercator()
        .scale(150)
        .translate([width / 2, height / 2]),
    [width, height],
  )

  // Filtered data
  const filteredPartners = useMemo(
    () => partners.filter((partner) => partner.services.some((service) => mapState.activeServices.has(service))),
    [mapState.activeServices],
  )

  const filteredRoutes = useMemo(
    () =>
      routes.filter(
        (route) =>
          mapState.showRoutes &&
          route.services.some((service) => mapState.activeServices.has(service)) &&
          mapState.routeIntensityFilter.includes(route.intensity),
      ),
    [mapState.showRoutes, mapState.activeServices, mapState.routeIntensityFilter],
  )

  const filteredAssets = useMemo(
    () =>
      realTimeData.assets.filter((asset) => {
        const route = routes.find((r) => r.id === asset.routeId)
        return route && route.services.some((service) => mapState.activeServices.has(service))
      }),
    [realTimeData.assets, mapState.activeServices],
  )

  const handleRegionClick = (region: Region) => {
    mapState.setSelectedRegion(region.id)

    if (transformRef.current) {
      const [centerX, centerY] = projection(region.center) || [0, 0]
      const scale = region.zoom

      const x = width / 2 - centerX * scale
      const y = height / 2 - centerY * scale

      transformRef.current.setTransform(x, y, scale, 500)
      mapState.setCurrentZoom(scale)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#EEF4EE] flex items-center justify-center">
        <div className="text-center">
          <Globe className="w-12 h-12 text-[#52AAA3] animate-spin mx-auto mb-4" />
          <p className="text-slate-700 text-lg">Loading world map data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#EEF4EE] flex flex-col overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#EEF4EE] via-slate-50 to-[#EEF4EE]" />

      {/* Pulse animation styles */}
      <style jsx>{`
        @keyframes pulse {
          0%, 100% { opacity: 0.6; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.1); }
        }
      `}</style>

      {/* Header - Full Width */}
      <header className="relative z-10 p-6 border-b border-slate-200 bg-white/80 backdrop-blur-sm">
        <MapHeader
          selectedPartner={mapState.selectedPartner}
          selectedRoute={mapState.selectedRoute}
          selectedAsset={mapState.selectedAsset}
          isRealTimeEnabled={mapState.isRealTimeEnabled}
        />
      </header>

      {/* Main Content Grid */}
      <div
        className="flex-1 grid relative z-10"
        style={{
          gridTemplateColumns: `${mapState.isLeftSidebarVisible ? "300px" : "0"} 1fr ${mapState.isRealTimeControlsMinimized ? "0" : "300px"}`,
        }}
      >
        {/* Left Sidebar */}
        {mapState.isLeftSidebarVisible && (
          <SidebarLayout
            isLeftSidebarVisible={mapState.isLeftSidebarVisible}
            isRegionalNavMinimized={mapState.isRegionalNavMinimized}
            isLiveEventsMinimized={mapState.isLiveEventsMinimized}
            isStatusPanelMinimized={mapState.isStatusPanelMinimized}
            selectedRegion={mapState.selectedRegion}
            activeServices={mapState.activeServices}
            realTimeData={realTimeData}
            filteredAssets={filteredAssets}
            routeIntensityFilter={mapState.routeIntensityFilter}
            currentZoom={mapState.currentZoom}
            isRealTimeEnabled={mapState.isRealTimeEnabled}
            showRoutes={mapState.showRoutes}
            showAssets={mapState.showAssets}
            showEvents={mapState.showEvents}
            onRegionClick={handleRegionClick}
            setIsRegionalNavMinimized={mapState.setIsRegionalNavMinimized}
            setIsLiveEventsMinimized={mapState.setIsLiveEventsMinimized}
            setIsStatusPanelMinimized={mapState.setIsStatusPanelMinimized}
          />
        )}

        {/* Floating Left Sidebar Toggle (when completely hidden) */}
        {!mapState.isLeftSidebarVisible && (
          <div className="fixed left-4 top-1/2 transform -translate-y-1/2 z-30">
            <div className="bg-white/90 backdrop-blur-sm border border-slate-200 rounded-lg shadow-lg p-2">
              <div className="flex flex-col gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => mapState.setIsRegionalNavMinimized(false)}
                  className="justify-start text-xs text-slate-600 hover:text-slate-900 w-20"
                >
                  📍
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => mapState.setIsLiveEventsMinimized(false)}
                  className="justify-start text-xs text-slate-600 hover:text-slate-900 w-20"
                >
                  🚨
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => mapState.setIsStatusPanelMinimized(false)}
                  className="justify-start text-xs text-slate-600 hover:text-slate-900 w-20"
                >
                  📊
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Center Map Area */}
        <div className="bg-white">
          <div className="h-[calc(100vh-120px)] relative">
            <TransformWrapper
              ref={transformRef}
              initialScale={1}
              minScale={0.5}
              maxScale={8}
              centerOnInit={true}
              wheel={{ step: 0.1 }}
              pinch={{ step: 5 }}
              doubleClick={{ disabled: false }}
              onTransformed={(ref) => {
                mapState.setCurrentZoom(ref.state.scale)
              }}
            >
              {({ zoomIn, zoomOut, resetTransform }) => (
                <>
                  {/* Map Controls */}
                  <MapControls
                    showRoutes={mapState.showRoutes}
                    showAssets={mapState.showAssets}
                    onZoomIn={zoomIn}
                    onZoomOut={zoomOut}
                    onReset={() => {
                      resetTransform()
                      mapState.setSelectedRegion(null)
                      mapState.setCurrentZoom(1)
                    }}
                    onToggleRoutes={() => mapState.setShowRoutes(!mapState.showRoutes)}
                    onToggleAssets={() => mapState.setShowAssets(!mapState.showAssets)}
                  />

                  {/* Map SVG */}
                  <TransformComponent
                    wrapperClass="w-full h-full flex items-center justify-center"
                    contentClass="cursor-grab active:cursor-grabbing"
                  >
                    <MapCanvas
                      worldData={worldData}
                      width={width}
                      height={height}
                      currentZoom={mapState.currentZoom}
                      hoveredPartner={mapState.hoveredPartner}
                      selectedPartner={mapState.selectedPartner}
                      hoveredRoute={mapState.hoveredRoute}
                      selectedRoute={mapState.selectedRoute}
                      selectedAsset={mapState.selectedAsset}
                      showRoutes={mapState.showRoutes}
                      showAssets={mapState.showAssets}
                      showHeatmap={mapState.showHeatmap}
                      showEvents={mapState.showEvents}
                      showRegionalStats={mapState.showRegionalStats}
                      animationSpeed={mapState.animationSpeed}
                      filteredPartners={filteredPartners}
                      filteredRoutes={filteredRoutes}
                      filteredAssets={filteredAssets}
                      realTimeData={realTimeData}
                      onPartnerClick={mapState.setSelectedPartner}
                      onRouteClick={mapState.setSelectedRoute}
                      onAssetClick={mapState.setSelectedAsset}
                      onPartnerHover={mapState.setHoveredPartner}
                      onRouteHover={mapState.setHoveredRoute}
                    />
                  </TransformComponent>
                </>
              )}
            </TransformWrapper>
          </div>
        </div>

        {/* Right Sidebar */}
        {!mapState.isRealTimeControlsMinimized && (
          <div className="bg-white/80 backdrop-blur-sm border-l border-slate-200 overflow-y-auto max-h-[calc(100vh-120px)]">
            <RealTimeControls
              isRealTimeEnabled={mapState.isRealTimeEnabled}
              showAssets={mapState.showAssets}
              showHeatmap={mapState.showHeatmap}
              showEvents={mapState.showEvents}
              showRegionalStats={mapState.showRegionalStats}
              updateInterval={mapState.updateInterval}
              activeServices={mapState.activeServices}
              realTimeData={realTimeData}
              isMinimized={mapState.isRealTimeControlsMinimized}
              onToggleRealTime={() => mapState.setIsRealTimeEnabled(!mapState.isRealTimeEnabled)}
              onToggleAssets={mapState.setShowAssets}
              onToggleHeatmap={mapState.setShowHeatmap}
              onToggleEvents={mapState.setShowEvents}
              onToggleRegionalStats={mapState.setShowRegionalStats}
              onUpdateIntervalChange={mapState.setUpdateInterval}
              onToggleService={mapState.toggleService}
              onToggleAllServices={mapState.toggleAllServices}
              onToggleMinimized={() => mapState.setIsRealTimeControlsMinimized(!mapState.isRealTimeControlsMinimized)}
            />
          </div>
        )}

        {/* Floating Right Sidebar Toggle (when hidden) */}
        {mapState.isRealTimeControlsMinimized && (
          <div className="fixed right-4 top-1/2 transform -translate-y-1/2 z-30">
            <div className="bg-white/90 backdrop-blur-sm border border-slate-200 rounded-lg shadow-lg p-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => mapState.setIsRealTimeControlsMinimized(false)}
                className="text-slate-600 hover:text-slate-900"
              >
                ⚙️
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Overlay Panels for Selected Items */}
      <OverlayPanels
        selectedAsset={mapState.selectedAsset}
        selectedRoute={mapState.selectedRoute}
        selectedPartner={mapState.selectedPartner}
        filteredAssets={filteredAssets}
        onCloseAsset={() => mapState.setSelectedAsset(null)}
        onCloseRoute={() => mapState.setSelectedRoute(null)}
        onClosePartner={() => mapState.setSelectedPartner(null)}
        onSelectAsset={mapState.setSelectedAsset}
      />
    </div>
  )
}
