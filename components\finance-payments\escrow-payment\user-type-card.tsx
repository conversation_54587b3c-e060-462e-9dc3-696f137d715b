import type { ReactNode } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"

interface UserTypeCardProps {
  icon: ReactNode
  title: string
  description: string
}

export default function UserTypeCard({ icon, title, description }: UserTypeCardProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
      <CardHeader className="flex flex-col items-center text-center">
        <div className="p-3 rounded-full bg-[#004235]/10 mb-4">{icon}</div>
        <h3 className="text-xl font-semibold text-[#004235]">{title}</h3>
      </CardHeader>
      <CardContent className="text-center">
        <p className="text-gray-600">{description}</p>
      </CardContent>
    </Card>
  )
}
