import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { LayoutDashboard, BellRing, DatabaseZap, Zap, UserCog } from 'lucide-react';

export default function WhyDashboardsSection() {
  const features = [
    {
      icon: <LayoutDashboard className="h-10 w-10 text-[#028475] mb-4" />,
      title: "At-a-Glance Overview",
      description: "Instantly see your most important metrics and tasks.",
    },
    {
      icon: <BellRing className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Proactive Management",
      description: "Quickly identify pending actions, alerts, and opportunities.",
    },
    {
      icon: <DatabaseZap className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Data-Driven Decisions",
      description: "Access real-time data to inform your daily operations and strategy.",
    },
    {
      icon: <Zap className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Improved Efficiency",
      description: "Reduce time spent searching for information; focus on action.",
    },
    {
      icon: <UserCog className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Personalized Experience",
      description: "Dashboards are tailored to your specific role and needs within the StreamLnk ecosystem.",
    },
  ];

  return (
    <section className="w-full py-12 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center space-y-4 text-center mb-10">
          <h2 className="text-2xl font-bold tracking-tighter sm:text-3xl md:text-4xl text-[#004235]">
            The Power of a Centralized View
          </h2>
          <p className="text-xl font-medium text-[#028475]">
            Why a Dedicated Dashboard is Essential for Modern Trade
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
          {features.map((feature, index) => (
            <Card key={index} className="shadow-md bg-white rounded-lg flex flex-col items-center text-center p-6">
              {feature.icon}
              <CardHeader className="p-0 mb-2">
                <CardTitle className="text-[#004235] text-xl">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <p className="text-gray-700 text-sm">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}