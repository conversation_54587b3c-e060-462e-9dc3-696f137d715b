import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, CheckCircle } from "lucide-react";

const portals = [
  "MyStreamLnk: For B2B Customers/Buyers.",
  "E-Stream: For Suppliers & Producers.",
  "MyStreamLnk+: For Sales Agents & Distributors.",
  "StreamFreight: For Land Freight Carriers.",
  "StreamGlobe & StreamGlobe+: For Sea Freight Carriers & Customs Agents.",
  "StreamPak: For Packaging & Warehouse Providers.",
  "StreamResources+: For Data & Analytics Subscribers."
];

export default function PortalsSection() {
  return (
    <section className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">Our Integrated Portal Network</h2>
          <p className="text-lg text-gray-700">
            Tailored Access for Every Participant
          </p>
        </div>
        <p className="text-lg text-gray-700 mb-10 max-w-4xl mx-auto">
          The StreamLnk ecosystem is accessed through a suite of role-specific portals, ensuring each user has the tools and information they need:
        </p>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 max-w-6xl mx-auto mb-12">
          {portals.map(portal => (
            <div key={portal} className="bg-white p-6 rounded-lg shadow-sm flex items-start hover:shadow-md transition-shadow">
              <CheckCircle className="h-5 w-5 text-[#028475] mr-3 mt-1 flex-shrink-0" />
              <span className="text-gray-700">{portal}</span>
            </div>
          ))}
        </div>
        <div className="text-center">
          <Button variant="link" className="text-[#028475] hover:text-[#004235] text-lg px-6 py-3" asChild>
            <Link href="/portals">Explore All StreamLnk Portals <ArrowRight className="ml-2 h-5 w-5" /></Link>
          </Button>
        </div>
      </div>
    </section>
  );
}