import { CheckCircle } from 'lucide-react';

// Placeholder for fetching results-specific content based on slug
// In a real app, this data would come from a CMS or database
const getResultsContent = async (slug: string) => {
  // Simulate fetching data
  await new Promise(resolve => setTimeout(resolve, 100));
  // Example content structure - adapt as needed
  if (slug === "sample-case-study-1") {
    return {
      title: "Measurable Impact & Enhanced Operations",
      introParagraph: "The adoption of StreamLnk's platform brought about significant, measurable improvements across Acme Corp's operations, alongside valuable qualitative benefits that enhanced their overall business performance.",
      quantitativeResults: [
        { metric: "25%", description: "Reduction in overall logistics costs within the first year." },
        { metric: "40%", description: "Improvement in on-time delivery rates." },
        { metric: "15+ Hours", description: "Saved per week on administrative tasks due to automation." },
        { metric: "3 New Markets", description: "Successfully entered due to streamlined international shipping processes." },
        { metric: "99.8%", description: "Compliance accuracy achieved with digital documentation." },
        { metric: "500 tCO2e", description: "Estimated annual reduction in carbon emissions through optimized routing." }
      ],
      qualitativeBenefits: [
        "Improved team morale due to reduced manual workload and frustration.",
        "Enhanced collaboration and communication with suppliers and logistics partners.",
        "Greater operational visibility, leading to proactive decision-making.",
        "Increased peace of mind with robust risk mitigation and compliance features.",
        "Stronger customer relationships built on reliability and transparency."
      ]
    };
  }
  // Fallback or default content
  return {
    title: "The Results & Benefits",
    introParagraph: "A detailed breakdown of the positive outcomes. Use bullet points, charts, or infographics to highlight key metrics.",
    quantitativeResults: [
      { metric: "XX%", description: "Cost Savings" },
      { metric: "YY Hours", description: "Time Savings" },
      { metric: "ZZ%", description: "Efficiency Gains" },
    ],
    qualitativeBenefits: [
      "Improved team morale.",
      "Better supplier/customer relationships.",
      "Enhanced visibility.",
      "Peace of mind."
    ]
  };
};

interface ResultsBenefitsSectionProps {
  caseStudySlug: string;
}

const ResultsBenefitsSection: React.FC<ResultsBenefitsSectionProps> = async ({ caseStudySlug }) => {
  const content = await getResultsContent(caseStudySlug);

  return (
    <section className="py-12 md:py-16 lg:py-20 bg-white">
      <div className="container mx-auto px-4 md:px-6">
        <h2 className="text-3xl md:text-4xl font-semibold text-[#004235] mb-8 text-center">
          {content.title}
        </h2>
        {content.introParagraph && (
          <p className="text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto text-center mb-12">
            {content.introParagraph}
          </p>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {content.quantitativeResults.map((item, index) => (
            <div key={index} className="bg-[#F2F2F2] p-6 rounded-lg shadow-lg text-center">
              <div className="text-4xl font-bold text-[#028475] mb-2">{item.metric}</div>
              <p className="text-md text-gray-700">{item.description}</p>
            </div>
          ))}
        </div>

        {content.qualitativeBenefits && content.qualitativeBenefits.length > 0 && (
          <div className="max-w-3xl mx-auto">
            <h3 className="text-2xl font-semibold text-[#004235] mb-6 text-center md:text-left">Qualitative Gains:</h3>
            <ul className="space-y-3">
              {content.qualitativeBenefits.map((benefit, index) => (
                <li key={index} className="flex items-start text-lg text-gray-700">
                  <CheckCircle className="w-6 h-6 text-[#028475] mr-3 flex-shrink-0 mt-1" />
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </section>
  );
};

export default ResultsBenefitsSection;