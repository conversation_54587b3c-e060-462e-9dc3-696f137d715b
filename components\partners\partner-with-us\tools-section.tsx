import { FileText, BookOpen, Globe, BarChart4, LayoutDashboard } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export function ToolsSection() {
  const tools = [
    {
      icon: <FileText className="h-10 w-10 text-[#028475]" />,
      title: "Built-in RFQ Generator",
      description: "Create and manage RFQs efficiently with client-linked history.",
    },
    {
      icon: <BookOpen className="h-10 w-10 text-[#028475]" />,
      title: "Comprehensive Onboarding & Training",
      description: "Portal walkthroughs, training materials, and ongoing guidance to maximize your effectiveness.",
    },
    {
      icon: <Globe className="h-10 w-10 text-[#028475]" />,
      title: "Multi-Language Client Support Materials",
      description: "Resources to help you communicate value to a diverse international client base.",
    },
    {
      icon: <BarChart4 className="h-10 w-10 text-[#028475]" />,
      title: "Exclusive Quarterly Territory Intelligence Reports",
      description:
        "Data-driven insights to help you understand market dynamics and identify opportunities in your region.",
    },
    {
      icon: <LayoutDashboard className="h-10 w-10 text-[#028475]" />,
      title: "Live KPI Dashboard",
      description:
        "A personalized dashboard providing a real-time overview of your buyer group's activity and your performance.",
    },
  ]

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Agent Tools & Support Provided by StreamLnk
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">We equip you for success:</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {tools.map((tool, index) => (
            <Card key={index} className="border border-gray-200 hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="mb-4">{tool.icon}</div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">{tool.title}</h3>
                <p className="text-gray-700">{tool.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
