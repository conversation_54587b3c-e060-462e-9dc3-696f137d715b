import { Timeline } from "@/components/ui/timeline";
import { FileText, ListChecks, CheckCircle2, Rocket, FilePlus } from 'lucide-react';

export function HowToJoinSection() {
  const steps = [
    {
      title: "Complete Onboarding Form",
      description: "Provide details about your company, services, and facilities.",
      icon: <FileText className="h-8 w-8 text-white" />,
    },
    {
      title: "Upload Documentation",
      description: "Submit your facility licenses, insurance documentation, and any relevant certifications.",
      icon: <FilePlus className="h-8 w-8 text-white" />,
    },
    {
      title: "Define Capabilities",
      description: "Specify your service offerings, material handling expertise, and geographic coverage.",
      icon: <ListChecks className="h-8 w-8 text-white" />,
    },
    {
      title: "Compliance Approval",
      description: "Our team will review your application and documentation for verification.",
      icon: <CheckCircle2 className="h-8 w-8 text-white" />,
    },
    {
      title: "Go Live",
      description: "Once approved, you'll start receiving StreamLnk job assignments and system-integrated shipment alerts.",
      icon: <Rocket className="h-8 w-8 text-white" />,
    },
  ];

  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="w-10 h-1 bg-[#028475]"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">How to Join StreamPak</h2>
        </div>

        <div className="max-w-4xl mx-auto">
          <Timeline steps={steps} />
        </div>
      </div>
    </section>
  );
}
