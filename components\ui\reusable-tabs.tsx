"use client"

import React, { useState, useEffect, useRef, useCallback } from "react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { type LucideIcon } from "lucide-react";

export interface ReusableTabData {
  id: string;
  title: string;
  icon?: LucideIcon; // Icon is optional
  contentComponent: React.ElementType;
  disabled?: boolean;
}

interface ReusableTabsProps {
  tabsData: ReusableTabData[];
  defaultTabId?: string;
  onTabChange?: (tabId: string) => void;
  className?: string;
  tabsListClassName?: string;
  tabTriggerClassName?: string;
  tabContentClassName?: string;
  underlineColor?: string; // Allow customizing underline color
  activeTabTextColor?: string;
  inactiveTabTextColor?: string;
  activeTabBgColor?: string;
  inactiveTabBgColor?: string;
  hoverTabBgColor?: string;
}

export default function ReusableTabs({
  tabsData,
  defaultTabId,
  onTabChange,
  className = "",
  tabsListClassName = "",
  tabTriggerClassName = "",
  tabContentClassName = "",
  underlineColor = "bg-[#004235]",
  activeTabTextColor = "text-[#004235]",
  inactiveTabTextColor = "text-[#028475]",
  activeTabBgColor = "bg-white",
  inactiveTabBgColor = "bg-[#f3f4f6]",
  hoverTabBgColor = "hover:bg-gray-200",
}: ReusableTabsProps) {
  const [activeTab, setActiveTab] = useState<string>(
    defaultTabId || (tabsData.length > 0 ? tabsData[0].id : "")
  );
  const [underlineStyle, setUnderlineStyle] = useState({ left: 0, width: 0 });
  const tabsListRef = useRef<HTMLDivElement>(null);
  const tabTriggerRefs = useRef<(HTMLButtonElement | null)[]>([]);

  const handleTabChange = (newTabId: string) => {
    setActiveTab(newTabId);
    if (onTabChange) {
      onTabChange(newTabId);
    }
  };

  const updateUnderline = useCallback(() => {
    if (tabsListRef.current && tabsData.length > 0) {
      const activeTabIndex = tabsData.findIndex((tab) => tab.id === activeTab);
      if (activeTabIndex === -1 && tabsData.length > 0) {
        // Fallback if activeTab is somehow not in tabsData
        setActiveTab(tabsData[0].id);
        return;
      }
      const activeTabRef = tabTriggerRefs.current[activeTabIndex];

      if (activeTabRef) {
        const tabsListRect = tabsListRef.current.getBoundingClientRect();
        const activeTabRect = activeTabRef.getBoundingClientRect();
        setUnderlineStyle({
          left: activeTabRect.left - tabsListRect.left,
          width: activeTabRect.width,
        });
      }
    }
  }, [activeTab, tabsData]);

  useEffect(() => {
    updateUnderline();
    window.addEventListener("resize", updateUnderline);
    return () => window.removeEventListener("resize", updateUnderline);
  }, [activeTab, updateUnderline]);

  // Ensure refs are collected and reset active tab if tabsData changes
  useEffect(() => {
    tabTriggerRefs.current = tabTriggerRefs.current.slice(0, tabsData.length);
    if (tabsData.length > 0 && !tabsData.find(tab => tab.id === activeTab)) {
      setActiveTab(tabsData[0].id);
    }
  }, [tabsData, activeTab]);

  if (!tabsData || tabsData.length === 0) {
    return null; // Or some placeholder/error message
  }

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className={`w-full ${className}`}>
      <TabsList
        ref={tabsListRef}
        className={`relative flex w-full mb-8 bg-transparent p-0 h-auto rounded-none ${tabsListClassName}`}
      >
        {tabsData.map((tab, index) => (
          <TabsTrigger
            key={tab.id}
            value={tab.id}
            disabled={tab.disabled}
            ref={(el) => {
              tabTriggerRefs.current[index] = el;
            }}
            className={`relative flex-1 items-center justify-center space-x-2 px-6 py-6 
                        ${inactiveTabTextColor} ${inactiveTabBgColor} ${hoverTabBgColor} 
                        hover:scale-105 transition-all duration-200 
                        data-[state=active]:${activeTabBgColor} data-[state=active]:shadow-none 
                        focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#004235] focus-visible:ring-offset-2 
                        font-medium data-[state=active]:font-semibold rounded-none 
                        data-[state=active]:${activeTabTextColor} ${tabTriggerClassName}`}
          >
            {tab.icon && (
              <tab.icon
                className={`h-5 w-5 flex-shrink-0 ${
                  activeTab === tab.id ? activeTabTextColor : inactiveTabTextColor
                }`}
              />
            )}
            <span>{tab.title}</span>
          </TabsTrigger>
        ))}
        <div
          className={`absolute bottom-0 h-[4px] ${underlineColor} transition-all duration-300 ease-in-out`}
          style={underlineStyle}
        />
      </TabsList>

      {tabsData.map((tab) => (
        <TabsContent key={tab.id} value={tab.id} className={tabContentClassName}>
          <tab.contentComponent />
        </TabsContent>
      ))}
    </Tabs>
  );
}