import { <PERSON><PERSON><PERSON> } from 'lucide-react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'

export default function CTASection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            StreamLnk: Where Verified Businesses Connect for Compliant Global Trade
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Our Unwavering Commitment to a Secure Ecosystem
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white"
              size="lg"
              asChild
            >
              <Link href="/solutions/risk-compliance">
                LEARN MORE ABOUT OUR COMPLIANCE STANDARDS
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>

            <Button
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
              size="lg"
              asChild
            >
              <Link href="/register">
                HOW TO REGISTER YOUR BUSINESS (SECURELY)
              </Link>
            </Button>
          </div>

          <div className="mt-4">
            <Button
              variant="link"
              className="text-[#028475] hover:text-[#004235]"
              size="lg"
              asChild
            >
              <Link href="/contact">
                CONTACT OUR COMPLIANCE TEAM FOR QUERIES
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}