"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"

export function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl">
          <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
            Unlock a World of Industrial Materials: Discover, Compare, and Source with Precision
          </h1>
          <p className="text-xl md:text-2xl text-[#028475] mb-8">
            Stop sifting through outdated catalogs and endless supplier calls. StreamLnk's AI-powered Product Discovery engine connects you to the exact materials you need, from a global network of verified suppliers, in minutes.
          </p>
          <Button 
            className="bg-[#004235] hover:bg-[#028475] text-white px-6"
            size="lg"
            asChild
          >
            <Link href="/mystreamlnk-signup">
              START DISCOVERING PRODUCTS NOW
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}