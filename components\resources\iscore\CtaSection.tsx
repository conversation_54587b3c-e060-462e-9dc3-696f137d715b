"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowR<PERSON> } from 'lucide-react'; // Removed BarChart2 and HelpCircle, only ArrowRight will be used

export default function CtaSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-3xl font-bold text-[#004235] mb-6">
          iScore™: Higher Standard Trade
        </h2>
        <p className="text-xl text-gray-700 mb-8 max-w-2xl mx-auto">
          Join an Ecosystem Where Trust and Performance are Measurable and Rewarded.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
            size="lg"
            asChild
          >
            <Link href="/request-demo?feature=iscore_detailed">
              REQUEST ISCORE™ DEMO
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>

          <Button
            variant="outline"
            className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
            size="lg"
            asChild
          >
            <Link href="/resources/guides/improving-iscore"> {/* Placeholder link */}
              IMPROVE ISCORE™
            </Link>
          </Button>
        </div>
        <div className="mt-6">
          <Button
            variant="link"
            className="text-[#028475] hover:text-[#004235]"
            asChild
          >
            <Link href="/solutions/streamresources#analytics"> {/* Placeholder link */}
              DISCOVER STREAMRESOURCES+
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}