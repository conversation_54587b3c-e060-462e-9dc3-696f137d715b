const steps = [
  {
    step: 1,
    title: "Register",
    description: "Sign up with your company license and VAT information.",
  },
  {
    step: 2,
    title: "Upload Products",
    description: "List your products with detailed specifications, and define your logistics terms.",
  },
  {
    step: 3,
    title: "Get Approved",
    description: "Our team will review your submission for compliance approval.",
  },
  {
    step: 4,
    title: "Start Transacting",
    description: "Once approved, you can begin quoting, participating in auctions, and transacting with global buyers.",
  },
]

export function JoinStepsSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">How to Join as an E-Stream Seller</h2>
          <p className="text-lg text-gray-700">
            Becoming a StreamLnk Verified Supplier on E-Stream is straightforward:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {steps.map((step, index) => (
            <div key={index} className="bg-white rounded-lg shadow-md p-6 relative">
              <div className="absolute top-0 right-0 bg-[#004235] text-white font-bold text-xl w-10 h-10 flex items-center justify-center rounded-tr-lg rounded-bl-lg">
                {step.step}
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-4 mt-4">{step.title}</h3>
              <p className="text-gray-700">{step.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
