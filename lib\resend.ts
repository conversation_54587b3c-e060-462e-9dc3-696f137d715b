import { Resend } from 'resend';

// Initialize Resend with API key from environment variable
// If API key is not provided, return null to handle gracefully in the application
export const resend = process.env.RESEND_API_KEY 
  ? new Resend(process.env.RESEND_API_KEY)
  : null;

// Export a helper function to check if Resend is configured
export const isResendConfigured = () => {
  return !!process.env.RESEND_API_KEY && !!resend;
};