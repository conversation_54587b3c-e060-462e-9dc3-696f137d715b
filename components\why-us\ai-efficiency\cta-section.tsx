import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowR<PERSON> } from "lucide-react"; // Kept ArrowRight, removed Info, BarChart3 as they are not in the reference style

export default function CtaSection() {
  return (
    <section className="py-16 bg-white"> {/* Changed background, standardized padding */}
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            Embrace Future Efficiency
          </h2>
          <p className="text-xl text-gray-700 mb-8"> {/* Matched reference P style */}
            StreamLnk: Your Partner in Intelligent Industrial Trade. We are committed to continuous innovation, constantly refining our AI models and expanding our intelligent automation capabilities.
          </p>
          {/* Removed secondary paragraph as per reference structure, combined key info into primary paragraph */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto" // Matched reference primary button
              size="lg" // Ensured size is lg
              asChild
            >
              <Link href="/request-demo"> 
                REQUEST AI DEMO
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button 
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto" // Matched reference outline button
              size="lg" // Ensured size is lg
              asChild
            >
              <Link href="/solutions/streamresources-plus"> 
                LEARN STREAMRESOURCES+
                {/* Icon removed as per reference outline button style */}
              </Link>
            </Button>
          </div>
          <div className="mt-6"> {/* Matched reference link button section */}
            <Button 
                variant="link"
                className="text-[#028475] hover:text-[#004235]" // Matched reference link button
                asChild
            >
                <Link href="/solutions/iscore"> 
                    EXPLORE ISCORE™ RISK
                    <ArrowRight className="ml-2 h-4 w-4" /> {/* Matched reference link button icon */}
                </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}