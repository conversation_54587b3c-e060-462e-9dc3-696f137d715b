import type { ReactNode } from "react"
import Image from "next/image"

interface DashboardFeatureProps {
  icon: ReactNode
  title: string
  description: string
  features: string[]
  imageSrc: string
  imageAlt: string
  imageRight?: boolean
}

export default function DashboardFeature({
  icon,
  title,
  description,
  features,
  imageSrc,
  imageAlt,
  imageRight = false,
}: DashboardFeatureProps) {
  return (
    <div className="grid gap-6 md:grid-cols-2 items-center">
      <div className={`space-y-6 ${imageRight ? "md:order-2" : ""}`}>
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-full bg-[#004235]/10">{icon}</div>
          <h3 className="text-2xl font-semibold text-[#004235]">{title}</h3>
        </div>
        <p className="text-gray-600">{description}</p>
        <ul className="space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0"
              >
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                <polyline points="22 4 12 14.01 9 11.01" />
              </svg>
              <span className="text-gray-600">{feature}</span>
            </li>
          ))}
        </ul>
      </div>
      <div
        className={`relative h-[250px] md:h-[300px] rounded-lg overflow-hidden shadow-lg ${imageRight ? "md:order-1" : ""}`}
      >
        <Image src={imageSrc || "/placeholder.svg"} alt={imageAlt} fill className="object-cover" />
      </div>
    </div>
  )
}
