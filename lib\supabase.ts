import { createClient } from "@supabase/supabase-js"

// Create a mock client that doesn't throw errors but doesn't do anything
const createMockClient = () => {
  const noOp = async () => ({ data: null, error: new Error("Supabase not configured") })
  const noOpAuth = {
    getUser: noOp,
    getSession: noOp,
    signInWithOtp: noOp,
    signInWithPassword: noOp,
    signOut: noOp,
    verifyOtp: noOp,
    updateUser: noOp,
    onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } }),
  }

  return {
    auth: noOpAuth,
    from: () => ({
      select: () => ({ data: null, error: new Error("Supabase not configured") }),
      insert: noOp,
      update: noOp,
      upsert: noOp,
      delete: noOp,
    }),
  }
}

// Function to validate URL
const isValidUrl = (urlString: string) => {
  try {
    new URL(urlString)
    return true
  } catch (e) {
    return false
  }
}

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ""
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ""

// Check if Supabase is properly configured
export const isSupabaseConfigured = () => {
  return isValidUrl(supabaseUrl) && supabaseAnonKey.length > 0
}

// Create the Supabase client or a mock client if not configured
export const supabase = isSupabaseConfigured()
  ? createClient(supabaseUrl, supabaseAnonKey)
  : (createMockClient() as ReturnType<typeof createClient>)

// Log warning if using mock client
if (!isSupabaseConfigured()) {
  console.warn(
    "Supabase client not properly configured. Please set valid NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY environment variables.",
  )
}

