import { Building2, Globe, CreditCard } from "lucide-react";
import UseCaseCard from "./use-case-card"; // Assuming this component will be moved or path adjusted

export default function IdealForSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Ideal For</h2>
          <p className="text-gray-600 max-w-3xl">
            Our multi-currency payment solutions are designed for various business needs.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <UseCaseCard
            icon={<Building2 className="h-10 w-10 text-[#028475]" />}
            title="Cross-Border Buyers & Distributors"
            description="Seamless FX handling for businesses operating across multiple markets."
          />
          <UseCaseCard
            icon={<Globe className="h-10 w-10 text-[#028475]" />}
            title="International Suppliers"
            description="Transact in local currencies while billing internationally with ease."
          />
          <UseCaseCard
            icon={<CreditCard className="h-10 w-10 text-[#028475]" />}
            title="Freight and Logistics Partners"
            description="Operate across multi-region contracts with simplified payment processes."
          />
        </div>
      </div>
    </section>
  );
}