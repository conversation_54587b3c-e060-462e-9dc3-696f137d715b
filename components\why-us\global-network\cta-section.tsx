import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowRight, Users } from "lucide-react";

export default function CtaSection() {
  return (
    <section className="py-16 bg-white"> {/* Changed background, standardized padding */}
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            Connecting the World of Industrial Materials, One Verified Partner at a Time
          </h2>
          <p className="text-xl text-gray-700 mb-8"> {/* Matched reference P style */}
            Building a Truly Global Digital Trade Community
          </p>
          <p className="text-lg text-gray-700 mb-8"> {/* Adjusted secondary paragraph style */}
            StreamLnk is more than just technology; it's a community of trusted participants committed to making global industrial trade more efficient, transparent, and secure. We are continuously expanding our network, vetting new partners, and strengthening our presence in key markets worldwide to ensure our users have the global reach and local support they need to succeed.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto" // Matched reference primary button classes
              size="lg" // Ensured size is lg
              asChild
            >
              <Link href="/register"> 
                JOIN GLOBAL NETWORK
                <ArrowRight className="ml-2 h-5 w-5" /> {/* Changed icon to ArrowRight */}
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}