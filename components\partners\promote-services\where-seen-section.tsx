import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"

export function WhereSeenSection() {
  const portals = [
    {
      name: "E-Stream",
      subtitle: "Supplier Portal",
      description:
        "When suppliers are looking for downstream partners to fulfill orders (e.g., freight, packaging for an export).",
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      name: "MyStreamLnk",
      subtitle: "Buyer Portal",
      description:
        "As buyers configure their shipments and require logistics, warehousing, customs, or compliance services.",
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      name: "StreamFreight",
      subtitle: "Freight Portal",
      description: "To shippers looking for specific carrier capabilities or regional expertise.",
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      name: "StreamPak",
      subtitle: "Packaging & Warehouse Portal",
      description: "When specific handling or storage needs arise that match your profile.",
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      name: "StreamGlobe",
      subtitle: "Customs Portal",
      description: "If specialized customs advisory or support is needed beyond standard clearance.",
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      name: "MyStreamLnk+",
      subtitle: "Agent Portal",
      description: "As agents build fulfillment solutions for their clients and require trusted service providers.",
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      name: "Service Provider Marketplace",
      subtitle: "Dedicated Section",
      description:
        "A dedicated section where users can search and filter for specific services by region, capability, and rating.",
      image: "/placeholder.svg?height=200&width=300",
    },
  ]

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Where Will Your Services Be Seen and Promoted?
          </h2>
          <p className="text-lg text-gray-700 mb-12 max-w-4xl">
            Your verified company profile, detailed capabilities, certifications, and operational badges will be
            strategically visible within the StreamLnk ecosystem, including:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {portals.map((portal, index) => (
              <Card
                key={index}
                className="overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow duration-300"
              >
                <div className="h-40 relative">
                  <Image src={portal.image || "/placeholder.svg"} alt={portal.name} fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#004235]/80 to-transparent flex flex-col justify-end p-4">
                    <h3 className="text-xl font-bold text-white">{portal.name}</h3>
                    <p className="text-[#028475] font-medium">{portal.subtitle}</p>
                  </div>
                </div>
                <CardContent className="p-5">
                  <p className="text-gray-600">{portal.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          <p className="text-lg text-gray-700 mt-8 max-w-4xl">
            You'll be directly visible to users at the precise moment they are creating shipments, looking for specific
            services, building quotes, or solving logistical challenges.
          </p>
        </div>
      </div>
    </section>
  )
}
