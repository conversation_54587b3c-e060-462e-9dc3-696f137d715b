"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowRight, FileText, MessageSquare } from 'lucide-react';

export default function CtaSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            Let's Collaborate to Build the Future of Industrial Commerce.
          </h2>
          <p className="text-xl md:text-2xl text-[#028475] mb-10">
            Ready to Join a Leading Global Trade Network?
          </p>
          <p className="text-lg text-gray-700 mb-12">
            Explore the diverse partnership opportunities available and discover how aligning with StreamLnk can accelerate your growth and impact.
          </p>

          <div className="flex flex-col sm:flex-row justify-center items-center gap-4 md:gap-6">
            <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white transition-colors text-lg px-8 py-3 w-full sm:w-auto" asChild>
              <Link href="#partnership-pathways"> {/* Placeholder link, update as needed */}
                View Opportunities
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white transition-colors text-lg px-8 py-3 w-full sm:w-auto" asChild>
              <Link href="/contact?subject=PartnershipDiscussion"> {/* Placeholder link */}
                Request Discussion
                <MessageSquare className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button size="lg" variant="link" className="text-[#028475] hover:text-[#004235] text-lg px-8 py-3 w-full sm:w-auto" asChild>
              <Link href="/partner-success-stories"> {/* Placeholder link */}
                Success Stories
                <FileText className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}