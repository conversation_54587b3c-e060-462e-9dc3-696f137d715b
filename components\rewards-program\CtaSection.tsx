"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { LogIn, UserPlus, MessageSquare } from "lucide-react";
import Link from "next/link";

export default function CtaSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            StreamLnk: Performance Recognized, Loyalty Rewarded.
          </h2>
          <p className="text-xl text-gray-700 mb-8">
            Our program thanks you for contributing to an efficient, transparent, and trusted global industrial trade network.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/login"> {/* Placeholder: Update with actual login portal link */}
                LOGIN PORTAL
                <LogIn className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button 
              variant="outline" 
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/partner-with-us"> {/* Placeholder: Update with actual partner page link */}
                BECOME PARTNER
                <UserPlus className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="mt-6">
            <Button 
              variant="link" 
              className="text-[#028475] hover:text-[#004235]"
              asChild
            >
              <Link href="/contact-support"> {/* Placeholder: Update with actual support page link */}
                CONTACT SUPPORT
                <MessageSquare className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}