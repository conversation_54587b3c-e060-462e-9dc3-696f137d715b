import { <PERSON><PERSON> } from "@/components/ui/button"
import { FileCheck, Landmark, Shield, TruckIcon, Zap } from "lucide-react"

export function SecurityComplianceSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="w-10 h-1 bg-[#028475]"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            Security & Compliance Standards: Our Commitment to Quality
          </h2>
          <p className="text-gray-700">
            StreamPak-certified partners are expected to meet and maintain stringent operational guidelines:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow border-t-4 border-[#004235]">
            <Shield className="h-12 w-12 text-[#028475] mb-6" />
            <h3 className="text-xl font-bold text-[#004235] mb-4">Verified Insurance</h3>
            <p className="text-gray-600">Valid liability and property insurance suitable for the goods handled.</p>
          </div>

          <div className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow border-t-4 border-[#004235]">
            <Landmark className="h-12 w-12 text-[#028475] mb-6" />
            <h3 className="text-xl font-bold text-[#004235] mb-4">Regional Compliance</h3>
            <p className="text-gray-600">Adherence to all local and regional certifications and operational permits.</p>
          </div>

          <div className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow border-t-4 border-[#004235]">
            <TruckIcon className="h-12 w-12 text-[#028475] mb-6" />
            <h3 className="text-xl font-bold text-[#004235] mb-4">Trained Personnel</h3>
            <p className="text-gray-600">
              Staff qualified and trained for material-specific handling and safety protocols.
            </p>
          </div>

          <div className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow border-t-4 border-[#004235]">
            <Zap className="h-12 w-12 text-[#028475] mb-6" />
            <h3 className="text-xl font-bold text-[#004235] mb-4">Loss/Damage Adherence</h3>
            <p className="text-gray-600">Commitment to our stringent tolerance limits (e.g., 0.1% loss/damage).</p>
          </div>

          <div className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow border-t-4 border-[#004235]">
            <FileCheck className="h-12 w-12 text-[#028475] mb-6" />
            <h3 className="text-xl font-bold text-[#004235] mb-4">Regular Document Updates</h3>
            <p className="text-gray-600">
              Mandatory and timely updates of all compliance and certification documents for continued portal access.
            </p>
          </div>

          <div className="bg-[#004235] p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow flex flex-col justify-center">
            <h3 className="text-xl font-bold text-white mb-4">Need Help With Compliance?</h3>
            <p className="text-gray-200 mb-6">
              Our team can guide you through the compliance requirements needed to join the StreamPak network.
            </p>
            <Button
              variant="outline"
              className="text-white border-white hover:text-[#004235]"
            >
              Contact Our Compliance Team
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
