"use client";

import { Bar<PERSON><PERSON><PERSON><PERSON>, CheckCircle2, DollarSign, MessageSquare, Users, TrendingUp, ShieldCheck } from 'lucide-react';

export default function WhatIsIscoreSection() {
  const keyDataInputs = [
    {
      icon: <BarChartBig className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />,
      title: "Operational Performance:",
      description: "Metrics: On-time delivery/readiness, order fulfillment accuracy, inventory availability (for suppliers), customs clearance speed, packaging quality, job acceptance rates (for service providers)."
    },
    {
      icon: <CheckCircle2 className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />,
      title: "Compliance & Documentation:",
      description: "Metrics: Validity and timeliness of submitted documents (licenses, certifications, insurance), adherence to platform terms, dispute resolution history."
    },
    {
      icon: <DollarSign className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />,
      title: "Financial Trustworthiness:",
      description: "Metrics (for Buyers): On-time payment history, BNPL repayment record. Metrics (for Suppliers/Service Providers): Financial stability indicators (from external data where permissible and integrated for premium reports)."
    },
    {
      icon: <MessageSquare className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />,
      title: "Communication & Responsiveness:",
      description: "Metrics: Response times to RFQs, order confirmations, platform alerts, and support interactions."
    },
    {
      icon: <Users className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />,
      title: "Platform-Sourced Feedback:",
      description: "Metrics: Aggregated and verified ratings from counterparties on the platform (e.g., buyer ratings of supplier product quality, StreamLnk Ops ratings of service provider efficiency)."
    }
  ];

  const iscoreOutput = [
    "A Composite Overall Score (e.g., numerical 1-100, or letter grade A-F).",
    "Detailed Sub-Scores for each category (Operational, Compliance, etc.).",
    "Score Trend Analysis (Improving, Stable, Declining).",
    "Anonymous Benchmarking against peers in the same category/region."
  ];

  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">
            Introducing iScore™ – StreamLnk's Standard for Partner Excellence
          </h2>
          <p className="text-xl text-gray-700 mb-10 text-center">
            What is iScore™? Your Data-Driven Indicator of Reliability.
          </p>
          <p className="text-md text-gray-600 mb-8 text-center">
            iScore™ is a proprietary, multi-faceted rating system developed by StreamLnk, powered by real-time data aggregated through our StreamResources+ engine. It provides a dynamic, objective score for every registered Supplier, Buyer, and Service Provider (Freight, Customs, Packaging/Warehousing) on our platform.
          </p>

          <div className="bg-white p-8 rounded-lg shadow-md border border-gray-200 mb-12">
            <h3 className="text-2xl font-semibold text-[#004235] mb-6">How iScore™ is Calculated – Key Data Inputs:</h3>
            <div className="space-y-6">
              {keyDataInputs.map((item, index) => (
                <div key={index} className="flex items-start">
                  {item.icon}
                  <div>
                    <h4 className="font-semibold text-gray-800">{item.title}</h4>
                    <p className="text-gray-600 text-sm">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white p-8 rounded-lg shadow-md border border-gray-200">
            <h3 className="text-2xl font-semibold text-[#004235] mb-6 flex items-center">
              <TrendingUp className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />
              The iScore™ Output:
            </h3>
            <ul className="space-y-3 list-disc list-inside text-gray-700">
              {iscoreOutput.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </div>

        </div>
      </div>
    </section>
  );
}