"use client";

import { CheckCircle } from 'lucide-react';

const benefits = [
  {
    title: "Expand Your Market Reach",
    description: "Access new customers, regions, and verticals through our integrated platform."
  },
  {
    title: "Increase Business Volume",
    description: "Connect with qualified leads and participate in a higher volume of transactions."
  },
  {
    title: "Enhance Operational Efficiency",
    description: "Leverage StreamLnk's digital tools and automated workflows to streamline your processes."
  },
  {
    title: "Boost Brand Visibility & Credibility",
    description: "Align your brand with an innovative leader in supply chain digitization and trusted trade."
  },
  {
    title: "Unlock New Revenue Streams",
    description: "Explore co-marketing, referral programs, integrated service models, or data partnerships."
  },
  {
    title: "Collaborate on Innovation",
    description: "Co-develop solutions that address critical industry challenges and create new value for users."
  },
  {
    title: "Gain Strategic Insights",
    description: "Access valuable market data and performance analytics through our platform."
  }
];

export default function ValuePropositionSection() {
  return (
    <section className="py-16 md:py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4 text-center">
            The Value of Strategic Alliance in a Connected World
          </h2>
          <p className="text-xl md:text-2xl text-[#028475] mb-12 text-center">
            Why Partner with the StreamLnk Ecosystem?
          </p>
          <p className="text-lg text-gray-700 mb-12 text-center max-w-3xl mx-auto">
            Partnering with StreamLnk offers a unique gateway to a rapidly growing global network of industrial buyers, suppliers, and service providers. By joining our ecosystem, you can:
          </p>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="bg-gray-50 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col">
                <div className="flex items-start mb-3">
                  <CheckCircle className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0 mt-1" />
                  <h3 className="text-xl font-semibold text-[#004235]">{benefit.title}</h3>
                </div>
                <p className="text-gray-600 text-sm flex-grow">
                  {benefit.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}