import { StandardizedTimeline } from "@/components/ui/standardized-timeline"
import { Database, BarChart3, LineChart, TrendingUp } from "lucide-react"

export default function DataTransformationProcessSection() {
  return (
    <section className="py-16 bg-white" id="process">
      <div className="container mx-auto px-4">
        <StandardizedTimeline 
          title="From Raw Data to Strategic Foresight"
          description="StreamLnk's market intelligence process is designed for impact:"
          steps={[
            {
              title: "Data Aggregation",
              description: "Securely collects and anonymizes vast amounts of real-time data from all StreamLnk portal interactions.",
              icon: <Database className="h-6 w-6 text-white" />
            },
            {
              title: "AI-Powered Analysis",
              description: "Employs advanced algorithms and machine learning to identify patterns, calculate benchmarks (StreamIndex™, iScore™), and generate predictive models.",
              icon: <BarChart3 className="h-6 w-6 text-white" />
            },
            {
              title: "Actionable Insights",
              description: "Delivers intelligence through intuitive dashboards, concise reports, and API feeds.",
              icon: <LineChart className="h-6 w-6 text-white" />
            },
            {
              title: "Empowered Decisions",
              description: "Enables users to optimize pricing, improve sourcing, mitigate risk, and enhance strategic planning.",
              icon: <TrendingUp className="h-6 w-6 text-white" />
            }
          ]}
        />
      </div>
    </section>
  )
}