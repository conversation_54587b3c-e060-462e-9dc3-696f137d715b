"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export default function FeaturesAndBenefitsSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#172d2d] mb-10 text-center">
            Empowering Your Team with Powerful Sourcing Tools
          </h2>

          <div className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
              <div className="md:col-span-1">
                <div className="bg-[#a4dcb4] p-6 rounded-lg h-full flex flex-col justify-center">
                  <h3 className="text-xl font-semibold text-[#172d2d] mb-2">Centralized Supplier Database</h3>
                  <p className="text-sm text-[#172d2d]">(E-Stream Network)</p>
                </div>
              </div>
              <div className="md:col-span-2">
                <div className="bg-[#a4dcb4] p-6 rounded-lg h-full">
                  <h4 className="font-semibold text-[#172d2d] mb-2">Benefit:</h4>
                  <p className="text-[#172d2d]">Save countless hours on supplier discovery. Access a wider pool of qualified global and local vendors.</p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
              <div className="md:col-span-1">
                <div className="bg-[#a4dcb4] p-6 rounded-lg h-full flex flex-col justify-center">
                  <h3 className="text-xl font-semibold text-[#172d2d] mb-2">AI-Driven Product Matching</h3>
                  <p className="text-sm text-[#172d2d]">& Recommendation</p>
                </div>
              </div>
              <div className="md:col-span-2">
                <div className="bg-[#a4dcb4] p-6 rounded-lg h-full">
                  <h4 className="font-semibold text-[#172d2d] mb-2">Benefit:</h4>
                  <p className="text-[#172d2d]">Quickly find materials that meet your exact technical and compliance requirements. Discover alternative or innovative material options.</p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
              <div className="md:col-span-1">
                <div className="bg-[#a4dcb4] p-6 rounded-lg h-full flex flex-col justify-center">
                  <h3 className="text-xl font-semibold text-[#172d2d] mb-2">Digital RFQ & Automated Quoting</h3>
                </div>
              </div>
              <div className="md:col-span-2">
                <div className="bg-[#a4dcb4] p-6 rounded-lg h-full">
                  <h4 className="font-semibold text-[#172d2d] mb-2">Benefit:</h4>
                  <p className="text-[#172d2d]">Reduce RFQ processing time from weeks to days or even hours. Receive comparable quotes faster.</p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
              <div className="md:col-span-1">
                <div className="bg-[#a4dcb4] p-6 rounded-lg h-full flex flex-col justify-center">
                  <h3 className="text-xl font-semibold text-[#172d2d] mb-2">Real-Time Price & Availability</h3>
                  <p className="text-sm text-[#172d2d]">(StreamIndex™ Insights)</p>
                </div>
              </div>
              <div className="md:col-span-2">
                <div className="bg-[#a4dcb4] p-6 rounded-lg h-full">
                  <h4 className="font-semibold text-[#172d2d] mb-2">Benefit:</h4>
                  <p className="text-[#172d2d]">Make informed purchasing decisions based on current market data. Negotiate with confidence.</p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
              <div className="md:col-span-1">
                <div className="bg-[#a4dcb4] p-6 rounded-lg h-full flex flex-col justify-center">
                  <h3 className="text-xl font-semibold text-[#172d2d] mb-2">Integrated Logistics & Landed Cost Calculation</h3>
                </div>
              </div>
              <div className="md:col-span-2">
                <div className="bg-[#a4dcb4] p-6 rounded-lg h-full">
                  <h4 className="font-semibold text-[#172d2d] mb-2">Benefit:</h4>
                  <p className="text-[#172d2d]">Understand the true cost of sourcing from different suppliers and regions upfront, including all freight, customs, and duties.</p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
              <div className="md:col-span-1">
                <div className="bg-[#a4dcb4] p-6 rounded-lg h-full flex flex-col justify-center">
                  <h3 className="text-xl font-semibold text-[#172d2d] mb-2">Supplier Performance & iScore™ Ratings</h3>
                </div>
              </div>
              <div className="md:col-span-2">
                <div className="bg-[#a4dcb4] p-6 rounded-lg h-full">
                  <h4 className="font-semibold text-[#172d2d] mb-2">Benefit:</h4>
                  <p className="text-[#172d2d]">Mitigate risk by choosing reliable suppliers with proven track records for quality and on-time delivery.</p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
              <div className="md:col-span-1">
                <div className="bg-[#a4dcb4] p-6 rounded-lg h-full flex flex-col justify-center">
                  <h3 className="text-xl font-semibold text-[#172d2d] mb-2">ESG & Sustainability Filters</h3>
                </div>
              </div>
              <div className="md:col-span-2">
                <div className="bg-[#a4dcb4] p-6 rounded-lg h-full">
                  <h4 className="font-semibold text-[#172d2d] mb-2">Benefit:</h4>
                  <p className="text-[#172d2d]">Easily identify and source sustainable materials (recycled content, bio-based, low PCF) to meet your corporate responsibility goals.</p>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-12 text-center">
            <Button
              className="bg-[#a4dcb4] hover:bg-[#172d2d] text-[#172d2d] hover:text-white border border-[#172d2d] px-6"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                Request Demo
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}