"use client";

import Image from 'next/image';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { BookOpen } from 'lucide-react'; // Or another relevant icon

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Decoding Global Commerce: Your A-Z StreamLnk Trade Glossary
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Navigate the complexities of industrial sourcing, logistics, finance, and compliance with ease. Our comprehensive glossary defines key industry terms and StreamLnk-specific concepts.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
                size="lg"
                asChild
              >
                <Link href="/resources/trade-glossary#browse-terms"> {/* Adjust link as needed */}
                  BROWSE GLOSSARY
                  <BookOpen className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              {/* Optional: Add a secondary button if desired, e.g., for suggesting a term */}
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/resources/trade-glossary-hero.webp" // Placeholder - suggest user to replace
              alt="StreamLnk Trade Glossary Hero Image"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: Consider adding a more relevant image or illustration for the trade glossary */}
          </div>
        </div>
      </div>
    </section>
  );
}