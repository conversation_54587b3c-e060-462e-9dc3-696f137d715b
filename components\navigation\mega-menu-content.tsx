"use client";

import Link from "next/link";
import * as Icons from "lucide-react";
import {
  MegaMenuContent,
  MegaMenuColumns,
  MegaMenuSection,
  MegaMenuItem,
  MegaMenuSeparator,
  MegaMenuFooter,
} from "../ui/mega-menu";
import { MegaMenuColumnStructure, MegaMenuCustomStructure } from "./types";

// Component for rendering mega menu content based on its structure type
export function RenderMegaMenuContent({ content }: { content: MegaMenuColumnStructure | MegaMenuCustomStructure }) {
  // Handle Column Type (kept for potential future use, not used by current data)
  if (content.type === 'columns') {
      return (
      <MegaMenuContent className="w-screen bg-[#f5f5f0] border-t border-border shadow-md"> {/* Apply default theme styles, changed background */}
        <div className={`mx-auto ${content.maxWidthClass} py-6`}>
          <MegaMenuColumns>
            {content.columns.map((section) => (
              <MegaMenuSection key={section.title} title={section.title}>
                {section.items.map((item) => (
                  <MegaMenuItem key={item.href} href={item.href} icon={item.icon} description={item.description}>
                    {item.label}
                  </MegaMenuItem>
                ))}
              </MegaMenuSection>
            ))}
          </MegaMenuColumns>
          {content.footer && (
            <>
              <MegaMenuSeparator />
              <MegaMenuFooter className="flex items-center justify-between mt-4">
                <span className="text-sm text-muted-foreground">{content.footer.text}</span>
                <Link href={content.footer.link} className="text-sm font-medium text-primary hover:underline">
                  {content.footer.linkText}
                </Link>
              </MegaMenuFooter>
            </>
          )}
        </div>
      </MegaMenuContent>
    );
  }

  // Handle Custom Layout Type (Styled like DHL example)
  if (content.type === 'custom') {
    return (
      <MegaMenuContent className="w-screen bg-[#f5f5f0] border-t border-border shadow-md"> {/* Use #f5f5f0 background, theme border/shadow, changed background */}
        <div className={`container mx-auto px-4 py-6 ${content.maxWidthClass}`}>
          <div className="grid grid-cols-1 md:grid-cols-[1fr_2fr] gap-8"> {/* Left col smaller */}

            {/* Left Column: Quick Links + Intro Text */}
            <div className="md:border-r md:pr-8 border-border"> {/* Apply theme border */}
              <h3 className="text-sm font-bold text-[#004235] mb-4 tracking-wide uppercase">
                Quick Links
              </h3>
               {/* Added Introductory Text based on content.title and content.description */}
               <div className="mb-6">
                 <h4 className="text-base font-semibold text-foreground mb-1">{content.title}</h4> {/* Use theme color */}
                 <p className="text-sm text-muted-foreground">{content.description}</p> {/* Use theme color */}
               </div>
              <div className="flex flex-col gap-4">
                {content.sidebarLinks.map(item => (
                  <Link key={item.href} href={item.href} className="group flex items-center gap-3 p-3 rounded-md hover:bg-muted transition-colors border border-border shadow-md hover:shadow-lg"> {/* Increased shadow density */}
                    {item.icon && <span className="text-[#004235] flex-shrink-0">{item.icon}</span>} {/* Ensure icon doesn't shrink */}
                    <span className="flex-grow font-medium text-foreground group-hover:text-[#004235]">{item.label}</span> {/* Use theme colors */}
                    <Icons.ChevronRight className="h-5 w-5 text-[#004235] opacity-70 group-hover:opacity-100 transition-opacity flex-shrink-0" /> {/* Ensure icon doesn't shrink */}
                  </Link>
                ))}
              </div>
            </div>

            {/* Right Area: Main Content */}
            <div>
              {/* Title and Description removed from here as they are now in the left column */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {content.mainColumns.map(section => (
                  <div key={section.title} className="flex flex-col">
                    <h4 className="text-lg font-semibold text-foreground mb-2">{section.title}</h4> {/* Use theme color */}
                    <div className="space-y-2 mb-3">
                      {section.items.map(item => (
                        <Link key={item.href} href={item.href} className="group flex items-center text-sm text-gray-800 hover:text-[#004235] transition-colors"> {/* Made text darker */}
                          <span>{item.label}</span>
                          <Icons.ArrowRight className="h-4 w-4 ml-auto text-[#004235] opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0" /> {/* Added ml-auto, ensure icon doesn't shrink */}
                        </Link>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              {/* Optional Bottom Grid */}
              {content.bottomGrid && (
                <div className="mt-6 pt-6 border-t border-border"> {/* Use theme border */}
                  <h3 className="text-lg font-semibold text-foreground mb-4">{content.bottomGrid.title}</h3> {/* Use theme color */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {content.bottomGrid.sections.map(section => (
                      <div key={section.title} className="flex flex-col">
                        <h4 className="text-base font-semibold text-foreground mb-2">{section.title}</h4> {/* Use theme color */}
                        <div className="space-y-1">
                          {section.links.map(link => (
                            <Link key={link.href + link.label} href={link.href} className="text-sm text-gray-800 hover:text-[#004235] transition-colors block"> {/* Made text darker, add block for spacing */}
                              {link.label}
                            </Link>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          {content.footer && (
            <div className="mt-6 pt-6 border-t border-border flex items-center justify-between">
              <span className="text-sm text-muted-foreground">{content.footer.text}</span>
              <Link href={content.footer.link} className="text-sm font-medium text-[#004235] hover:underline">
                {content.footer.linkText}
              </Link>
            </div>
          )}
        </div>
      </MegaMenuContent>
    );
  }

  return null; // Fallback, should not happen with defined types
}