import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Landmark, Shield, Zap } from "lucide-react"

export function KeyFeaturesSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="w-10 h-1 bg-[#028475]"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            Key Features & Benefits of the StreamPak Portal
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="flex flex-col p-6 border-l-4 border-[#028475] shadow-md hover:shadow-lg transition-shadow">
            <Bell className="h-10 w-10 text-[#004235] mb-4" />
            <h3 className="text-xl font-bold text-[#004235] mb-2">Automated Job Assignment Notifications</h3>
            <p className="text-gray-600">
              Receive assignments based on your geographical region, service capabilities, and specific material
              handling expertise.
            </p>
          </div>

          <div className="flex flex-col p-6 border-l-4 border-[#028475] shadow-md hover:shadow-lg transition-shadow">
            <BarChart className="h-10 w-10 text-[#004235] mb-4" />
            <h3 className="text-xl font-bold text-[#004235] mb-2">Real-Time Inventory & Status Updates</h3>
            <p className="text-gray-600">
              Update product handling progress (e.g., received, repackaging, ready for dispatch) and maintain visible
              inventory status.
            </p>
          </div>

          <div className="flex flex-col p-6 border-l-4 border-[#028475] shadow-md hover:shadow-lg transition-shadow">
            <Zap className="h-10 w-10 text-[#004235] mb-4" />
            <h3 className="text-xl font-bold text-[#004235] mb-2">Shortage & Issue Alert System</h3>
            <p className="text-gray-600">
              Immediately alert us on inbound shortages, damages, or any other issues encountered, with tools for
              documenting deviations.
            </p>
          </div>

          <div className="flex flex-col p-6 border-l-4 border-[#028475] shadow-md hover:shadow-lg transition-shadow">
            <Shield className="h-10 w-10 text-[#004235] mb-4" />
            <h3 className="text-xl font-bold text-[#004235] mb-2">Quality Control Integration</h3>
            <p className="text-gray-600">Utilize or integrate checklists for inbound/outbound shipment verification.</p>
          </div>

          <div className="flex flex-col p-6 border-l-4 border-[#028475] shadow-md hover:shadow-lg transition-shadow">
            <FileCheck className="h-10 w-10 text-[#004235] mb-4" />
            <h3 className="text-xl font-bold text-[#004235] mb-2">Document & Invoicing Portal</h3>
            <p className="text-gray-600">
              Upload necessary service agreements, insurance, and submit invoices for completed jobs.
            </p>
          </div>

          <div className="flex flex-col p-6 border-l-4 border-[#028475] shadow-md hover:shadow-lg transition-shadow">
            <Landmark className="h-10 w-10 text-[#004235] mb-4" />
            <h3 className="text-xl font-bold text-[#004235] mb-2">Contract Flexibility</h3>
            <p className="text-gray-600">
              Participate in per-job assignments or engage in longer-term service contracts.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
