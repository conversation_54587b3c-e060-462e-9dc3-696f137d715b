"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import {
  ShoppingCart,
  Truck,
  Ship,
  FileText as FileTextIcon, // Renamed to avoid conflict with Next.js FileText
  Warehouse,
  Briefcase,
  Settings2,
  DatabaseZap,
  Users,
  Megaphone,
  ArrowRight
} from 'lucide-react';

interface PartnershipProgram {
  id: string;
  category: string;
  type: string;
  forWho: string;
  benefits: string;
  linkText: string;
  linkHref: string;
  icon: React.ReactNode;
}

const programsData: PartnershipProgram[] = [
  {
    id: 'supplier-estream',
    category: 'Sell Your Products',
    type: 'Verified Supplier (E-Stream)',
    forWho: 'Producers & distributors of polymers, chemicals, energy, industrial materials.',
    benefits: 'Global buyer access, AI pricing tools, auction platform, integrated logistics.',
    linkText: 'LEARN MORE & REGISTER AS SUPPLIER',
    linkHref: '/register/supplier?source=partnership-opportunities',
    icon: <ShoppingCart className="h-8 w-8 text-[#028475]" />
  },
  {
    id: 'land-freight-streamfreight',
    category: 'Provide Logistics & Fulfillment Services',
    type: 'Land Freight Carrier (StreamFreight)',
    forWho: 'Trucking companies, rail operators, domestic transporters.',
    benefits: 'Access to live bid pool, streamlined job management, digital PODs, faster payments.',
    linkText: 'JOIN STREAMFREIGHT NETWORK',
    linkHref: '/register/carrier/land?source=partnership-opportunities',
    icon: <Truck className="h-8 w-8 text-[#028475]" />
  },
  {
    id: 'sea-freight-streamglobe',
    category: 'Provide Logistics & Fulfillment Services',
    type: 'Sea Freight Carrier (StreamGlobe)',
    forWho: 'Ocean carriers, NVOCCs.',
    benefits: 'API integration for schedules/bookings/tracking, access to StreamLnk cargo volume.',
    linkText: 'EXPLORE SEA FREIGHT INTEGRATION',
    linkHref: '/solutions/api-integration?focus=sea-freight&source=partnership-opportunities',
    icon: <Ship className="h-8 w-8 text-[#028475]" />
  },
  {
    id: 'customs-streamglobeplus',
    category: 'Provide Logistics & Fulfillment Services',
    type: 'Customs Clearance Agent (StreamGlobe+)',
    forWho: 'Licensed customs brokers.',
    benefits: 'Receive assignments, automated document flow, real-time status updates, POA tools.',
    linkText: 'BECOME A CUSTOMS AGENT PARTNER',
    linkHref: '/register/customs-agent?source=partnership-opportunities',
    icon: <FileTextIcon className="h-8 w-8 text-[#028475]" />
  },
  {
    id: 'packaging-streampak',
    category: 'Provide Logistics & Fulfillment Services',
    type: 'Packaging & Warehouse Provider (StreamPak)',
    forWho: '3PLs, contract packagers, warehouse operators.',
    benefits: 'Manage packaging/storage jobs, real-time inventory tools, compliance support.',
    linkText: 'LIST YOUR PACKAGING/WAREHOUSING SERVICES',
    linkHref: '/register/warehouse-packager?source=partnership-opportunities',
    icon: <Warehouse className="h-8 w-8 text-[#028475]" />
  },
  {
    id: 'agent-mystreamlnkplus',
    category: 'Represent StreamLnk & Drive Sales',
    type: 'Independent Agent / Regional Distributor (MyStreamLnk+)',
    forWho: 'Experienced sales professionals, regional distribution companies.',
    benefits: 'Manage client portfolios, facilitate trades, earn commissions, access marketing support.',
    linkText: 'JOIN OUR AGENT NETWORK',
    linkHref: '/register/agent?source=partnership-opportunities',
    icon: <Briefcase className="h-8 w-8 text-[#028475]" />
  },
  {
    id: 'tech-api-integration',
    category: 'Integrate & Enhance Technology',
    type: 'Technology & API Integration Partner',
    forWho: 'ERP/SCM software providers, Fintechs, compliance tech, data analytics firms.',
    benefits: 'Integrate your solutions with StreamLnk, offer enhanced value to mutual customers, co-marketing.',
    linkText: 'EXPLORE TECH PARTNERSHIPS & APIS',
    linkHref: '/solutions/api-integration?source=partnership-opportunities',
    icon: <Settings2 className="h-8 w-8 text-[#028475]" />
  },
  {
    id: 'data-insights-streamresourcesplus',
    category: 'Integrate & Enhance Technology',
    type: 'Data & Insights Partner (StreamResources+)',
    forWho: 'Market research firms, data providers, industry analysts.',
    benefits: 'Collaborate on enriching StreamLnk\'s data offerings, co-develop intelligence products, access unique trade data.',
    linkText: 'DISCUSS DATA PARTNERSHIPS',
    linkHref: '/contact-us?subject=DataPartnershipInquiry&source=partnership-opportunities',
    icon: <DatabaseZap className="h-8 w-8 text-[#028475]" />
  },
  {
    id: 'strategic-alliance',
    category: 'Strategic Growth & Innovation',
    type: 'Strategic Alliance Partner',
    forWho: 'Industry leaders, major corporations, influential associations.',
    benefits: 'Joint go-to-market initiatives, co-development of transformative solutions, shaping industry standards.',
    linkText: 'PROPOSE A STRATEGIC ALLIANCE',
    linkHref: '/contact-us?subject=StrategicAllianceProposal&source=partnership-opportunities',
    icon: <Users className="h-8 w-8 text-[#028475]" />
  },
  {
    id: 'auction-network',
    category: 'Strategic Growth & Innovation',
    type: 'Auction Network Participant',
    forWho: 'Suppliers looking to liquidate, Buyers seeking opportunistic deals, Agents facilitating for clients.',
    benefits: 'Efficient inventory movement, competitive pricing, access to time-sensitive offers.',
    linkText: 'LEARN MORE ABOUT STREAMLNK AUCTIONS',
    linkHref: '/solutions/auctions?source=partnership-opportunities',
    icon: <Megaphone className="h-8 w-8 text-[#028475]" />
  },
];

const groupByCategory = (programs: PartnershipProgram[]) => {
  return programs.reduce((acc, program) => {
    const category = program.category;
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(program);
    return acc;
  }, {} as Record<string, PartnershipProgram[]>);
};

export default function PartnershipProgramsSection() {
  const groupedPrograms = groupByCategory(programsData);

  return (
    <section id="partnership-programs" className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Find Your Ideal Partnership Pathway with StreamLnk
          </h2>
          <p className="text-xl text-[#028475] font-semibold">
            Explore Our Partnership Programs
          </p>
        </div>

        {Object.entries(groupedPrograms).map(([category, programs]) => (
          <div key={category} className="mb-12 md:mb-16">
            <h3 className="text-2xl md:text-3xl font-semibold text-[#004235] mb-8 text-center md:text-left border-b-2 border-[#028475] pb-3">
              {category}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {programs.map((program) => (
                <div key={program.id} className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 flex flex-col justify-between">
                  <div>
                    <div className="flex items-center mb-4">
                      {program.icon}
                      <h4 className="text-xl font-semibold text-[#004235] ml-3">{program.type}</h4>
                    </div>
                    <p className="text-sm text-gray-500 mb-2">
                      <span className="font-medium">For:</span> {program.forWho}
                    </p>
                    <p className="text-sm text-gray-700 mb-4 leading-relaxed">
                      <span className="font-medium">Benefits:</span> {program.benefits}
                    </p>
                  </div>
                  <Button
                    variant="link"
                    className="text-[#028475] hover:text-[#004235] justify-start p-0 mt-auto font-semibold group"
                    asChild
                  >
                    <Link href={program.linkHref}>
                      {program.linkText}
                      <ArrowRight className="ml-1.5 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </Link>
                  </Button>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}