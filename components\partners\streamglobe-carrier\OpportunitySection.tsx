"use client";

import { PackageOpen, Zap, Eye, FileText, BarChartBig, Users, MinusCircle } from 'lucide-react';

const opportunities = [
  {
    icon: <PackageOpen className="h-8 w-8 text-[#028475]" />,
    title: "Access New Cargo Volumes",
    description: "Tap into a growing stream of B2B shipments generated through our E-Stream and MyStreamLnk marketplaces."
  },
  {
    icon: <Zap className="h-8 w-8 text-[#028475]" />,
    title: "Automated Booking Processes",
    description: "Receive standardized booking requests via API, reducing manual intervention and errors."
  },
  {
    icon: <Eye className="h-8 w-8 text-[#028475]" />,
    title: "Enhanced Visibility for Shippers",
    description: "Provide your direct customers (who also use StreamLnk) and StreamLnk users with real-time vessel tracking and milestone updates through a unified platform."
  },
  {
    icon: <FileText className="h-8 w-8 text-[#028475]" />,
    title: "Streamlined Data Exchange",
    description: "Efficiently share schedules, Bill of Lading information, and arrival notices via secure API connections."
  },
  {
    icon: <BarChartBig className="h-8 w-8 text-[#028475]" />,
    title: "Improved Asset Utilization",
    description: "Gain better foresight into cargo demand on key trade lanes originating from the StreamLnk ecosystem."
  },
  {
    icon: <Users className="h-8 w-8 text-[#028475]" />,
    title: "Partnership with an Innovative Platform",
    description: "Align your services with a technology leader in industrial supply chain digitization."
  },
  {
    icon: <MinusCircle className="h-8 w-8 text-[#028475]" />,
    title: "Reduced Administrative Overhead",
    description: "Minimize manual communication for bookings and status updates."
  }
];

export default function OpportunitySection() {
  return (
    <section className="py-12 md:py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">
            Why Integrate Your Sea Freight Services with StreamLnk StreamGlobe?
          </h2>
          <p className="text-lg text-[#028475] font-semibold">
            The Opportunity for Sea Freight Carriers with StreamLnk
          </p>
          <div className="w-20 h-1 bg-[#028475] mx-auto mt-2 mb-6"></div>
          <p className="text-lg text-gray-700 leading-relaxed">
            StreamLnk is digitizing the end-to-end supply chain for high-volume industrial materials. By partnering with us, sea freight carriers can unlock significant advantages:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {opportunities.map((item, index) => (
            <div key={index} className="bg-[#f3f4f6] p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center text-center">
              <div className="mb-4 p-3 bg-white rounded-full shadow-sm">
                {item.icon}
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">{item.title}</h3>
              <p className="text-gray-600 text-sm leading-relaxed">{item.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}