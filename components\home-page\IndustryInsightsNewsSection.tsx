import Image from "next/image"
import { Chevron<PERSON>eft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export function IndustryInsightsNewsSection() {
  return (
    <div className="mb-16 md:mb-24">
      {/* Customer Testimonial Section */}
      <section className="bg-[#004235] py-24 md:py-32">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <div className="mb-12">
            <div className="text-6xl text-[#18b793] mb-8">"</div>
            <blockquote className="text-3xl md:text-4xl font-bold text-white mb-8 max-w-4xl mx-auto leading-relaxed">
              StreamLnk has revolutionized our energy procurement process. We've reduced costs by 25% and improved our supply chain transparency dramatically.
            </blockquote>
            <div className="text-[#18b793] font-semibold">
              <p className="text-lg"><PERSON>, Chief Procurement Officer</p>
              <p className="text-base opacity-80">Global Energy Solutions Inc.</p>
            </div>
          </div>
          <div className="flex justify-center items-center gap-4">
            <div className="flex gap-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className={`w-2 h-2 rounded-full ${i === 2 ? "bg-white" : "bg-[#18b793]"}`}></div>
              ))}
            </div>
            <div className="flex gap-2 ml-8">
              <Button
                variant="outline"
                size="icon"
                className="rounded-full border-[#18b793] text-[#18b793] hover:bg-[#18b793] hover:text-white"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="rounded-full border-[#18b793] text-[#18b793] hover:bg-[#18b793] hover:text-white"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Insights Section */}
      <section className="px-6 py-24 md:py-32 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4 text-center">Energy Market Insights</h2>
          <p className="text-lg text-gray-600 text-center mb-16 md:mb-20 max-w-3xl mx-auto">
            Stay ahead of market trends with expert analysis, industry reports, and strategic insights from our energy trading specialists.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
            {/* Market Analysis Card */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <div className="aspect-video bg-gradient-to-br from-[#004235] to-[#18b793] relative overflow-hidden">
                <Image
                  src="/images/homepage/blog 1.webp"
                  alt="Energy market analysis"
                  width={300}
                  height={200}
                  className="object-cover w-full h-full opacity-80"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                <div className="absolute bottom-4 left-4 text-white">
                  <span className="text-sm font-semibold">Market Analysis</span>
                </div>
              </div>
              <CardContent className="p-6">
                <div className="text-sm text-[#18b793] mb-2 font-semibold">• Energy Markets</div>
                <div className="text-sm text-gray-500 mb-4">Research Report</div>
                <h3 className="text-xl font-semibold text-[#004235]">
                  Global Energy Trading Trends: Q4 2024 Market Outlook
                </h3>
              </CardContent>
            </Card>

            {/* Technology Innovation Card */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <div className="aspect-video bg-gradient-to-br from-[#18b793] to-[#004235] relative overflow-hidden">
                <Image
                  src="/images/homepage/blog 2.webp"
                  alt="Technology innovation in energy"
                  width={300}
                  height={200}
                  className="object-cover w-full h-full opacity-80"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                <div className="absolute bottom-4 left-4 text-white">
                  <span className="text-sm font-semibold">Innovation</span>
                </div>
              </div>
              <CardContent className="p-6">
                <div className="text-sm text-[#18b793] mb-2 font-semibold">• Technology</div>
                <div className="text-sm text-gray-500 mb-4">Case Study</div>
                <h3 className="text-xl font-semibold text-[#004235]">
                  How AI is Transforming Energy Supply Chain Management
                </h3>
              </CardContent>
            </Card>

            {/* Platform Guide Card */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <div className="aspect-video bg-gradient-to-br from-[#004235] via-[#18b793] to-[#004235] relative overflow-hidden">
                <Image
                  src="/images/homepage/blog 3.webp"
                  alt="StreamLnk platform guide"
                  width={300}
                  height={200}
                  className="object-cover w-full h-full opacity-80"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                <div className="absolute bottom-4 left-4 text-white">
                  <span className="text-sm font-semibold">Platform Guide</span>
                </div>
              </div>
              <CardContent className="p-6">
                <div className="text-sm text-[#18b793] mb-2 font-semibold">• StreamLnk Platform</div>
                <div className="text-sm text-gray-500 mb-4">User Guide</div>
                <h3 className="text-xl font-semibold text-[#004235]">
                  Getting Started with Digital Energy Trading
                </h3>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}