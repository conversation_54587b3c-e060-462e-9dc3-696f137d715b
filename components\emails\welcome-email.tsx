import {
  <PERSON>,
  Button,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from "@react-email/components"
import { Tailwind } from "@react-email/tailwind"

interface WelcomeEmailProps {
  name: string
  dashboardUrl: string
}

export function WelcomeEmail({ name, dashboardUrl }: WelcomeEmailProps) {
  return (
    <Html>
      <Head />
      <Preview>Welcome to StreamLnk!</Preview>
      <Tailwind>
        <Body className="bg-gray-100 font-sans">
          <Container className="bg-white p-8 rounded-lg shadow-md max-w-md mx-auto my-8">
            <Img
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/dark%20logo%20dor%20white%20background-MIeaQojvl7sSEIsIQ0bQRzzYU3lzwW.png"
              alt="StreamLnk"
              width="200"
              height="45"
              className="mx-auto mb-6"
            />

            <Heading className="text-2xl font-bold text-[#004235] mb-6 text-center">
              Welcome to StreamLnk, {name}!
            </Heading>

            <Text className="text-gray-700 mb-4">
              Your account has been successfully created and is now ready to use.
            </Text>

            <Text className="text-gray-700 mb-6">
              With StreamLnk, you can connect with suppliers, optimize your logistics, and streamline your petrochemical
              supply chain.
            </Text>

            <Section className="text-center mb-6">
              <Button
                className="bg-gradient-to-r from-[#004235] to-[#07BC94] text-white px-6 py-3 rounded-md font-medium"
                href={dashboardUrl}
              >
                Go to Dashboard
              </Button>
            </Section>

            <Hr className="border-gray-200 my-6" />

            <Text className="text-gray-700 font-medium mb-4">Here are some things you can do:</Text>

            <Text className="text-gray-700 mb-2">• Complete your profile to get personalized recommendations</Text>
            <Text className="text-gray-700 mb-2">• Explore the marketplace for petrochemical products</Text>
            <Text className="text-gray-700 mb-2">• Connect with logistics providers for your shipping needs</Text>
            <Text className="text-gray-700 mb-6">• Set up your company details to start trading</Text>

            <Text className="text-gray-700 mb-6">
              If you have any questions, please don't hesitate to contact our support team at{" "}
              <Link href="mailto:<EMAIL>" className="text-[#07BC94]">
                <EMAIL>
              </Link>
            </Text>

            <Hr className="border-gray-200 my-6" />

            <Text className="text-gray-500 text-xs text-center">
              © 2025 StreamLnk. All rights reserved.
              <br />
              123 Business Street, Suite 100, New York, NY 10001
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  )
}

