"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import type { LiveEvent } from "@/types/map-types"
import { But<PERSON> } from "@/components/ui/button"

interface LiveEventsPanelProps {
  events: LiveEvent[]
  showEvents: boolean
  isMinimized: boolean
  onToggleMinimized: () => void
}

export function LiveEventsPanel({ events, showEvents, isMinimized, onToggleMinimized }: LiveEventsPanelProps) {
  if (!showEvents || events.length === 0) return null

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-slate-900 font-semibold flex items-center gap-2">
          <AlertTriangle className="w-4 h-4" />
          Live Events
          {isMinimized && <span className="text-xs text-slate-600">({events.length})</span>}
        </h3>
        <Button variant="ghost" size="sm" onClick={onToggleMinimized} className="text-slate-600 hover:text-slate-900">
          <X className="w-4 h-4" />
        </Button>
      </div>

      {!isMinimized && (
        <div className="space-y-2 max-h-60 overflow-y-auto">
          {events.slice(0, 5).map((event) => (
            <div
              key={event.id}
              className={`p-2 rounded-lg border-l-2 ${
                event.severity === "critical"
                  ? "bg-red-50 border-red-500"
                  : event.severity === "high"
                    ? "bg-orange-50 border-orange-500"
                    : event.severity === "medium"
                      ? "bg-blue-50 border-blue-500"
                      : "bg-green-50 border-green-500"
              }`}
            >
              <div className="flex items-center justify-between">
                <span className="text-slate-900 text-sm font-medium">{event.type.toUpperCase()}</span>
                <span className="text-slate-600 text-xs">{event.timestamp.toLocaleTimeString()}</span>
              </div>
              <p className="text-slate-600 text-xs mt-1">{event.message}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
