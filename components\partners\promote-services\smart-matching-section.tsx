import { Card, CardContent } from "@/components/ui/card"
import { FileSearch, PackageOpen, Clock, Languages, Users } from "lucide-react"

export function SmartMatchingSection() {
  const matchingFeatures = [
    {
      icon: <FileSearch className="h-12 w-12 text-white" />,
      title: "RFQ-Driven Matches",
      description:
        "Your profile is suggested when RFQs are generated that require your specific coverage zone, service type, or industry expertise.",
    },
    {
      icon: <PackageOpen className="h-12 w-12 text-white" />,
      title: "Shipment Service Gaps",
      description:
        "You may be recommended when a shipment is being configured and is missing a crucial service layer (e.g., no customs agent assigned for a specific destination, specialized packaging needed).",
    },
    {
      icon: <Clock className="h-12 w-12 text-white" />,
      title: "Time-Sensitive Needs",
      description:
        "Your services could be highlighted for auction-based workflows or urgent shipments where speed and reliability are critical.",
    },
    {
      icon: <Languages className="h-12 w-12 text-white" />,
      title: "Regional & Language Alignment",
      description: "Prioritization for buyers and suppliers operating in your language or primary regional zone.",
    },
    {
      icon: <Users className="h-12 w-12 text-white" />,
      title: "Preferred Vendor Pools",
      description:
        "Potential inclusion in contracted vendor pools favored by large regional distributors or enterprise clients on the platform.",
    },
  ]

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Smart Service Matching: Connecting You to the Right Opportunities
          </h2>
          <p className="text-lg text-gray-700 mb-12 max-w-4xl">
            Once your service profile is verified and complete, StreamLnk's intelligent matching algorithms work to
            connect you with relevant opportunities:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {matchingFeatures.map((feature, index) => (
              <Card
                key={index}
                className="border-none overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300"
              >
                <div className="bg-gradient-to-r from-[#004235] to-[#028475] p-6 flex justify-center">
                  <div className="rounded-full bg-white/10 p-4">{feature.icon}</div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold text-[#004235] mb-3">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          <p className="text-lg text-gray-700 mt-8 max-w-4xl">
            You'll receive notifications of eligible shipment or quote opportunities, minimizing "bidding fatigue" and
            focusing your efforts on relevant, qualified leads.
          </p>
        </div>
      </div>
    </section>
  )
}
