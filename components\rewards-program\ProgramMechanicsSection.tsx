"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Users, Briefcase, UserCheck, Truck, ShoppingCart, Building, Handshake, Zap } from "lucide-react";
import Link from "next/link";

export default function ProgramMechanicsSection() {
  const userGroups = [
    {
      icon: <ShoppingCart className="h-10 w-10 text-[#028475] mb-4" />,
      title: "For Customers (MyStreamLnk Users)",
      description: "Earn tiers (Bronze to Elite Circle) based on spend, payment history, feedback, and feature adoption. Benefits include discounts, priority support, and early access. Earn points for activity, redeemable for further discounts or vouchers.",
      buttonText: "Full Customer Details",
      buttonLink: "/rewards-program/customer-details" // Placeholder
    },
    {
      icon: <Building className="h-10 w-10 text-[#028475] mb-4" />,
      title: "For Suppliers (E-Stream Users)",
      description: "Achieve tiers (Bronze to Elite Circle) through sales volume, fulfillment quality, and compliance. Benefits include higher visibility, reduced fees, and market analytics. Earn points for successful transactions, redeemable for sponsored listings.",
      buttonText: "Full Supplier Details",
      buttonLink: "/rewards-program/supplier-details" // Placeholder
    },
    {
      icon: <Handshake className="h-10 w-10 text-[#028475] mb-4" />,
      title: "For Agents/Distributors (MyStreamLnk+ Users)",
      description: "Earn tiers (Bronze to Strategic Partner) based on client onboarding, GMV, and retention. Benefits include increased commissions and priority leads. Earn points for milestones, redeemable for accelerators or reports.",
      buttonText: "Full Agent Details",
      buttonLink: "/rewards-program/agent-details" // Placeholder
    },
    {
      icon: <Truck className="h-10 w-10 text-[#028475] mb-4" />,
      title: "For Service Providers (StreamFreight, StreamGlobe+, StreamPak Users)",
      description: "Achieve tiers (Bronze to Strategic Partner) through consistent on-time performance, job acceptance, and compliance. Benefits include higher job priority, faster payments, and 'Verified Partner' badging. Perks are unlocked for high performance.",
      buttonText: "Full Service Provider Details",
      buttonLink: "/rewards-program/service-provider-details" // Placeholder
    }
  ];

  return (
    <section id="how-to-earn" className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            How It Works – Tailored for You
          </h2>
          <p className="text-lg text-gray-700 leading-relaxed">
            Our program features distinct tier structures and reward mechanisms, tailored to different user groups. Tier status is typically evaluated quarterly based on performance.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-stretch">
          {userGroups.map((group, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-lg flex flex-col h-full">
              <div className="flex justify-start mb-3">{group.icon}</div>
              <h3 className="text-xl font-semibold text-[#004235] mb-3 min-h-[2.5em]">{group.title}</h3>
              <p className="text-gray-600 text-sm mb-6 flex-grow">{group.description}</p>
              <div className="mt-auto">
                <Button 
                  variant="outline"
                  className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full"
                  size="lg"
                  asChild
                >
                  <Link href={group.buttonLink}>
                    {group.buttonText}
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}