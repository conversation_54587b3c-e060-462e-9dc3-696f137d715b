import Image from "next/image"

interface LogoProps {
  variant?: "dark" | "light"
  className?: string
  width?: number
  height?: number
  size?: "small" | "medium" | "large"
  type?: "default" | "auth"
}

export function Logo({ variant = "dark", className = "", width, height, size = "medium", type = "default" }: LogoProps) {
  // Use the appropriate logo based on type
  const logoUrl = type === "auth"
    ? "/images/homepage/signup-signin form logo.svg"
    : "/images/homepage/main header logo light.svg"

  const sizes = {
    small: { width: 150, height: 32 },
    medium: { width: 200, height: 45 },
    large: { width: 250, height: 55 },
  }

  const { width: defaultWidth, height: defaultHeight } = sizes[size]

  return (
    <Image
      src={logoUrl}
      alt="StreamLnk Logo"
      width={width || defaultWidth}
      height={height || defaultHeight}
      className={className}
    />
  )
}

