import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

const benefits = [
  "Direct access to a global network of industrial buyers (end-users, distributors, agents) in 40+ countries.",
  "AI-driven product-buyer matching based on technical specs, certifications, and demand.",
  "Built-in tools for RFQs, instant quotes, and Incoterm-based delivery options (CIF, CFR, EXW, DDP).",
  "Seamless integration with freight, customs, warehousing, and payment systems.",
  "Dynamic dashboards to manage orders, payments, and product performance.",
  "Full delivery automation, compliance tracking, and bidding visibility.",
]

export function BenefitsSection() {
  return (
    <section id="learn-more" className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row gap-12">
          <div className="md:w-1/2">
            <h2 className="text-3xl font-bold text-[#004235] mb-6">Why Sell on E-Stream?</h2>
            <p className="text-gray-700 mb-6">
              Selling industrial products online has long been fragmented, slow, and heavily manual. E-Stream changes
              that. As part of the StreamLnk ecosystem, E-Stream connects your products directly with qualified
              industrial buyers while automating logistics, compliance, and payments.
            </p>

            <div className="space-y-4 mt-8">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-start gap-3">
                  <CheckCircle className="h-6 w-6 text-[#028475] flex-shrink-0 mt-0.5" />
                  <p className="text-gray-700">{benefit}</p>
                </div>
              ))}
            </div>
          </div>

          <div className="md:w-1/2">
            <div className="bg-[#F2F2F2] rounded-lg overflow-hidden shadow-lg">
              <div className="relative h-64">
                <Image
                  src="/images/partner/e-stream/supplier-dashboard.webp"
                  alt="E-Stream supplier dashboard"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-[#004235] mb-4">Digitize Your Industrial Supply Chain</h3>
                <p className="text-gray-700 mb-4">
                  E-Stream isn't just a marketplace. It's a modern B2B operating system for the next generation of
                  global suppliers, designed to help you reach global buyers with full delivery automation, compliance
                  tracking, and bidding visibility.
                </p>
                <Button
                  variant="outline"
                  className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white transition-colors"
                  asChild
                >
                  <Link href="#supplier-resources">Explore Supplier Tools</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
