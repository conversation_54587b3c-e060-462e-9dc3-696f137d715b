import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import HeroSection from "@/components/finance-payments/invoicing-center/hero-section"
import DashboardPreviewSection from "@/components/finance-payments/invoicing-center/dashboard-preview-section"
import KeyFeaturesSection from "@/components/finance-payments/invoicing-center/key-features-section"
import ComplianceAuditSupportSection from "@/components/finance-payments/invoicing-center/compliance-audit-support-section"
import IntegratedAcrossStreamLnkSection from "@/components/finance-payments/invoicing-center/integrated-across-streamlnk-section"
import UseCasesSection from "@/components/finance-payments/invoicing-center/use-cases-section"
import CtaSection from "@/components/finance-payments/invoicing-center/cta-section"

export default function InvoicingCenterPage() {
  return (
    <main className="flex flex-col min-h-screen">
      <MainNav />
      <HeroSection />
      <DashboardPreviewSection />
      <KeyFeaturesSection />
      <ComplianceAuditSupportSection />
      <IntegratedAcrossStreamLnkSection />
      <UseCasesSection />
      <CtaSection />
      <MainFooter />
    </main>
  )
}
