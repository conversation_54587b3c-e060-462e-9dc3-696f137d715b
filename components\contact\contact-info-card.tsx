// components/contact/contact-info-card.tsx
import Link from 'next/link';
import { ReactElement } from 'react';

export interface ContactInfoItem {
  icon: ReactElement;
  title: string;
  email?: string; // Make email optional if some cards don't have it
  phone?: string; // Add phone if needed
  description: string;
  href?: string; // Optional link for the whole card or email
}

interface ContactInfoCardProps {
  item: ContactInfoItem;
}

export function ContactInfoCard({ item }: ContactInfoCardProps) {
  const cardContent = (
    <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200 flex flex-col h-full">
      <div className="flex items-center mb-3">
        <span className="mr-3 flex-shrink-0 text-[#004235]">{item.icon}</span>
        <h3 className="text-lg font-semibold text-[#004235]">{item.title}</h3>
      </div>
      {item.email && (
        <Link href={`mailto:${item.email}`} className="text-[#028475] hover:underline break-all mb-1 text-sm">
          {item.email}
        </Link>
      )}
      {item.phone && (
        <Link href={`tel:${item.phone}`} className="text-[#028475] hover:underline break-all mb-1 text-sm">
          {item.phone}
        </Link>
      )}
      <p className="text-sm text-muted-foreground flex-grow">
        {item.description}
      </p>
    </div>
  );

  if (item.href) {
    return (
      <Link href={item.href} className="block hover:shadow-lg transition-shadow duration-200">
        {cardContent}
      </Link>
    );
  }

  return cardContent;
}