import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";

export default function CTASection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            Empower Your Strategy with Unparalleled Market Intelligence.
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            Accessing StreamLnk Market Analysis: Detailed market analysis and reports are primarily available through StreamResources+ subscriptions. Explore our subscription tiers to find the level of access that meets your needs.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white px-6 py-3 text-base w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/request-demo?product=streamresources-plus-analytics"> {/* Placeholder link, add query for specific demo */}
                REQUEST DEMO
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-6 py-3 text-base w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/solutions/streamresources-plus/plans"> {/* Placeholder link */}
                COMPARE PLANS
              </Link>
            </Button>
          </div>
          <div className="mt-6">
             <Button 
              variant="link" 
              className="text-[#028475] hover:text-[#004235] text-base"
              asChild
            >
              <Link href="/contact/data-team-inquiry"> {/* Placeholder link */}
                CONTACT DATA TEAM
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}