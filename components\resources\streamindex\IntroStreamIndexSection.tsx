"use client";

import { BarChart2, Truck, Users, TrendingUp, ShieldAlert, Database } from 'lucide-react';

const coreComponents = [
  {
    icon: <BarChart2 className="h-8 w-8 text-[#028475]" />,
    title: "Product Pricing Index",
    whatItOffers: "Real-time average market prices, price ranges, and volatility scores for specific industrial materials (polymers, chemicals, etc.).",
    granularity: "Filterable by product grade, packaging type, Incoterm, and precise geographic region.",
    insights: "Track historical price trends, identify arbitrage opportunities, benchmark your quotes/offers."
  },
  {
    icon: <Truck className="h-8 w-8 text-[#028475]" />,
    title: "Logistics Efficiency Index",
    whatItOffers: "Benchmarks for average delivery times, carrier on-time performance ratings (aggregated and anonymized), and customs clearance efficiency.",
    granularity: "Analysis by specific trade lanes, ports, and transport modes (sea, land, rail).",
    insights: "Optimize logistics planning, select more reliable routes/partners, anticipate potential delays."
  },
  {
    icon: <Users className="h-8 w-8 text-[#028475]" />,
    title: "Supplier & Partner Reliability Index (feeds into iScore™)",
    whatItOffers: "Aggregated, anonymized performance indicators for categories of suppliers and service providers based on on-time readiness/delivery, order fulfillment accuracy, document compliance, and platform-generated feedback.",
    granularity: "N/A - Aggregated view",
    insights: "Understand general reliability trends within specific market segments or regions."
  },
  {
    icon: <TrendingUp className="h-8 w-8 text-[#028475]" />,
    title: "Buyer Demand Index",
    whatItOffers: "Real-time indicators of market demand based on aggregated RFQ volumes, auction participation, and product search trends.",
    granularity: "By product category, grade, and region.",
    insights: "Identify demand hotspots, anticipate shifts in buyer interest, optimize inventory and production."
  },
  {
    icon: <ShieldAlert className="h-8 w-8 text-[#028475]" />,
    title: "Compliance & Market Risk Index",
    whatItOffers: "Flags regions or trade lanes with higher observed instances of logistics disruptions, customs delays, payment issues, or heightened geopolitical/economic risk factors impacting trade.",
    granularity: "N/A - Risk flagging",
    insights: "Provides early warnings for potential supply chain vulnerabilities, enabling proactive risk mitigation."
  }
];

export default function IntroStreamIndexSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Introducing StreamIndex™ – Intelligent Benchmarking by StreamLnk
          </h2>
          <p className="text-xl text-gray-700 mb-2">
            What is StreamIndex™? Unveiling True Market Dynamics.
          </p>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            StreamIndex™ is a powerful suite of proprietary market indicators developed by StreamLnk's data intelligence arm, StreamResources+. It continuously analyzes millions of anonymized data points generated across our entire global B2B trade ecosystem (E-Stream, MyStreamLnk, logistics portals, etc.) using advanced AI and machine learning.
          </p>
        </div>

        <div className="mb-12 md:mb-16">
            <h3 className="text-2xl md:text-3xl font-semibold text-[#004235] mb-8 text-center">Core Components of StreamIndex™:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {coreComponents.map((component, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-4">
                    {component.icon}
                    <h4 className="text-xl font-semibold text-[#004235] ml-3">{component.title}</h4>
                </div>
                <div className="space-y-3 text-sm text-gray-600">
                    <p><strong className="text-[#004235]">What it offers:</strong> {component.whatItOffers}</p>
                    <p><strong className="text-[#004235]">Granularity:</strong> {component.granularity}</p>
                    <p><strong className="text-[#004235]">Insights:</strong> {component.insights}</p>
                </div>
                </div>
            ))}
            </div>
        </div>

      </div>
    </section>
  );
}