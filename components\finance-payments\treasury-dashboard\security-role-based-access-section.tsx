import {
  <PERSON>,
  <PERSON>,
  <PERSON>Che<PERSON>,
  Layers,
} from "lucide-react"
import SecurityFeature from "@/components/finance-payments/treasury-dashboard/security-feature"

export default function SecurityRoleBasedAccessSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Security & Role-Based Access</h2>
          <p className="text-gray-600 max-w-3xl">
            Enterprise-grade security features ensure your financial data remains protected.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <SecurityFeature
            icon={<Users className="h-8 w-8 text-[#028475]" />}
            title="Role-Based Permissions"
            description="Segmented access for treasury, compliance, and regional finance teams"
          />
          <SecurityFeature
            icon={<Lock className="h-8 w-8 text-[#028475]" />}
            title="Two-Factor Authentication"
            description="Enhanced security for sensitive financial operations"
          />
          <SecurityFeature
            icon={<ShieldCheck className="h-8 w-8 text-[#028475]" />}
            title="Audit Logging"
            description="Comprehensive activity tracking for compliance requirements"
          />
          <SecurityFeature
            icon={<Layers className="h-8 w-8 text-[#028475]" />}
            title="Enterprise Financial Controls"
            description="Meets strict enterprise security and compliance standards"
          />
        </div>
      </div>
    </section>
  )
}