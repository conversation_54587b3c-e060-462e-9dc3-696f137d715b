import type { Metadata } from "next"
import { HeroSec<PERSON> } from "@/components/partners/partner-with-us/hero-section"
import { WhatIsSection } from "@/components/partners/partner-with-us/what-is-section"
import { BenefitsSection } from "@/components/partners/partner-with-us/benefits-section"
import { PortalFeaturesSection } from "@/components/partners/partner-with-us/portal-features-section"
import { CommissionSection } from "@/components/partners/partner-with-us/commission-section"
import { ToolsSection } from "@/components/partners/partner-with-us/tools-section"
import { IdealApplicantsSection } from "@/components/partners/partner-with-us/ideal-applicants-section"
import { JoinProcessSection } from "@/components/partners/partner-with-us/join-process-section"
import { SupportSection } from "@/components/partners/partner-with-us/support-section"
import { CtaSection } from "@/components/partners/partner-with-us/cta-section"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"

export const metadata: Metadata = {
  title: "Become a StreamLnk Sales Agent or Distributor | StreamLnk",
  description:
    "Join StreamLnk as an independent sales agent or regional distributor. Leverage our powerful digital platform to onboard customers, manage their accounts, track transactions, and earn commissions.",
}

export default function PartnerWithUsPage() {
  return (
    <main className="flex min-h-screen flex-col">
      <MainNav />
      <HeroSection />
      <WhatIsSection />
      <BenefitsSection />
      <PortalFeaturesSection />
      <CommissionSection />
      <ToolsSection />
      <IdealApplicantsSection />
      <JoinProcessSection />
      <SupportSection />
      <CtaSection />
      <MainFooter />
    </main>
  )
}
