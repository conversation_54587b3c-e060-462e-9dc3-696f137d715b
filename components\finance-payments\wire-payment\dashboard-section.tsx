import Image from "next/image";
import { BarChart3, DollarSign, CheckCircle2, Building2 } from "lucide-react";
import DashboardFeature from "./dashboard-feature"; // Assuming this component will be moved or path adjusted

export default function DashboardSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="grid gap-12 lg:grid-cols-2 items-center">
          <div>
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-6">Real-Time Treasury Dashboard</h2>
            <p className="text-gray-600 mb-8">
              Users with access to StreamLnk's Treasury Center can monitor and manage all payment activities in one
              place.
            </p>

            <div className="space-y-4">
              <DashboardFeature
                icon={<BarChart3 className="h-5 w-5 text-[#028475]" />}
                title="Track incoming/outgoing wire payments"
              />
              <DashboardFeature icon={<DollarSign className="h-5 w-5 text-[#028475]" />} title="View live FX rates" />
              <DashboardFeature
                icon={<CheckCircle2 className="h-5 w-5 text-[#028475]" />}
                title="Reconcile payments by invoice, PO, and delivery"
              />
              <DashboardFeature
                icon={<Building2 className="h-5 w-5 text-[#028475]" />}
                title="Monitor payment statuses by counterparty and country"
              />
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/finance-payments/Graphic of real-time treasury dashboard displaying global payment tracking and live FX rates.png"
              alt="Treasury dashboard interface"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}