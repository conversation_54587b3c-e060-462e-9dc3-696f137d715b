import { CheckCircle2, Clock, MessageSquare, <PERSON><PERSON>he<PERSON>, Shield } from "lucide-react"

export function ServiceExpectationsSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
              Service Expectations for StreamGlobe Partners
            </h2>
            <p className="mx-auto max-w-[700px] text-gray-600 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              What we expect from our customs clearance partners
            </p>
          </div>
        </div>
        <div className="mx-auto max-w-3xl mt-12">
          <div className="grid gap-6">
            {[
              {
                icon: <Clock className="h-6 w-6 text-[#028475]" />,
                title: "Real-Time Updates",
                description: "Provide timely and accurate updates on clearance progress through the portal.",
              },
              {
                icon: <MessageSquare className="h-6 w-6 text-[#028475]" />,
                title: "Proactive Communication",
                description: "Immediately flag any incomplete documentation, discrepancies, or potential delays.",
              },
              {
                icon: <FileCheck className="h-6 w-6 text-[#028475]" />,
                title: "Maintain Current Documentation",
                description:
                  "Ensure your POA, customs license, and other compliance documents are always up-to-date in the portal.",
              },
              {
                icon: <CheckCircle2 className="h-6 w-6 text-[#028475]" />,
                title: "Meet Performance Benchmarks",
                description: "Strive to maintain average clearance times at or below the regional benchmark.",
              },
              {
                icon: <Shield className="h-6 w-6 text-[#028475]" />,
                title: "Adherence to Regulations",
                description:
                  "Work strictly within port/terminal rules and customs regulations for each specific shipment region.",
              },
            ].map((item, index) => (
              <div key={index} className="flex items-start space-x-4 rounded-lg border border-gray-200 p-6 shadow-sm">
                <div className="rounded-full bg-[#F2F2F2] p-2">{item.icon}</div>
                <div>
                  <h3 className="text-xl font-bold text-[#004235]">{item.title}</h3>
                  <p className="text-gray-600">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
