import type { Metadata } from "next";
import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";
import HeroSection from "@/components/why-us/ai-efficiency/hero-section";
import TraditionalInefficiencySection from "@/components/why-us/ai-efficiency/traditional-inefficiency-section";
import AiBenefitsSection from "@/components/why-us/ai-efficiency/ai-benefits-section";
import CumulativeImpactSection from "@/components/why-us/ai-efficiency/cumulative-impact-section";
import CtaSection from "@/components/why-us/ai-efficiency/cta-section";

export const metadata: Metadata = {
  title: "Why StreamLnk? AI-Driven Efficiency for Industrial Supply Chains | StreamLnk",
  description:
    "Discover how StreamLnk's AI-driven platform makes your supply chain smarter, faster, more predictive, and radically more efficient.",
};

export default function AiEfficiencyPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <MainNav />
      <main>
        <HeroSection />
        <TraditionalInefficiencySection />
        <AiBenefitsSection />
        <CumulativeImpactSection />
        <CtaSection />
      </main>
      <MainFooter />
    </div>
  );
}