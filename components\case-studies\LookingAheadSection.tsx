"use client";

interface LookingAheadSectionProps {
  lookingAheadText: string;
}

export default function LookingAheadSection({ lookingAheadText }: LookingAheadSectionProps) {
  return (
    <section className="mb-12">
      <h2 className="text-3xl font-bold text-[#004235] mb-6">
        Looking Ahead with StreamLnk
      </h2>
      <p className="text-lg text-gray-700 leading-relaxed">
        {lookingAheadText}
      </p>
    </section>
  );
}