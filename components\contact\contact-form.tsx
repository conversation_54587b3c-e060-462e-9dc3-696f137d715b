// components/contact/contact-form.tsx
"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowRight } from 'lucide-react';

// TODO: Populate these with actual data or fetch from an API
const countries = [{ value: "us", label: "United States" }, { value: "ca", label: "Canada" }];
const contactReasons = [{ value: "general", label: "General Inquiry" }, { value: "support", label: "Support Request" }];
const areasOfInterest = [{ value: "logistics", label: "Logistics Solutions" }, { value: "technology", label: "Technology" }];

const formSchema = z.object({
  firstName: z.string().min(1, { message: "First name is required." }),
  lastName: z.string().min(1, { message: "Last name is required." }),
  companyEmail: z.string().email({ message: "Invalid email address." }),
  companyName: z.string().min(1, { message: "Company name is required." }),
  phoneNumber: z.string().min(1, { message: "Phone number is required." }), // Add more specific phone validation if needed
  jobTitle: z.string().min(1, { message: "Job title is required." }),
  country: z.string().min(1, { message: "Country/Territory is required." }),
  contactReason: z.string().min(1, { message: "Reason for contacting is required." }),
  areaOfInterest: z.string().min(1, { message: "Area of interest is required." }),
  requestDetails: z.string().min(10, { message: "Please provide at least 10 characters." }),
  communicationsPreference: z.enum(["yes", "no"], { required_error: "Please select a communication preference." }),
});

export function ContactForm() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      companyEmail: "",
      companyName: "",
      phoneNumber: "",
      jobTitle: "",
      country: "",
      contactReason: "",
      areaOfInterest: "",
      requestDetails: "",
      communicationsPreference: undefined,
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    // TODO: Implement form submission logic (e.g., API call)
    console.log(values);
    // Potentially show a success message or redirect
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="mb-8">
            <p className="text-muted-foreground text-sm mb-2">
                Connect with StreamLnk by completing the form below.
            </p>
            <div className="flex items-center mb-6">
                <div className="h-px bg-[#028475] w-8 mr-3"></div>
                <h2 className="text-3xl font-semibold text-[#004235]">
                    Fill the form below
                </h2>
            </div>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>First Name: <span className="text-red-600">*</span></FormLabel>
                <FormControl>
                  <Input placeholder="First Name" {...field} className="bg-white" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Last Name: <span className="text-red-600">*</span></FormLabel>
                <FormControl>
                  <Input placeholder="Last Name" {...field} className="bg-white" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="companyEmail"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Company Email Address: <span className="text-red-600">*</span></FormLabel>
              <FormControl>
                <Input type="email" placeholder="Company Email Address" {...field} className="bg-white" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="companyName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Company Name: <span className="text-red-600">*</span></FormLabel>
              <FormControl>
                <Input placeholder="Company Name" {...field} className="bg-white" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Primary Phone Number: <span className="text-red-600">*</span></FormLabel>
                <FormControl>
                  <Input placeholder="Primary Phone Number" {...field} className="bg-white" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="jobTitle"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Job Title: <span className="text-red-600">*</span></FormLabel>
                <FormControl>
                  <Input placeholder="Job Title" {...field} className="bg-white" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="country"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Country/Territory: <span className="text-red-600">*</span></FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="bg-white">
                    <SelectValue placeholder="Select Country/Territory..." />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {countries.map(country => (
                    <SelectItem key={country.value} value={country.value}>{country.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="contactReason"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Which category best describes your reason for contacting us? <span className="text-red-600">*</span></FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="bg-white">
                    <SelectValue placeholder="Select..." />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {contactReasons.map(reason => (
                    <SelectItem key={reason.value} value={reason.value}>{reason.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="areaOfInterest"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Area of Interest: <span className="text-red-600">*</span></FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="bg-white">
                    <SelectValue placeholder="Select..." />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {areasOfInterest.map(area => (
                    <SelectItem key={area.value} value={area.value}>{area.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="requestDetails"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Please provide details about your request: <span className="text-red-600">*</span></FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Write your request here."
                  className="resize-none bg-white min-h-[120px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="communicationsPreference"
          render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>Communications Preference: <span className="text-red-600">*</span></FormLabel>
              <FormControl>
                <div className="space-y-2">
                    <FormItem className="flex items-start space-x-3 space-y-0">
                        <FormControl>
                            <Checkbox 
                                checked={field.value === "yes"} 
                                onCheckedChange={(checked) => field.onChange(checked ? "yes" : field.value === "no" ? "no" : "")} 
                            />
                        </FormControl>
                        <FormLabel className="font-normal text-sm text-muted-foreground">
                            Yes, please keep me informed of topics and innovations transforming my industry, including special event invitations, surveys, newsletters, product and service incentives and new product announcements from StreamLnk Company and its affiliated companies (together “StreamLnk”).
                        </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-start space-x-3 space-y-0">
                        <FormControl>
                            <Checkbox 
                                checked={field.value === "no"} 
                                onCheckedChange={(checked) => field.onChange(checked ? "no" : field.value === "yes" ? "yes" : "")} 
                            />
                        </FormControl>
                        <FormLabel className="font-normal text-sm text-muted-foreground">
                            No, please exclude me from marketing communications from StreamLnk.
                        </FormLabel>
                    </FormItem>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="text-xs text-muted-foreground space-y-1">
            <p>
                Detailed information about how the personal data you have supplied is collected, stored and processed can be found in the <a href="/privacy-policy" className="text-[#028475] hover:underline">StreamLnk Privacy Notice</a>. If you have opted in to receive marketing communications, you can withdraw your consent at any time by visiting the <a href="/communication-preferences" className="text-[#028475] hover:underline">StreamLnk Communications Preference Center</a>, or by using the unsubscribe link found at the bottom of all emails from StreamLnk.
            </p>
        </div>

        <Button type="submit" className="bg-[#028475] hover:bg-[#028475]/90 text-white w-full md:w-auto text-base px-8 py-6 group">
          Submit Contact Request <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
        </Button>
      </form>
    </Form>
  );
}