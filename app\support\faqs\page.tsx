import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import Link from 'next/link';
import { Search, ChevronRight, MessageSquare, FileText, LifeBuoy } from 'lucide-react';

const faqCategories = [
  {
    categoryTitle: "General Account & Login",
    questions: [
      {
        question: "How do I register for a StreamLnk account?",
        answer: (
          <>
            Visit our <Link href='/signup' className='text-[#028475] hover:text-[#004235] hover:underline'>Register Page</Link>, select your role, and follow the verification steps.
          </>
        )
      },
      {
        question: "How do I reset my password?",
        answer: "On the login page, click \"Forgot Password.\" Enter your registered email address, and we'll send you a link to reset your password."
      },
      {
        question: "Why is my account suspended?",
        answer: (
          <>
            Suspension can be due to expired documents, overdue payments, or ToS violations. Check your email for details or contact <Link href='/customer-service' className='text-[#028475] hover:text-[#004235] hover:underline'>Customer Service</Link>.
          </>
        )
      }
    ]
  },
  {
    categoryTitle: "MyStreamLnk (For Buyers)",
    questions: [
      {
        question: "How do I submit an RFQ?",
        answer: "Log in to MyStreamLnk, go to 'Sourcing' or 'Product Discovery,' search for your product, and click 'Request Quote' to complete the form."
      },
      {
        question: "Understanding your landed cost quote.",
        answer: "Quotes provide a transparent \"landed cost,\" itemizing material, freight, customs duties, and platform fees."
      },
      {
        question: "How do I compare suppliers?",
        answer: "On MyStreamLnk, you can compare quotes side-by-side, view supplier profiles, and check their iScore™ ratings for objective performance and reliability data."
      }
    ]
  },
  {
    categoryTitle: "E-Stream (For Suppliers)",
    questions: [
      {
        question: "How do I add a new product?",
        answer: "Log in to E-Stream, go to 'Product Listings,' click 'Add New Product,' and follow the prompts to enter details and images."
      },
      {
        question: "Understanding StreamIndex™ pricing guidance.",
        answer: "StreamIndex™ offers AI-powered market benchmarks within E-Stream to help you price products competitively, showing indicative market price ranges."
      },
      {
        question: "How do I manage my inventory levels?",
        answer: "You can update your available inventory and lead times directly within the \"Product Listings\" or \"Inventory Management\" section of your E-Stream portal."
      }
    ]
  },
  {
    categoryTitle: "Logistics Portals (StreamFreight, StreamGlobe+, StreamPak)",
    questions: [
      {
        question: "Where can I find my shipment status?",
        answer: (
          <>
            Track shipments on your MyStreamLnk/E-Stream dashboard ('My Orders'/'Shipments'), or via your specific logistics portal. Our public <Link href='/tools/track-shipment' className='text-[#028475] hover:text-[#004235] hover:underline'>Track Shipment</Link> page is also available.
          </>
        )
      },
      {
        question: "What do the different shipment milestones mean?",
        answer: (
          <>
            Milestones like "Order Confirmed," "Ready for Pickup," "In Transit," "Customs Cleared," and "Delivered" provide real-time updates on your shipment's journey. Detailed explanations are available in our <Link href='/resources/glossary' className='text-[#028475] hover:text-[#004235] hover:underline'>Trade Glossary</Link>.
          </>
        )
      },
      {
        question: "How do I report a shipment issue or delay?",
        answer: "Please contact your StreamLnk support representative or submit a support ticket via your portal, providing the Order ID and a detailed description of the issue."
      }
    ]
  },
  {
    categoryTitle: "Payments & Finance",
    questions: [
      {
        question: "How do I view my invoices/payouts?",
        answer: "Invoices (for buyers) and payout statements (for suppliers/partners) are available in the \"Billing & Payments\" section of your respective StreamLnk portal."
      },
      {
        question: "Understanding StreamLnk fees and commissions.",
        answer: "StreamLnk charges transparent platform fees or commissions for facilitated transactions and services. These are clearly outlined in your user/partner agreement and itemized in your invoices/payout statements."
      },
      {
        question: "What are the accepted payment methods?",
        answer: "StreamLnk supports secure wire transfers, Escrow, and Buy Now, Pay Later (BNPL) options for qualified transactions. Specific options are presented during the checkout process."
      }
    ]
  },
  {
    categoryTitle: "Compliance & Documents",
    questions: [
      {
        question: "What documents do I need to upload for verification?",
        answer: (
          <>
            Upload business registration, tax IDs, and relevant licenses during registration. Requirements vary by user type and region. See our <Link href='/legal/kyc-aml' className='text-[#028475] hover:text-[#004235] hover:underline'>KYC/AML Verification</Link> page for details.
          </>
        )
      },
      {
        question: "How do I update an expiring certificate?",
        answer: "You will receive automated notifications before your critical compliance documents expire. Upload renewed documents in the \"Compliance Center\" or \"My Documents\" section of your portal."
      },
      {
        question: "What information is included in an iScore™ report?",
        answer: "A full iScore™ report (available to StreamResources+ subscribers) includes detailed scores on operational performance, compliance, financial trustworthiness, communication, and aggregated platform feedback for a given entity."
      }
    ]
  }
];

const popularQuestions = [
  { question: "How do I register for a StreamLnk account?", link: "#general-account-login-0" },
  { question: "How does the Buy Now, Pay Later (BNPL) option work for buyers?", link: "#payments-finance-2" },
  { question: "Where can I track my international shipment?", link: "#logistics-portals-0" },
];

const FaqsPage = () => {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="bg-[#F2F2F2] py-16 md:py-24">
          <div className="container mx-auto px-4 md:px-6 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-4">
              FAQs: StreamLnk Support
            </h1>
            <p className="text-lg md:text-xl text-gray-700 mb-8 max-w-3xl mx-auto">
              Your Quick Guide to StreamLnk. Quick answers to common questions about StreamLnk features and usage.
            </p>
            <div className="max-w-xl mx-auto flex flex-col sm:flex-row gap-4">
              <Input
                type="search"
                placeholder='Search FAQs (e.g., "reset password," "track shipment")'
                className="flex-grow text-base py-3 px-4"
              />
              <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white text-base">
                <Search className="mr-2 h-5 w-5" /> Search FAQs
              </Button>
            </div>
          </div>
        </section>

        {/* Browse by Category Section */}
        <section className="py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] text-center mb-4">
              Find Answers Relevant to Your Needs
            </h2>
            <p className="text-lg text-gray-600 text-center mb-12 max-w-2xl mx-auto">
              Browse FAQs by Category/Portal. Browse FAQs by topic or StreamLnk portal.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
              {faqCategories.map((cat) => (
                <Link key={cat.categoryTitle} href={`#${cat.categoryTitle.toLowerCase().replace(/\s+/g, '-').replace(/[&+,]/g, '')}`} passHref>
                  <Button variant="outline" className="w-full justify-start text-left h-auto py-3 border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white">
                    {cat.categoryTitle}
                  </Button>
                </Link>
              ))}
            </div>

            {/* FAQ Display Area */}
            {faqCategories.map((category, catIndex) => (
              <div key={catIndex} id={`${category.categoryTitle.toLowerCase().replace(/\s+/g, '-').replace(/[&+,]/g, '')}`} className="mb-12">
                <h3 className="text-2xl font-semibold text-[#004235] mb-6 pt-4 border-t border-gray-200">
                  {category.categoryTitle}
                </h3>
                <Accordion type="single" collapsible className="w-full">
                  {category.questions.map((faq, faqIndex) => (
                    <AccordionItem key={faqIndex} value={`item-${catIndex}-${faqIndex}`} id={`${category.categoryTitle.toLowerCase().replace(/\s+/g, '-').replace(/[&+,]/g, '')}-${faqIndex}`}>
                      <AccordionTrigger className="text-left hover:no-underline text-lg font-medium text-gray-800">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent className="text-gray-600 text-base leading-relaxed pt-2 pb-4">
                        {faq.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            ))}
          </div>
        </section>

        {/* Popular Questions Section */}
        <section className="bg-[#F2F2F2] py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] text-center mb-10">
              Popular Questions
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {popularQuestions.map((pq, index) => (
                <Card key={index} className="bg-white hover:shadow-md transition-shadow duration-300">
                  <CardContent className="p-6">
                    <Link href={pq.link} className="text-[#028475] hover:text-[#004235] hover:underline font-semibold text-lg">
                      {pq.question}
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Still Can't Find Your Answer? Section */}
        <section className="py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
              Still Can't Find Your Answer?
            </h2>
            <p className="text-lg text-gray-600 mb-8 max-w-xl mx-auto">
              Our Support Team is Ready to Assist You.
            </p>
            <div className="flex flex-col sm:flex-row justify-center items-center gap-4 mb-6">
              <Link href="/contact-us" passHref>
                <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto">
                  <MessageSquare className="mr-2 h-5 w-5" /> Contact Customer Service
                </Button>
              </Link>
              <Link href="/support/submit-ticket" passHref>
                <Button size="lg" variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto">
                  <FileText className="mr-2 h-5 w-5" /> Submit A Support Ticket
                </Button>
              </Link>
            </div>
            <Link href="/resources/guides" passHref>
              <Button variant="link" className="text-[#028475] hover:text-[#004235] text-lg">
                <LifeBuoy className="mr-2 h-5 w-5" /> Explore Our Portal Guides & Tutorials <ChevronRight className="ml-1 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </section>

      </main>
      <BottomFooter />
    </div>
  );
};

export default FaqsPage;