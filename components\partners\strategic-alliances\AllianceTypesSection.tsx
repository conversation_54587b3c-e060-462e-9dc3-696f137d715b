"use client";

import { Briefcase, Users, Settings, Landmark, Building, Globe } from 'lucide-react'; // Example icons

const allianceTypes = [
  {
    id: "joint-market-dev",
    icon: <Briefcase className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Joint Market Development & Go-To-Market Alliances",
    idealPartners: "Large industrial manufacturers, major commodity producers/traders, established global distributors, leading industry associations.",
    focus: "Co-launching StreamLnk services in new geographic regions or industry verticals, joint marketing campaigns, preferred access for partner's customer/member base.",
    example: "Partnering with a national chemical industry association to roll out StreamLnk as the preferred digital trading platform for its members."
  },
  {
    id: "tech-co-dev",
    icon: <Settings className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Technology Co-Development & Integration Alliances",
    idealPartners: "Leading enterprise software providers (ERP, SCM), major logistics technology firms, AI/Data analytics specialists, financial institutions with advanced trade finance capabilities.",
    focus: "Deeply integrating complementary technologies to create unique, end-to-end solutions. Co-developing new platform modules or data products (e.g., advanced risk engines, specialized ESG tracking).",
    example: "Collaborating with a major ERP provider to offer seamless, out-of-the-box integration between StreamLnk and their enterprise clients' systems."
  },
  {
    id: "joint-ventures",
    icon: <Users className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Joint Ventures for New Service Offerings",
    idealPartners: "Companies with specialized expertise in areas like trade finance, cargo insurance, quality inspection, or sustainable material certification.",
    focus: "Creating new, co-branded value-added services offered exclusively or preferentially through the StreamLnk platform.",
    example: "Forming a JV with a trade finance institution to offer tailored, embedded financing solutions directly within StreamLnk's transaction flow."
  },
  {
    id: "ecosystem-expansion",
    icon: <Building className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Ecosystem Expansion & M&A Alliances (Later Stage)",
    idealPartners: "Niche B2B marketplaces, specialized data providers, regional logistics platforms that could be integrated or acquired to accelerate StreamLnk's growth.",
    focus: "Strategic acquisitions or mergers to consolidate market share, acquire new technologies, or enter new verticals rapidly.",
    example: "Acquiring a regional logistics platform to quickly expand service coverage."
  },
  {
    id: "gov-regulatory",
    icon: <Landmark className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Government & Regulatory Body Alliances",
    idealPartners: "National customs authorities, port authorities, trade development agencies.",
    focus: "Collaborating on digitizing trade facilitation processes, enhancing compliance, promoting secure trade corridors, and leveraging StreamLnk data for economic insights.",
    example: "Working with a port authority to integrate StreamLnk for streamlined digital clearance and cargo tracking."
  }
];

export default function AllianceTypesSection() {
  return (
    <section className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12 md:mb-16">
          <Globe className="h-12 w-12 text-[#028475] mx-auto mb-6" />
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Pathways to Transformative Collaboration
          </h2>
          <p className="text-xl text-[#028475] font-semibold">
            Types of Strategic Alliances We Envision
          </p>
          <div className="w-24 h-1 bg-[#028475] mx-auto mt-4"></div>
          <p className="text-lg text-gray-700 mt-6 leading-relaxed">
            StreamLnk is open to exploring a range of strategic alliance models with organizations that share our vision and can bring complementary strengths:
          </p>
        </div>

        <div className="space-y-12 md:space-y-16">
          {allianceTypes.map((type) => (
            <div key={type.id} className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="flex flex-col md:flex-row items-start md:items-center mb-6">
                {type.icon} {/* Display icon on the left for larger screens */} 
                <h3 className="text-2xl font-semibold text-[#004235] md:ml-4">{type.title}</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                <div>
                  <h4 className="font-semibold text-[#004235] mb-2">Ideal Partners:</h4>
                  <p className="text-gray-600 leading-relaxed">{type.idealPartners}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-[#004235] mb-2">Focus:</h4>
                  <p className="text-gray-600 leading-relaxed">{type.focus}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-[#004235] mb-2">Example:</h4>
                  <p className="text-gray-600 leading-relaxed">{type.example}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}