import { Card, CardContent } from "@/components/ui/card"
import { Truck, ShieldCheck, CreditCard, Factory, LineChart } from "lucide-react"

export function UseCasesSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <div className="mb-12 text-center">
            <h2 className="text-3xl font-bold text-[#004235] mb-4">
              Example Use Cases – How Integration Creates Value
            </h2>
            <div className="w-20 h-1 bg-[#028475] mx-auto mb-6"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Truck className="h-8 w-8 text-[#028475] mr-3" />
                  <h3 className="text-lg font-bold text-[#004235]">Freight Integration</h3>
                </div>
                <p className="text-gray-700">
                  A freight aggregator embeds StreamLnk's real-time polymer delivery cost API into its platform,
                  enhancing route optimization and cost estimation.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <ShieldCheck className="h-8 w-8 text-[#028475] mr-3" />
                  <h3 className="text-lg font-bold text-[#004235]">Compliance Enhancement</h3>
                </div>
                <p className="text-gray-700">
                  A compliance partner enhances its underwriting tools by adding StreamLnk's transaction-based risk
                  scores, improving accuracy and reducing false positives.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <CreditCard className="h-8 w-8 text-[#028475] mr-3" />
                  <h3 className="text-lg font-bold text-[#004235]">Financial Services</h3>
                </div>
                <p className="text-gray-700">
                  A financial institution uses anonymized buyer intent data from StreamLnk to offer targeted trade
                  financing solutions at the optimal moment in the purchase cycle.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Factory className="h-8 w-8 text-[#028475] mr-3" />
                  <h3 className="text-lg font-bold text-[#004235]">Manufacturing Sync</h3>
                </div>
                <p className="text-gray-700">
                  A manufacturer syncs RFQ and order data from E-Stream directly into their internal ERP system for
                  streamlined operations and reduced manual data entry.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-sm md:col-span-2 lg:col-span-1">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <LineChart className="h-8 w-8 text-[#028475] mr-3" />
                  <h3 className="text-lg font-bold text-[#004235]">Analytics Provider</h3>
                </div>
                <p className="text-gray-700">
                  An analytics provider offers a specialized dashboard on chemical price volatility in specific regions,
                  using StreamLnk data to deliver unique market insights.
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="mt-12 p-6 bg-white rounded-lg shadow-sm border border-gray-200">
            <h3 className="text-xl font-bold text-[#004235] mb-4">Integration Success Story</h3>
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex-1">
                <p className="text-gray-700 mb-4">
                  <span className="font-semibold">Challenge:</span> A global logistics provider needed real-time
                  visibility into polymer shipment status across multiple carriers and regions.
                </p>
                <p className="text-gray-700 mb-4">
                  <span className="font-semibold">Solution:</span> Integration with StreamLnk's freight tracking APIs to
                  consolidate shipment data from various sources.
                </p>
              </div>
              <div className="flex-1">
                <p className="text-gray-700 mb-4">
                  <span className="font-semibold">Implementation:</span> A two-way data exchange was established, with
                  the logistics provider feeding tracking data into StreamLnk while consuming aggregated market
                  intelligence.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Result:</span> 42% improvement in delivery time accuracy and a new
                  revenue stream from premium data services for the logistics provider.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
