import { Button } from "@/components/ui/button";
import { Timeline } from "@/components/ui/timeline"; // Assuming a Timeline component exists and is suitable
import { FileSearch, Settings, MessageSquare, Code, Rocket, ArrowRight } from 'lucide-react';
import Link from "next/link";

export function GetStartedSection() {
  const steps = [
    {
      title: "Explore Our Developer Portal",
      description: "Access API documentation, sandbox environments, and use case examples.",
      icon: <FileSearch className="h-8 w-8 text-white" />,
    },
    {
      title: "Identify Integration Points",
      description: "Determine how your solution can best connect with StreamLnk workflows and data.",
      icon: <Settings className="h-8 w-8 text-white" />,
    },
    {
      title: "Contact Our Partnerships Team",
      description: "Discuss your integration proposal and technical requirements.",
      icon: <MessageSquare className="h-8 w-8 text-white" />,
    },
    {
      title: "Develop & Test",
      description: "Utilize our APIs and support resources to build and test your integration.",
      icon: <Code className="h-8 w-8 text-white" />,
    },
    {
      title: "Go Live & Promote",
      description: "Launch your integrated solution to mutual customers.",
      icon: <Rocket className="h-8 w-8 text-white" />,
    },
  ];

  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="w-10 h-1 bg-[#028475]"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            A Clear Path to Connecting Your Technology
          </h2>
          <p className="text-xl text-[#028475]">How to Get Started with API Integration</p>
        </div>

        <div className="max-w-4xl mx-auto mb-16">
          <Timeline steps={steps} />
        </div>


      </div>
    </section>
  );
}