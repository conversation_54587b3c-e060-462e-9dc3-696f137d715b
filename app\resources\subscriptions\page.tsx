"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/resources/subscriptions/HeroSection";
import WhySubscribeSection from "@/components/resources/subscriptions/WhySubscribeSection";
import SubscriptionTiersSection from "@/components/resources/subscriptions/SubscriptionTiersSection";
import HowToGetStartedSection from "@/components/resources/subscriptions/HowToGetStartedSection";
import FaqSection from "@/components/resources/subscriptions/FaqSection";
import CtaSection from "@/components/resources/subscriptions/CtaSection";

export default function StreamResourcesSubscriptionPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <WhySubscribeSection />
      <SubscriptionTiersSection />
      <HowToGetStartedSection />
      <FaqSection />
      <CtaSection />

      <BottomFooter />
    </div>
  );
}