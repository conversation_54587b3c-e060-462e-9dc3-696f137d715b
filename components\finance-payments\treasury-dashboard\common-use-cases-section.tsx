import {
  Building2,
  <PERSON><PERSON><PERSON><PERSON>,
  ShieldCheck,
  Globe,
} from "lucide-react"
import UseCaseCard from "@/components/finance-payments/treasury-dashboard/use-case-card"

export default function CommonUseCasesSection() {
  return (
    <section className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Common Use Cases</h2>
          <p className="text-gray-600 max-w-3xl">
            The Treasury & FX Dashboard serves various roles across your organization.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <UseCaseCard
            icon={<Building2 className="h-8 w-8 text-[#028475]" />}
            title="CFOs"
            description="Monitoring FX exposure by region and making strategic financial decisions"
          />
          <UseCaseCard
            icon={<FileCheck className="h-8 w-8 text-[#028475]" />}
            title="Finance Managers"
            description="Reconciling multi-currency payments and managing cash flow"
          />
          <UseCaseCard
            icon={<ShieldCheck className="h-8 w-8 text-[#028475]" />}
            title="Compliance Teams"
            description="Reviewing transaction logs and proof of payment documentation"
          />
          <UseCaseCard
            icon={<Globe className="h-8 w-8 text-[#028475]" />}
            title="Procurement Leaders"
            description="Tracking cleared vs. pending invoices across global operations"
          />
        </div>
      </div>
    </section>
  )
}