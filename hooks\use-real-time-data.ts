"use client"

import { useState, useEffect, useCallback, useRef } from "react"
import { partners, routes, regions } from "@/data/map-data"
import type { RealTimeData, LiveAsset, RegionalStats, LiveEvent } from "@/types/map-types"

interface UseRealTimeDataProps {
  isRealTimeEnabled: boolean
  updateInterval: number[]
  animationSpeed: number[]
  showAssets: boolean
}

export function useRealTimeData({
  isRealTimeEnabled,
  updateInterval,
  animationSpeed,
  showAssets,
}: UseRealTimeDataProps) {
  const animationFrameRef = useRef<number>()
  const lastUpdateRef = useRef<number>(0)
  const assetsRef = useRef<LiveAsset[]>([])

  const [realTimeData, setRealTimeData] = useState<RealTimeData>({
    assets: [],
    regionalStats: [],
    events: [],
    globalMetrics: {
      totalShipments: 0,
      totalValue: 0,
      averageDelay: 0,
      systemHealth: 100,
    },
    lastUpdate: new Date(),
  })

  // Generate mock real-time data
  const generateMockData = useCallback((): RealTimeData => {
    const assets: LiveAsset[] = []
    const regionalStats: RegionalStats[] = []
    const events: LiveEvent[] = []

    // Generate assets for active routes
    routes
      .filter((route) => route.status === "active")
      .forEach((route) => {
        const assetCount = route.intensity === "high" ? 3 : route.intensity === "medium" ? 2 : 1

        for (let i = 0; i < assetCount; i++) {
          const fromPartner = partners.find((p) => p.id === route.from)
          const toPartner = partners.find((p) => p.id === route.to)

          if (fromPartner && toPartner) {
            const assetType = route.services.includes("sea")
              ? "ship"
              : route.services.includes("rail")
                ? "train"
                : route.services.includes("land")
                  ? "truck"
                  : "ship"

            const progress = Math.random()
            const statuses = ["on-time", "delayed", "critical"] as const
            const status = statuses[Math.floor(Math.random() * statuses.length)]

            // Simple interpolation for current location
            const lat = fromPartner.coordinates[1] + (toPartner.coordinates[1] - fromPartner.coordinates[1]) * progress
            const lng = fromPartner.coordinates[0] + (toPartner.coordinates[0] - fromPartner.coordinates[0]) * progress

            assets.push({
              id: `${route.id}-asset-${i}`,
              type: assetType,
              routeId: route.id,
              progress,
              speed: 0.001 + Math.random() * 0.002,
              cargo: `Container ${Math.floor(Math.random() * 10000)}`,
              value: Math.floor(Math.random() * 1000000) + 100000,
              status,
              eta: new Date(Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
              currentLocation: [lng, lat],
              destination: toPartner.name,
              origin: fromPartner.name,
            })
          }
        }
      })

    // Generate regional stats
    regions.forEach((region) => {
      regionalStats.push({
        regionId: region.id,
        activeShipments: Math.floor(Math.random() * 100) + 20,
        totalValue: Math.floor(Math.random() * 10000000) + 1000000,
        averageDelay: Math.random() * 24,
        throughput: Math.floor(Math.random() * 1000) + 100,
        alerts: Math.floor(Math.random() * 5),
        efficiency: Math.floor(Math.random() * 20) + 80,
      })
    })

    // Generate random events
    const eventTypes = ["delay", "arrival", "departure", "alert", "milestone"] as const
    const severities = ["low", "medium", "high", "critical"] as const

    for (let i = 0; i < 5; i++) {
      const randomPartner = partners[Math.floor(Math.random() * partners.length)]
      const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)]
      const severity = severities[Math.floor(Math.random() * severities.length)]

      events.push({
        id: `event-${Date.now()}-${i}`,
        type: eventType,
        severity,
        message: `${eventType.charAt(0).toUpperCase() + eventType.slice(1)} at ${randomPartner.name}`,
        location: randomPartner.coordinates,
        timestamp: new Date(),
        autoHide: severity === "low",
      })
    }

    return {
      assets,
      regionalStats,
      events,
      globalMetrics: {
        totalShipments: assets.length,
        totalValue: assets.reduce((sum, asset) => sum + asset.value, 0),
        averageDelay: regionalStats.reduce((sum, stat) => sum + stat.averageDelay, 0) / regionalStats.length,
        systemHealth: Math.floor(Math.random() * 10) + 90,
      },
      lastUpdate: new Date(),
    }
  }, [])

  // Real-time data simulation
  useEffect(() => {
    if (!isRealTimeEnabled) return

    const interval = setInterval(() => {
      const newData = generateMockData()
      setRealTimeData(newData)
      assetsRef.current = newData.assets
    }, updateInterval[0])

    return () => clearInterval(interval)
  }, [isRealTimeEnabled, updateInterval, generateMockData])

  // Animation loop for moving assets
  useEffect(() => {
    if (!showAssets || !isRealTimeEnabled) return

    const animate = (timestamp: number) => {
      if (timestamp - lastUpdateRef.current > 50) {
        // Update at 20 FPS
        assetsRef.current = assetsRef.current.map((asset) => ({
          ...asset,
          progress: Math.min(1, asset.progress + asset.speed * animationSpeed[0]),
        }))

        // Force re-render by updating the real-time data
        setRealTimeData((prevData) => ({
          ...prevData,
          assets: [...assetsRef.current],
        }))

        lastUpdateRef.current = timestamp
      }
      animationFrameRef.current = requestAnimationFrame(animate)
    }

    animationFrameRef.current = requestAnimationFrame(animate)

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [showAssets, isRealTimeEnabled, animationSpeed])

  // Initialize real-time data
  useEffect(() => {
    const initialData = generateMockData()
    setRealTimeData(initialData)
    assetsRef.current = initialData.assets
  }, [generateMockData])

  return {
    realTimeData,
    assetsRef,
  }
}
