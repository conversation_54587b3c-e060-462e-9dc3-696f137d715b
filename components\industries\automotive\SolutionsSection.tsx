"use client"

import { CheckCircle, Package, Users, Truck, BarChart3, FileText, Leaf, Search } from "lucide-react"

const solutions = [
  {
    icon: <Package className="h-8 w-8 text-[#028475]" />,
    title: "E-Stream for Automotive Material Suppliers",
    points: [
      "List specialized polymers (e.g., PP, ABS, PC, PA for interiors/exteriors), engineering plastics, automotive-grade chemicals, metal components, and electronic parts with detailed specifications and OEM compliance certifications.",
      "Manage JIT inventory availability and provide accurate lead times.",
      "Utilize StreamIndex™ for insights into automotive material pricing trends.",
    ],
  },
  {
    icon: <Search className="h-8 w-8 text-[#028475]" />,
    title: "MyStreamLnk for Automotive OEMs & Tiered Suppliers (Buyers)",
    points: [
      "Advanced search to find suppliers meeting IATF 16949 or specific OEM requirements. Filter by material performance, certifications, and supplier iScore™.",
      "Streamlined RFQ and order management for both spot buys and long-term contracts.",
      "Source materials for EV production (battery casings, lightweight composites, specialized chemicals).",
    ],
  },
  {
    icon: <Truck className="h-8 w-8 text-[#028475]" />,
    title: "Integrated Logistics for JIT (StreamFreight, StreamGlobe+, StreamPak)",
    points: [
      "Coordinate time-sensitive domestic and international freight with real-time tracking and predictive ETAs crucial for JIT.",
      "Manage customs clearance efficiently to avoid port delays.",
      "Utilize StreamPak for sequenced kitting, sub-assembly, or just-in-sequence warehousing near assembly plants.",
    ],
  },
  {
    icon: <FileText className="h-8 w-8 text-[#028475]" />,
    title: "Enhanced Quality & Compliance Management",
    points: [
      "Centralized document vault for CoAs, PPAP documentation (if applicable), and compliance certificates.",
      "iScore™ ratings help vet supplier reliability and quality history.",
      "Traceability features (future) to track component batch origins.",
    ],
  },
  {
    icon: <Leaf className="h-8 w-8 text-[#028475]" />,
    title: "ESG Tracking for Automotive",
    points: [
      "Source materials with high recycled content or lower carbon footprints.",
      "Track the ESG impact of sourced components and logistics for corporate sustainability reporting.",
    ],
  },
  {
    icon: <BarChart3 className="h-8 w-8 text-[#028475]" />,
    title: "StreamResources+ for Automotive Market Intelligence",
    points: [
      "Insights into automotive material price volatility, supply chain risks specific to the sector, and demand forecasts for key components.",
    ],
  },
]

export default function SolutionsSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Your Digital Co-Pilot for a More Resilient and Efficient Automotive Value Chain
          </h2>
          <p className="text-xl text-[#028475] font-semibold mb-6">
            StreamLnk's Tailored Solutions for the Automotive Sector
          </p>
          <p className="text-lg text-gray-700">
            StreamLnk provides specialized tools and a connected ecosystem designed to meet the rigorous demands of the automotive industry:
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {solutions.map((solution, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235]/10 p-3 rounded-full mr-4">
                  {solution.icon}
                </div>
                <h3 className="text-xl font-bold text-[#004235]">{solution.title}</h3>
              </div>
              <ul className="space-y-2">
                {solution.points.map((point, pointIndex) => (
                  <li key={pointIndex} className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2 mt-1 flex-shrink-0" />
                    <span className="text-gray-700">{point}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}