import { UserPlus, LogIn, Info, MessageCircleQuestion } from "lucide-react";

export default function GettingStartedGuideSection() {
  const steps = [
    {
      icon: <UserPlus className="h-8 w-8 text-white" />,
      title: "Easy & Secure Registration",
      description: "Our onboarding process is designed to be straightforward yet thorough, ensuring all new members are properly vetted.",
    },
    {
      icon: <LogIn className="h-8 w-8 text-white" />,
      title: "Personalized Portal Access",
      description: "Once verified, you gain access to your dedicated StreamLnk portal (MyStreamLnk, E-Stream, etc.) with tools tailored to your role.",
    },
    {
      icon: <Info className="h-8 w-8 text-white" />,
      title: "Transparent Information",
      description: "From supplier profiles and product specifications to pricing and logistics, access the information you need to make confident decisions.",
    },
    {
      icon: <MessageCircleQuestion className="h-8 w-8 text-white" />,
      title: "Support Every Step of the Way",
      description: "Our team is here to assist you with onboarding, platform navigation, and any questions you may have.",
    },
  ];

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Begin Your Secure and Transparent Trading Experience
          </h2>
          <p className="text-xl text-[#028475] font-semibold">
            Getting Started with StreamLnk – A Journey Built on Confidence
          </p>
        </div>

        <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="flex items-start space-x-4 p-6 bg-[#f3f4f6] rounded-lg shadow-md">
              <div className="flex-shrink-0 bg-[#004235] text-white rounded-full p-3">
                {step.icon}
              </div>
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">{step.title}</h3>
                <p className="text-gray-700">{step.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}