import Image from "next/image"
import Link from "next/link"
import { ChevronRight, Search } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { CountrySelector } from "@/components/country-selector"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"

export default function PressReleasesPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <CountrySelector />
      <MainNav />

      <main>
        {/* Press Releases Header */}
        <section className="py-8 md:py-12">
          <div className="container mx-auto px-4">
            <h1 className="text-3xl md:text-4xl font-bold text-[#004235] mb-8">Press Releases</h1>
            
            {/* Filter and Search */}
            <div className="flex flex-col md:flex-row gap-4 mb-8">
              <div className="w-full md:w-48">
                <select className="w-full h-10 px-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#18b793]">
                  <option>Category</option>
                  <option>Corporate News</option>
                  <option>Product Updates</option>
                  <option>Industry News</option>
                  <option>Events</option>
                </select>
              </div>
              <div className="flex-1 flex">
                <Input 
                  type="text" 
                  placeholder="Search" 
                  className="rounded-r-none focus-visible:ring-0 focus-visible:ring-transparent" 
                />
                <Button className="bg-[#18b793] hover:bg-[#14a583] rounded-l-none">
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            {/* Press Release Cards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Press Release Card 1 */}
              <div className="border rounded-md overflow-hidden flex">
                <div className="w-1/3 relative">
                  <Image 
                    src="/images/press-releases/Press Releases image.webp" 
                    alt="Press release image" 
                    width={200} 
                    height={200} 
                    className="object-cover h-full"
                  />
                </div>
                <div className="w-2/3 p-6">
                  <h3 className="font-bold mb-2">Press title here</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et...
                  </p>
                  <Link href="#" className="text-[#18b793] font-medium flex items-center text-sm">
                    <ChevronRight className="h-4 w-4 mr-1" />
                  </Link>
                </div>
              </div>
              
              {/* Press Release Card 2 */}
              <div className="border rounded-md overflow-hidden flex">
                <div className="w-1/3 relative">
                  <Image 
                    src="/images/press-releases/Press Releases image.webp" 
                    alt="Press release image" 
                    width={200} 
                    height={200} 
                    className="object-cover h-full"
                  />
                </div>
                <div className="w-2/3 p-6">
                  <h3 className="font-bold mb-2">Press title here</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et...
                  </p>
                  <Link href="#" className="text-[#18b793] font-medium flex items-center text-sm">
                    <ChevronRight className="h-4 w-4 mr-1" />
                  </Link>
                </div>
              </div>
              
              {/* Press Release Card 3 */}
              <div className="border rounded-md overflow-hidden flex">
                <div className="w-1/3 relative">
                  <Image 
                    src="/images/press-releases/Press Releases image.webp" 
                    alt="Press release image" 
                    width={200} 
                    height={200} 
                    className="object-cover h-full"
                  />
                </div>
                <div className="w-2/3 p-6">
                  <h3 className="font-bold mb-2">Press title here</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et...
                  </p>
                  <Link href="#" className="text-[#18b793] font-medium flex items-center text-sm">
                    <ChevronRight className="h-4 w-4 mr-1" />
                  </Link>
                </div>
              </div>
              
              {/* Press Release Card 4 */}
              <div className="border rounded-md overflow-hidden flex">
                <div className="w-1/3 relative">
                  <Image 
                    src="/images/press-releases/Press Releases image.webp" 
                    alt="Press release image" 
                    width={200} 
                    height={200} 
                    className="object-cover h-full"
                  />
                </div>
                <div className="w-2/3 p-6">
                  <h3 className="font-bold mb-2">Press title here</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et...
                  </p>
                  <Link href="#" className="text-[#18b793] font-medium flex items-center text-sm">
                    <ChevronRight className="h-4 w-4 mr-1" />
                  </Link>
                </div>
              </div>
              
              {/* Press Release Card 5 */}
              <div className="border rounded-md overflow-hidden flex">
                <div className="w-1/3 relative">
                  <Image 
                    src="/images/press-releases/Press Releases image.webp" 
                    alt="Press release image" 
                    width={200} 
                    height={200} 
                    className="object-cover h-full"
                  />
                </div>
                <div className="w-2/3 p-6">
                  <h3 className="font-bold mb-2">Press title here</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et...
                  </p>
                  <Link href="#" className="text-[#18b793] font-medium flex items-center text-sm">
                    <ChevronRight className="h-4 w-4 mr-1" />
                  </Link>
                </div>
              </div>
              
              {/* Press Release Card 6 */}
              <div className="border rounded-md overflow-hidden flex">
                <div className="w-1/3 relative">
                  <Image 
                    src="/images/press-releases/Press Releases image.webp" 
                    alt="Press release image" 
                    width={200} 
                    height={200} 
                    className="object-cover h-full"
                  />
                </div>
                <div className="w-2/3 p-6">
                  <h3 className="font-bold mb-2">Press title here</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et...
                  </p>
                  <Link href="#" className="text-[#18b793] font-medium flex items-center text-sm">
                    <ChevronRight className="h-4 w-4 mr-1" />
                  </Link>
                </div>
              </div>
              
              {/* Press Release Card 7 */}
              <div className="border rounded-md overflow-hidden flex">
                <div className="w-1/3 relative">
                  <Image 
                    src="/images/press-releases/Press Releases image.webp" 
                    alt="Press release image" 
                    width={200} 
                    height={200} 
                    className="object-cover h-full"
                  />
                </div>
                <div className="w-2/3 p-6">
                  <h3 className="font-bold mb-2">Press title here</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et...
                  </p>
                  <Link href="#" className="text-[#18b793] font-medium flex items-center text-sm">
                    <ChevronRight className="h-4 w-4 mr-1" />
                  </Link>
                </div>
              </div>
              
              {/* Press Release Card 8 */}
              <div className="border rounded-md overflow-hidden flex">
                <div className="w-1/3 relative">
                  <Image 
                    src="/images/press-releases/Press Releases image.webp" 
                    alt="Press release image" 
                    width={200} 
                    height={200} 
                    className="object-cover h-full"
                  />
                </div>
                <div className="w-2/3 p-6">
                  <h3 className="font-bold mb-2">Press title here</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et...
                  </p>
                  <Link href="#" className="text-[#18b793] font-medium flex items-center text-sm">
                    <ChevronRight className="h-4 w-4 mr-1" />
                  </Link>
                </div>
              </div>
            </div>
            
            {/* Load More Button */}
            <div className="flex justify-center mt-8">
              <Button variant="outline" className="border-[#18b793] text-[#18b793] hover:bg-[#18b793] hover:text-white">
                Load more
              </Button>
            </div>
          </div>
        </section>
        
        {/* Media Center Section */}
        <section className="py-16 md:py-24 bg-gray-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-[#004235] mb-4">Media Center</h2>
            <p className="text-gray-600 mb-12 max-w-3xl">
              This resource center for journalists featuring our latest press releases, useful multimedia materials, a list of press contacts and a direct line to our spokespeople.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Media Item 1 */}
              <div>
                <div className="mb-4 overflow-hidden rounded-md">
                  <Image 
                    src="/images/press-releases/Media Center image.webp" 
                    alt="Media center image" 
                    width={400} 
                    height={300} 
                    className="w-full object-cover transition-transform hover:scale-105 duration-300"
                  />
                </div>
                <h3 className="font-bold mb-2 flex items-center">
                  Media title here <ChevronRight className="h-4 w-4 ml-1 text-[#18b793]" />
                </h3>
                <p className="text-sm text-gray-600">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                </p>
              </div>
              
              {/* Media Item 2 */}
              <div>
                <div className="mb-4 overflow-hidden rounded-md">
                  <Image 
                    src="/images/press-releases/Media Center image.webp" 
                    alt="Media center image" 
                    width={400} 
                    height={300} 
                    className="w-full object-cover transition-transform hover:scale-105 duration-300"
                  />
                </div>
                <h3 className="font-bold mb-2 flex items-center">
                  Media title here <ChevronRight className="h-4 w-4 ml-1 text-[#18b793]" />
                </h3>
                <p className="text-sm text-gray-600">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                </p>
              </div>
              
              {/* Media Item 3 */}
              <div>
                <div className="mb-4 overflow-hidden rounded-md">
                  <Image 
                    src="/images/press-releases/Media Center image.webp" 
                    alt="Media center image" 
                    width={400} 
                    height={300} 
                    className="w-full object-cover transition-transform hover:scale-105 duration-300"
                  />
                </div>
                <h3 className="font-bold mb-2 flex items-center">
                  Media title here <ChevronRight className="h-4 w-4 ml-1 text-[#18b793]" />
                </h3>
                <p className="text-sm text-gray-600">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>

      <MainFooter />
    </div>
  )
}