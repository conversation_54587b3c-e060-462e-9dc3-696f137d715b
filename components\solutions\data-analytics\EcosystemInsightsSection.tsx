import { BarChart2, <PERSON><PERSON><PERSON>Up, <PERSON>Bar, Database } from "lucide-react"

export default function EcosystemInsightsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]" id="explore">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Harness the Power of Collective Intelligence: Insights from the StreamLnk Ecosystem
          </h2>
          <p className="text-lg text-gray-700">
            StreamResources+, StreamLnk's dedicated data and analytics division, transforms the vast transactional, operational, and behavioral data generated across our entire platform into powerful, actionable intelligence.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {/* StreamIndex Market Benchmarks */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex items-center mb-4">
              <BarChart2 className="h-8 w-8 text-[#004235] mr-3" />
              <h3 className="text-xl font-semibold text-[#004235]">StreamIndex™ Market Benchmarks</h3>
            </div>
            <p className="text-gray-700 mb-4">Proprietary, real-time indices for:</p>
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>Product Pricing: Average market prices and volatility by SKU, grade, region, Incoterm.</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>Logistics Efficiency: Average transit times, carrier performance, customs clearance speeds by lane/port.</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>Supplier Reliability: Aggregated scores on on-time performance, quality, and compliance.</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>Buyer Demand Signals: Trends in RFQ volumes, auction participation, and search activity.</span>
              </li>
            </ul>
          </div>

          {/* Predictive Analytics & Forecasting */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex items-center mb-4">
              <TrendingUp className="h-8 w-8 text-[#004235] mr-3" />
              <h3 className="text-xl font-semibold text-[#004235]">Predictive Analytics & Forecasting</h3>
            </div>
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>Demand forecasting for specific products and regions.</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>Price trend predictions.</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>Logistics delay probability assessments.</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>Supplier and buyer risk scoring (iScore™).</span>
              </li>
            </ul>
          </div>

          {/* Comprehensive Performance Dashboards */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex items-center mb-4">
              <ChartBar className="h-8 w-8 text-[#004235] mr-3" />
              <h3 className="text-xl font-semibold text-[#004235]">Comprehensive Performance Dashboards</h3>
            </div>
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>For Suppliers (E-Stream Premium): Track sales performance, quote conversion, inventory velocity, and benchmark against anonymized market averages.</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>For Buyers (MyStreamLnk Premium): Monitor spend analytics, supplier performance, landed cost trends, and ESG impact.</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>For Logistics Partners (Respective Portals - Premium Tier): View operational KPIs, lane efficiency, and service level adherence.</span>
              </li>
            </ul>
          </div>

          {/* Customizable Reports & Data Exports */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex items-center mb-4">
              <Database className="h-8 w-8 text-[#004235] mr-3" />
              <h3 className="text-xl font-semibold text-[#004235]">Customizable Reports & Data Exports</h3>
            </div>
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>Generate tailored reports or access aggregated, anonymized data via API (DaaS) for integration into your own BI tools.</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>Market Intelligence Reports: Regular publications on industry trends, regional spotlights, and deep-dive analyses of specific commodities.</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  )
}