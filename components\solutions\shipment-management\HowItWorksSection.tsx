import { WorkflowTimeline, WorkflowStep } from "@/components/ui/WorkflowTimeline";

export default function HowItWorksSection() {
  const timelineSteps: WorkflowStep[] = [
    {
      number: 1,
      title: "Capturing Order Data",
      description: "When an order is placed on E-Stream or MyStreamLnk."
    },
    {
      number: 2,
      title: "Integrating with Logistics Portals",
      description: "StreamFreight provides real-time updates from land carriers, StreamGlobe feeds live data from sea freight carriers, and StreamPak provides updates on packaging completion."
    },
    {
      number: 3,
      title: "AI-Powered Aggregation",
      description: "Our system intelligently aggregates these data points into a coherent, end-to-end view for each shipment."
    },
    {
      number: 4,
      title: "Proactive Notifications",
      description: "Users are alerted to key status changes and potential issues without needing to manually check multiple systems."
    }
  ];

  return (
    <WorkflowTimeline
      title="Connecting the Dots for Complete Transparency"
      subtitle="StreamLnk achieves comprehensive shipment management by:"
      steps={timelineSteps}
    />
  );
}