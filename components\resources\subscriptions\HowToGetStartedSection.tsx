"use client";

import { List<PERSON>he<PERSON>, LogIn, SquareDashedMousePointer, SearchCode, Settings2 } from 'lucide-react';

const steps = [
  {
    icon: <ListChecks className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Choose Your Plan",
    description: "Review our subscription tiers and select the one that best matches your analytical needs and budget."
  },
  {
    icon: <LogIn className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Sign Up / Upgrade",
    description: "Existing StreamLnk portal users can often upgrade directly from their account settings. New users can subscribe directly."
  },
  {
    icon: <SquareDashedMousePointer className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Access Your Portal",
    description: "Log in to the dedicated StreamResources+ portal with your credentials."
  },
  {
    icon: <Settings2 className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Explore & Customize",
    description: "Utilize the dashboards, filtering tools, and reporting features. Enterprise users can begin API integration."
  },
  {
    icon: <SearchCode className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Leverage Insights",
    description: "Start making more informed, data-driven decisions."
  }
];

export default function HowToGetStartedSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">
            Accessing Your Premium Intelligence is Simple
          </h2>
          <p className="text-xl text-[#028475] font-semibold">
            How to Get Started with StreamResources+
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {steps.map((step, index) => (
            <div 
              key={index} 
              className="bg-[#f3f4f6] p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow duration-300 flex flex-col items-center"
            >
              <div className="mb-4">
                {step.icon}
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">{step.title}</h3>
              <p className="text-gray-600 text-sm">
                {step.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}