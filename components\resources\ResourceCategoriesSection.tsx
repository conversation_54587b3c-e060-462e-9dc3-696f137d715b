"use client";

import Link from "next/link";
import {
  Newspaper,
  FileText,
  Award,
  Youtube,
  HelpCircle,
  LineChart,
  Globe,
  Code,
  ArrowRight
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

const resourceCategories = [
  {
    icon: <Newspaper className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Delivered by StreamLnk (Blog & Magazine)",
    description: "Read our latest articles, expert interviews, and thought leadership pieces on trends shaping global trade, technology, sustainability, and compliance.",
    linkText: "Explore Articles",
    href: "/blog" // Placeholder, update as needed
  },
  {
    icon: <FileText className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Whitepapers & In-Depth Reports",
    description: "Download comprehensive guides and research reports on critical topics like supply chain digitization, risk management, and sector-specific market outlooks.",
    linkText: "Access Whitepapers",
    href: "/resources/whitepapers" // Placeholder
  },
  {
    icon: <Award className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Case Studies",
    description: "See real-world examples of how businesses are leveraging StreamLnk to achieve efficiency, cost savings, and global growth.",
    linkText: "View Case Studies",
    href: "/resources/case-studies" // Placeholder
  },
  {
    icon: <Youtube className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Webinars & Event Recordings",
    description: "Access on-demand recordings of our past webinars, expert panels, and keynotes from industry events.",
    linkText: "Watch Webinars",
    href: "/resources/webinars" // Placeholder
  },
  {
    icon: <HelpCircle className="h-10 w-10 text-[#028475] mb-4" />,
    title: "StreamLnk Platform Guides & Tutorials",
    description: "Find user manuals, how-to guides, and video tutorials to help you master the features of MyStreamLnk, E-Stream, and all our specialized portals.",
    linkText: "Go to Platform Support",
    href: "/support/platform-guides" // Placeholder
  },
  {
    icon: <LineChart className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Tools & Market Data (StreamIndex™ & iScore™ Info)",
    description: "Learn more about our proprietary StreamIndex™ market benchmarks and iScore™ partner rating system, and how to access premium data via StreamResources+.",
    linkText: "Discover Our Data Tools",
    href: "/solutions/streamresources" // Placeholder
  },
  {
    icon: <Globe className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Trade & Compliance Resources",
    description: "Access a glossary of trade terms, guides on international shipping Incoterms, and summaries of key compliance regulations.",
    linkText: "Explore Compliance Guides",
    href: "/resources/compliance" // Placeholder
  },
  {
    icon: <Code className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Developer Portal (API Documentation)",
    description: "For tech partners and enterprise clients looking to integrate with StreamLnk via our APIs. Access documentation, sandboxes, and support.",
    linkText: "Visit Developer Portal",
    href: "/developers" // Placeholder
  }
];

export default function ResourceCategoriesSection() {
  return (
    <section className="py-16 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {resourceCategories.map((category, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-md flex flex-col items-start h-full">
              {category.icon}
              <h3 className="font-semibold text-[#004235] text-lg mb-2">{category.title}</h3>
              <p className="text-gray-600 text-sm mb-4 flex-grow">{category.description}</p>
              <Button variant="link" className="text-[#028475] hover:text-[#004235] p-0 mt-auto" asChild>
                <Link href={category.href}>
                  {category.linkText}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}