"use client"

import { MainNav } from "@/components/main-nav"
import { BottomFooter } from "@/components/bottom-footer"
import { HeroSection } from "@/components/partners/api-integration/hero-section"
import { WhyPartnerSection } from "@/components/partners/api-integration/why-partner-section"
import { PartnershipTypesSection } from "@/components/partners/api-integration/partnership-types-section"
import { DeveloperToolsSection } from "@/components/partners/api-integration/developer-tools-section"
import { MonetizationSection } from "@/components/partners/api-integration/monetization-section"
import { UseCasesSection } from "@/components/partners/api-integration/use-cases-section"
import { ResourcesSection } from "@/components/partners/api-integration/resources-section"
import { GetStartedSection } from "@/components/partners/api-integration/get-started-section"
import { CtaSection } from "@/components/partners/api-integration/cta-section"

export default function ApiIntegrationPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <MainNav />
      <main>
        <HeroSection />
        <WhyPartnerSection />
        <PartnershipTypesSection />
        <DeveloperToolsSection />
        <MonetizationSection />
        <UseCasesSection />
        <ResourcesSection />
        <GetStartedSection />
        <CtaSection />
      </main>
      <BottomFooter />
    </div>
  )
}
