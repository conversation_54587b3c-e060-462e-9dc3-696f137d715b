import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Expand Your Business, Empower Global Trade: Become a StreamLnk Channel Partner
            </h1>
            <p className="text-lg md:text-xl text-gray-700 mb-10">
              Leverage your industry expertise and regional network. Partner with StreamLnk as an Independent Agent or Official Regional Distributor to bring our transformative B2B ecosystem to businesses in your market and build a recurring revenue stream.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white px-8 py-3" asChild>
                <Link href="#join-partner-program"> {/* Placeholder Link */}
                  JOIN PROGRAM <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-8 py-3"
                asChild
              >
                <Link href="/portals#mystreamlnkplus"> {/* Link to MyStreamLnk+ section on portals page */}
                  LEARN MORE
                </Link>
              </Button>
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[450px] rounded-lg overflow-hidden shadow-xl mt-12 lg:mt-0">
            <Image
              src="/images/placeholder-channel-partner.webp" // TODO: User to replace with a relevant image for channel partners
              alt="StreamLnk Channel Partner Program"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: User to replace with a relevant image for channel partners */}
          </div>
        </div>
      </div>
    </section>
  );
}