import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";

// TODO: Create these components
// import TrackingForm from '@/components/track/tracking-form';
// import TrackingResults from '@/components/track/tracking-results';
// import TrackingMilestones from '@/components/track/tracking-milestones';

export default function TrackShipmentPage() {
  // Mock data for example display - replace with actual data fetching and state management
  const shipmentFound = true; // or false
  const shipmentDetails = {
    trackingId: "SLNK1234567890",
    orderNumber: "ORD987654321",
    status: "In Transit",
    origin: "Rotterdam, Netherlands",
    destination: "Houston, USA",
    eta: "November 5, 2023",
    lastUpdate: "October 27, 2023, 14:30 GMT",
    progress: [
      { name: "Order Confirmed", completed: true },
      { name: "Ready for Pickup / Picked Up by Carrier", completed: true },
      { name: "In Transit", completed: true, subSteps: ["Departed Rotterdam Port", "At Sea"] },
      { name: "Customs Submitted / Customs Cleared", completed: false, current: true }, // Example of current stage
      { name: "At Destination Hub / Out for Delivery", completed: false },
      { name: "Delivered", completed: false },
    ],
    eventHistory: [
      { dateTime: "Oct 27, 2023, 14:30 GMT", location: "Rotterdam Port", event: "Container Discharged from Vessel" },
      { dateTime: "Oct 27, 2023, 12:00 GMT", location: "Rotterdam Port", event: "Customs Declaration Submitted" },
      { dateTime: "Oct 26, 2023, 08:00 GMT", location: "At Sea", event: "Vessel en route to Rotterdam, ETA Oct 27" },
      { dateTime: "Oct 20, 2023, 16:45 GMT", location: "Shanghai Port", event: "Loaded on Vessel - MSC GLORY (Voyage GL987)" },
      { dateTime: "Oct 20, 2023, 10:00 GMT", location: "Shanghai Port", event: "Container Gate-In at Port Terminal" },
      { dateTime: "Oct 18, 2023, 11:30 GMT", location: "Supplier Warehouse, Shanghai", event: "Picked Up by Carrier (Trucking Co. XYZ)" },
      { dateTime: "Oct 17, 2023, 09:00 GMT", location: "Supplier Warehouse, Shanghai", event: "Ready for Pickup" },
    ],
    carrierInfo: {
      seaCarrier: "MSC Line",
      landCarrier: "XYZ Logistics",
    },
  };

  const trackingMilestones = [
    { term: "Order Confirmed", definition: "Your order has been processed and accepted by the supplier." },
    { term: "Ready for Pickup", definition: "Goods are prepared and awaiting collection by the assigned carrier." },
    { term: "In Transit", definition: "Your shipment is on its way via the designated transport mode (sea, land, rail)." },
    { term: "Customs Submitted", definition: "Necessary documents have been sent to customs authorities for clearance." },
    { term: "Customs Cleared", definition: "Your shipment has successfully passed through customs procedures at the port of entry/exit." },
    { term: "Out for Delivery", definition: "Your shipment is with the final-mile carrier and en route to your specified delivery address." },
    { term: "Delivered", definition: "Your shipment has arrived safely at its destination." },
  ];

  return (
    <div className="flex flex-col min-h-screen">
      <MainNav />
      <main className="flex-grow">
        <section className="bg-gray-100 py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <div className="max-w-3xl mx-auto text-center">
              <h1 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
                Track Your StreamLnk Shipment: Real-Time Visibility, End-to-End.
              </h1>
              <p className="text-lg text-gray-700 mb-8">
                Stay informed every step of the way. Enter your StreamLnk Tracking ID, Order Number, or other reference to get live updates on your industrial material shipment's journey across our global network.
              </p>
            </div>
          </div>
        </section>

        <section className="py-12 md:py-16">
          <div className="container mx-auto px-4 md:px-6">
            <div className="max-w-xl mx-auto bg-white p-6 md:p-8 rounded-lg shadow-lg">
              <h2 className="text-2xl font-semibold text-[#004235] mb-6">Enter Your Tracking Information</h2>
              <div className="space-y-4">
                <div>
                  <label htmlFor="tracking-input" className="block text-sm font-medium text-gray-700 mb-1">
                    Enter StreamLnk Tracking ID, Order Number, PO Number, or Bill of Lading Number
                  </label>
                  <Input type="text" id="tracking-input" placeholder="e.g., SLNK1234567890" className="w-full" />
                </div>
                <Button className="w-full bg-[#004235] hover:bg-[#028475] text-white">
                  TRACK SHIPMENT
                </Button>
              </div>
              <div className="mt-6 text-sm text-gray-600">
                <p className="mb-2">Don't have a tracking number? Try searching by:</p>
                <div className="flex flex-col sm:flex-row sm:space-x-4 space-y-2 sm:space-y-0">
                  <Link href="#" className="text-[#028475] hover:text-[#004235] hover:underline">
                    Shipper Reference
                  </Link>
                  <Link href="#" className="text-[#028475] hover:text-[#004235] hover:underline">
                    Consignee Reference
                  </Link>
                  <Link href="/login" className="text-[#028475] hover:text-[#004235] hover:underline">
                    Login to MyStreamLnk for Full Order History & Tracking
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section id="tracking-results" className="py-12 md:py-16 bg-gray-50">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-2xl md:text-3xl font-semibold text-[#004235] mb-8 text-center">Tracking Results</h2>
            {/* This section will dynamically populate after a successful search */}
            {!shipmentFound && (
              <div className="max-w-2xl mx-auto bg-white p-6 md:p-8 rounded-lg shadow-md text-center">
                <p className="text-gray-700 mb-4">
                  No shipment found for the provided information. Please check your details and try again, or contact your StreamLnk representative/Customer Service for assistance.
                </p>
                <Link href="/contact-us">
                  <Button variant="link" className="text-[#028475] hover:text-[#004235]">Contact Support</Button>
                </Link>
              </div>
            )}

            {shipmentFound && shipmentDetails && (
              <div className="max-w-4xl mx-auto bg-white p-6 md:p-8 rounded-lg shadow-lg">
                <div className="grid md:grid-cols-2 gap-6 mb-8">
                  <div>
                    <h3 className="text-xl font-semibold text-[#004235] mb-3">Shipment Overview</h3>
                    <p><strong>StreamLnk Tracking ID:</strong> {shipmentDetails.trackingId}</p>
                    <p><strong>Order Number:</strong> {shipmentDetails.orderNumber}</p>
                    <p><strong>Status:</strong> <span className="font-bold text-green-600">{shipmentDetails.status}</span></p>
                    <p><strong>Origin:</strong> {shipmentDetails.origin}</p>
                    <p><strong>Destination:</strong> {shipmentDetails.destination}</p>
                    <p><strong>Estimated Delivery Date (ETA):</strong> {shipmentDetails.eta} <span className="text-xs text-gray-500">(Subject to change)</span></p>
                    <p><strong>Last Update:</strong> {shipmentDetails.lastUpdate}</p>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-[#004235] mb-3">Carrier Information</h3>
                    <p><strong>Main Sea Carrier:</strong> {shipmentDetails.carrierInfo.seaCarrier}</p>
                    <p><strong>Main Land Carrier:</strong> {shipmentDetails.carrierInfo.landCarrier}</p>
                    <div className="mt-4 space-x-3">
                       <Button className="bg-[#004235] hover:bg-[#028475] text-white text-sm">
                        Refresh Status
                      </Button>
                      <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white text-sm">
                        Sign Up for Updates
                      </Button>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-4">Shipment Progress Timeline</h3>
                  <ul className="space-y-3 mb-8">
                    {shipmentDetails.progress.map((item, index) => (
                      <li key={index} className={`flex items-start ${item.completed ? 'text-green-600' : item.current ? 'text-blue-600 font-semibold' : 'text-gray-500'}`}>
                        <span className="mr-2 mt-1">
                          {item.completed ? (
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-check-circle-2"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"/><path d="m9 12 2 2 4-4"/></svg>
                          ) : item.current ? (
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-loader-2"><path d="M21 12a9 9 0 1 1-6.219-8.56"/></svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-circleDashed"><path d="M10.1 2.182a10 10 0 0 1 3.8 0"/><path d="M13.9 21.818a10 10 0 0 1-3.8 0"/><path d="M21.818 13.9a10 10 0 0 1 0-3.8"/><path d="M2.182 10.1a10 10 0 0 1 0 3.8"/><path d="M17.609 3.721a10 10 0 0 1 2.69 2.69"/><path d="M3.721 6.391A10 10 0 0 1 6.41 3.72"/><path d="M6.391 20.279a10 10 0 0 1-2.69-2.69"/><path d="M20.279 17.609a10 10 0 0 1-2.69 2.69"/></svg>
                          )}
                        </span>
                        <div>
                          {item.name}
                          {item.subSteps && (
                            <ul className="ml-6 mt-1 text-sm list-disc list-inside">
                              {item.subSteps.map((subStep, subIndex) => (
                                <li key={subIndex}>{subStep}</li>
                              ))}
                            </ul>
                          )}
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-4">Detailed Event History</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {shipmentDetails.eventHistory.map((event, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{event.dateTime}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{event.location}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{event.event}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}
          </div>
        </section>

        <section className="py-12 md:py-16">
          <div className="container mx-auto px-4 md:px-6">
            <div className="max-w-3xl mx-auto">
              <h2 className="text-2xl md:text-3xl font-semibold text-[#004235] mb-6 text-center">What Do Our Tracking Milestones Mean?</h2>
              <p className="text-lg text-gray-700 mb-8 text-center">Understanding Your Shipment Status</p>
              <div className="space-y-4">
                {trackingMilestones.map((milestone, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow">
                    <h4 className="font-semibold text-[#028475]">{milestone.term}</h4>
                    <p className="text-sm text-gray-600">{milestone.definition}</p>
                  </div>
                ))}
              </div>
              <div className="text-center mt-8">
                <Link href="/resources/glossary" className="text-[#028475] hover:text-[#004235] hover:underline">
                  View Full Glossary of Shipping Terms
                </Link>
              </div>
            </div>
          </div>
        </section>

        <section className="bg-[#F2F2F2] py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-2xl md:text-3xl font-semibold text-[#004235] mb-4">Need More Help with Your Shipment?</h2>
              <p className="text-lg text-gray-700 mb-8">We're Here to Assist</p>
              <p className="text-gray-600 mb-6">
                If you have specific questions about your shipment that aren't answered here, or if you're experiencing an issue:
              </p>
              <p className="text-gray-600 mb-2">
                <strong>Registered Users:</strong> Please log in to your <Link href="/login" className="text-[#028475] hover:text-[#004235] hover:underline">MyStreamLnk Portal</Link> for more detailed information and direct support options.
              </p>
              <p className="text-gray-600 mb-8">
                <strong>All Users:</strong> You can contact our <Link href="/contact-us" className="text-[#028475] hover:text-[#004235] hover:underline">Customer Service Team</Link> for assistance. Please have your tracking or order number ready.
              </p>
              <div className="space-y-3 sm:space-y-0 sm:space-x-4">
                <Link href="/login">
                  <Button className="bg-[#004235] hover:bg-[#028475] text-white">
                    LOGIN TO MYSTREAMLNK
                  </Button>
                </Link>
                <Link href="/contact-us">
                  <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white">
                    CONTACT CUSTOMER SERVICE
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>
      <MainFooter />
    </div>
  );
}