import { BarChart3, Clock, <PERSON> } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export default function WhatIsSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            What Is the StreamLnk Auction Marketplace?
          </h2>
          <p className="text-lg text-gray-700">
            The StreamLnk Auction Marketplace is a sophisticated, predictive, real-time bidding environment seamlessly
            integrated across multiple core StreamLnk portals.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <Card className="border-2 border-[#F2F2F2] shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="rounded-full bg-[#004235]/10 w-12 h-12 flex items-center justify-center mb-4">
                <Database className="h-6 w-6 text-[#004235]" />
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">E-Stream (for Suppliers)</h3>
              <p className="text-gray-600">
                Enabling sellers to list products for auction, optimize inventory, and reach a wider market.
              </p>
            </CardContent>
          </Card>

          <Card className="border-2 border-[#F2F2F2] shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="rounded-full bg-[#004235]/10 w-12 h-12 flex items-center justify-center mb-4">
                <BarChart3 className="h-6 w-6 text-[#004235]" />
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">MyStreamLnk (for Buyers)</h3>
              <p className="text-gray-600">
                Providing buyers direct access to bid on auction listings and secure competitive deals.
              </p>
            </CardContent>
          </Card>

          <Card className="border-2 border-[#F2F2F2] shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="rounded-full bg-[#004235]/10 w-12 h-12 flex items-center justify-center mb-4">
                <Clock className="h-6 w-6 text-[#004235]" />
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">MyStreamLnk+ (for Agents)</h3>
              <p className="text-gray-600">
                Allowing agents to guide their client portfolios to participate in strategic auction campaigns.
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="mt-12 max-w-3xl mx-auto text-center">
          <p className="text-gray-700">
            Our intelligent auction system moves beyond traditional RFQs to help dynamically balance supply and demand.
            It facilitates the efficient movement of inventory, reduces idle or surplus stock, and empowers smarter,
            more competitive purchasing decisions for all participants.
          </p>
        </div>
      </div>
    </section>
  )
}
