"use client"

import { useState, useEffect, useCallback } from "react"
import { feature } from "topojson-client"
import type { WorldData } from "@/types/map-types"

export function useMapData() {
  const [worldData, setWorldData] = useState<WorldData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const loadWorldData = useCallback(async () => {
    try {
      setIsLoading(true)

      const dataSources = [
        "https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson",
        "https://cdn.jsdelivr.net/npm/world-atlas@1/world/110m.json",
        "https://unpkg.com/world-atlas@1/world/110m.json",
      ]

      let worldGeoData = null

      for (const source of dataSources) {
        try {
          const response = await fetch(source)
          if (!response.ok) continue

          const contentType = response.headers.get("content-type")

          if (source.includes("geojson") || contentType?.includes("application/json")) {
            const geoJsonData = await response.json()
            worldGeoData = { countries: geoJsonData }
            break
          } else {
            const topology = await response.json()
            if (topology.objects && topology.objects.countries) {
              const countries = feature(topology, topology.objects.countries) as any
              worldGeoData = { countries }
              break
            } else if (topology.objects && topology.objects.land) {
              const countries = feature(topology, topology.objects.land) as any
              worldGeoData = { countries }
              break
            }
          }
        } catch (sourceError) {
          continue
        }
      }

      if (!worldGeoData) {
        worldGeoData = createFallbackData()
      }

      setWorldData(worldGeoData)
    } catch (error) {
      console.error("Error loading world data:", error)
      setWorldData(createFallbackData())
    } finally {
      setIsLoading(false)
    }
  }, [])

  const createFallbackData = (): WorldData => ({
    countries: {
      type: "FeatureCollection",
      features: [
        {
          type: "Feature",
          properties: { NAME: "World", ISO_A3: "WLD" },
          geometry: {
            type: "Polygon",
            coordinates: [
              [
                [-180, -90],
                [180, -90],
                [180, 90],
                [-180, 90],
                [-180, -90],
              ],
            ],
          },
        },
      ],
    },
  })

  useEffect(() => {
    loadWorldData()
  }, [loadWorldData])

  return {
    worldData,
    isLoading,
    reloadData: loadWorldData,
  }
}
