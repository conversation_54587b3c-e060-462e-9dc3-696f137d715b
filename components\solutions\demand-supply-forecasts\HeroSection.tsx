"use client";

import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Navigate Market Volatility: AI-Powered Demand & Supply Forecasting
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Move beyond reactive decision-making. StreamLnk's intelligent forecasting engine, fueled by real-time ecosystem data from StreamResources+, helps you anticipate shifts in demand and supply for key industrial materials, enabling proactive strategy and optimized operations.
            </p>
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                UNLOCK PREDICTIVE INSIGHTS – REQUEST A DEMO
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/solutions/demand-supply-forecasts/hero-placeholder.svg" // Placeholder image path
              alt="Demand & Supply Forecasting"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}