import type { Metadata } from "next"
import { HeroSec<PERSON> } from "@/components/partners/stream-freight/hero-section"
import { BenefitsSection } from "@/components/partners/stream-freight/benefits-section"
import { PartnersSection } from "@/components/partners/stream-freight/partners-section"
import { FeaturesSection } from "@/components/partners/stream-freight/features-section"
import { HowItWorksSection } from "@/components/partners/stream-freight/how-it-works-section"
import { ComplianceSection } from "@/components/partners/stream-freight/compliance-section"
import { ToolsSection } from "@/components/partners/stream-freight/tools-section"
import { PaymentModelsSection } from "@/components/partners/stream-freight/payment-models-section"
import { ValuePropositionSection } from "@/components/partners/stream-freight/value-proposition-section"
import { CtaSection } from "@/components/partners/stream-freight/cta-section"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"

export const metadata: Metadata = {
  title: "Join the StreamFreight Logistics Network | StreamLnk",
  description:
    "Connect your trucking fleet, rail services, or dispatch operations to StreamLnk's dynamic ecosystem. Bid on jobs, manage logistics, and track your earnings—all in one integrated portal.",
}

export default function StreamFreightPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <MainNav />
      <main>
      <HeroSection />
      <BenefitsSection />
      <PartnersSection />
      <FeaturesSection />
      <HowItWorksSection />
      <ComplianceSection />
      <ToolsSection />
      <PaymentModelsSection />
      <ValuePropositionSection />
      <CtaSection />
      </main>
      <MainFooter />
    </div>
  )
}
