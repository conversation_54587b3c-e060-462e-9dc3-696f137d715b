import Image from "next/image";
import { ArrowR<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
          <div className="flex flex-col justify-center space-y-4">
            <div className="inline-block rounded-lg bg-[#004235]/10 px-3 py-1 text-sm text-[#004235]">
              Global Payment Solutions
            </div>
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
              Wire & Multi-Currency Payment Support
            </h1>
            <p className="text-gray-600 md:text-xl">
              Transact securely across borders with robust wire transfer and multi-currency payment capabilities
              designed for international scale.
            </p>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button className="bg-[#004235] hover:bg-[#004235]/90">
                Get Started <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button variant="outline" className="border-[#028475] text-[#028475]">
                Learn More
              </Button>
            </div>
          </div>
          <div className="flex justify-center">
            <div className="relative w-full max-w-[500px] h-[300px] md:h-[400px]">
              <Image
                src="/images/finance-payments/wire-payment/Wire & Multi-Currency Payment Support.png"
                alt="Global payment illustration"
                fill
                className="object-contain"
                priority
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}