"use client"

import { useEffect } from "react"
import * as d3 from "d3"
import type { RealTimeData } from "@/types/map-types"

interface EventsLayerProps {
  svg: d3.Selection<SVGSVGElement, unknown, null, undefined>
  showEvents: boolean
  realTimeData: RealTimeData
  projection: d3.GeoProjection
}

export function useEventsLayer({
  svg,
  showEvents,
  realTimeData,
  projection,
}: EventsLayerProps) {
  useEffect(() => {
    if (!svg || !showEvents) return

    const eventGroup = svg.append("g").attr("class", "events")

    realTimeData.events.forEach((event) => {
      const [x, y] = projection(event.location) || [0, 0]

      const getEventColor = (severity: string) => {
        switch (severity) {
          case "critical":
            return "#ef4444"
          case "high":
            return "#f59e0b"
          case "medium":
            return "#3b82f6"
          case "low":
            return "#10b981"
          default:
            return "#52AAA3"
        }
      }

      const eventColor = getEventColor(event.severity)

      const eventMarker = eventGroup
        .append("g")
        .attr("class", `event-marker event-${event.id}`)
        .attr("transform", `translate(${x}, ${y})`)
        .style("cursor", "pointer")

      // Pulsing circle for alerts
      if (event.severity === "critical" || event.severity === "high") {
        eventMarker
          .append("circle")
          .attr("r", 8)
          .style("fill", "none")
          .style("stroke", eventColor)
          .style("stroke-width", "2px")
          .style("opacity", 0.8)
          .append("animate")
          .attr("attributeName", "r")
          .attr("values", "8;15;8")
          .attr("dur", "2s")
          .attr("repeatCount", "indefinite")
      }

      // Main event circle
      eventMarker
        .append("circle")
        .attr("r", 4)
        .style("fill", eventColor)
        .style("stroke", "#fff")
        .style("stroke-width", "1px")

      // Auto-hide low severity events
      if (event.autoHide) {
        eventMarker.transition().delay(5000).duration(1000).style("opacity", 0).remove()
      }
    })
  }, [svg, showEvents, realTimeData, projection])
}
