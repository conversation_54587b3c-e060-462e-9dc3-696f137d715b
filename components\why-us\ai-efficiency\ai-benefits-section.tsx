import { Users, TrendingUp, <PERSON>, <PERSON><PERSON>heck, FileText, <PERSON><PERSON><PERSON><PERSON>ig, Z<PERSON>, Brain } from "lucide-react";

interface BenefitCardProps {
  icon: React.ReactNode;
  title: string;
  howItWorks: string;
  efficiencyGain: string;
}

const BenefitCard: React.FC<BenefitCardProps> = ({ icon, title, howItWorks, efficiencyGain }) => (
  <div className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
    <div className="flex items-center mb-4">
      <div className="rounded-full bg-[#028475]/10 w-12 h-12 flex items-center justify-center mr-4">
        {icon}
      </div>
      <h3 className="text-xl font-semibold text-[#004235]">{title}</h3>
    </div>
    <p className="text-sm text-gray-600 mb-2"><strong className="text-gray-700">How it works:</strong> {howItWorks}</p>
    <p className="text-sm text-green-700"><strong className="text-green-600">Efficiency Gain:</strong> {efficiencyGain}</p>
  </div>
);

export default function AiBenefitsSection() {
  const aiApplications = [
    {
      icon: <Users className="h-6 w-6 text-[#028475]" />,
      title: "AI-Powered Product & Partner Matching",
      howItWorks: "Our algorithms analyze detailed product specifications, buyer requirements, supplier capabilities, certifications, iScore™ ratings, and logistical parameters to suggest the most optimal matches.",
      efficiencyGain: "Drastically reduces time spent on manual sourcing and vetting, ensuring higher quality connections faster."
    },
    {
      icon: <TrendingUp className="h-6 w-6 text-[#028475]" />,
      title: "StreamIndex™ AI-Assisted Pricing & Quoting",
      howItWorks: "AI continuously analyzes market data to power StreamIndex™ benchmarks, providing real-time pricing guidance to suppliers and instant, data-driven landed cost quotes to buyers.",
      efficiencyGain: "Accelerates the quoting process from days/weeks to minutes, enables more competitive pricing, and improves price transparency."
    },
    {
      icon: <Route className="h-6 w-6 text-[#028475]" />,
      title: "Predictive Logistics & Route Optimization",
      howItWorks: "ML models analyze historical transit times, carrier performance, port congestion, and other variables to predict ETAs, identify optimal shipping routes, and suggest the most reliable carriers for each leg of the journey.",
      efficiencyGain: "Minimizes delays, reduces freight costs, and improves on-time delivery performance."
    },
    {
      icon: <ShieldCheck className="h-6 w-6 text-[#028475]" />,
      title: "iScore™ AI-Driven Risk Assessment",
      howItWorks: "AI algorithms calculate dynamic iScore™ ratings for all platform participants by processing operational, compliance, and financial data, predicting potential counterparty risks.",
      efficiencyGain: "Automates a significant portion of due diligence, allowing businesses to make faster, more informed decisions about who to trade with."
    },
    {
      icon: <FileText className="h-6 w-6 text-[#028475]" />,
      title: "Automated Workflow & Document Processing",
      howItWorks: "AI assists in automating document verification (e.g., checking for completeness, matching data across forms), triggers rule-based workflows (e.g., AR reminders, compliance alerts), and flags exceptions for human review.",
      efficiencyGain: "Reduces manual administrative work, minimizes errors, and speeds up processing times for critical tasks."
    },
    {
      icon: <BarChartBig className="h-6 w-6 text-[#028475]" />,
      title: "Demand & Supply Forecasting (StreamResources+)",
      howItWorks: "AI analyzes historical trends, current market signals (RFQs, auctions), and external factors to forecast demand for specific materials and predict potential supply chain imbalances.",
      efficiencyGain: "Enables proactive inventory management, optimized production planning, and strategic sourcing/selling decisions."
    },
    {
      icon: <Zap className="h-6 w-6 text-[#028475]" />,
      title: "Intelligent Auction Engine",
      howItWorks: "AI suggests optimal timing for auctions, recommends reserve prices, and can even facilitate automated bidding within predefined parameters to maximize outcomes for both buyers and suppliers.",
      efficiencyGain: "Makes inventory liquidation and opportunistic sourcing faster and more effective."
    }
  ];

  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            How StreamLnk's AI Delivers Tangible Efficiency Gains
          </h2>
          <p className="text-lg text-gray-700 mb-2">
            StreamLnk's AI Engine: Powering a New Era of Efficiency
          </p>
          <p className="text-gray-600">
            Artificial Intelligence is not just a feature at StreamLnk; it's the foundational engine that drives efficiency, intelligence, and optimization across our entire integrated ecosystem. Here's how:
          </p>
        </div>
        <div className="max-w-5xl mx-auto mb-12">
          <h3 className="text-2xl font-semibold text-[#004235] mb-8 text-center md:text-left">Key AI Applications & Benefits:</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8">
            {aiApplications.map((app, index) => (
              <BenefitCard 
                key={index} 
                icon={app.icon} 
                title={app.title} 
                howItWorks={app.howItWorks} 
                efficiencyGain={app.efficiencyGain} 
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}