"use client";

import { <PERSON>Circle, Star, ShieldCheck, Lightbulb, Handshake, MapPin } from 'lucide-react';

const qualities = [
  {
    text: 'Commitment to Quality & Reliability',
    icon: <Star className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />
  },
  {
    text: 'Focus on Customer/User Satisfaction',
    icon: <CheckCircle className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />
  },
  {
    text: 'Adherence to Compliance & Ethical Standards',
    icon: <ShieldCheck className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />
  },
  {
    text: 'Willingness to Embrace Digital Tools & Innovation',
    icon: <Lightbulb className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />
  },
  {
    text: 'Collaborative Spirit & Shared Vision for a Better Supply Chain',
    icon: <Handshake className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />
  },
  {
    text: 'Relevant Industry Expertise and/or Regional Presence',
    icon: <MapPin className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />
  }
];

export default function PartnerQualitiesSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6 text-center">
            Qualities We Value in Our Partners
          </h2>
          <p className="text-xl text-[#028475] font-semibold mb-10 text-center">
            What Makes a Successful StreamLnk Partner?
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
            {qualities.map((quality, index) => (
              <div key={index} className="flex items-start p-4 bg-slate-50 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
                {quality.icon}
                <p className="text-lg text-gray-700 leading-relaxed">{quality.text}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}