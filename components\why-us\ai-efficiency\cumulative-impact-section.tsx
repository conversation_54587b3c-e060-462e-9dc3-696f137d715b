import { Tren<PERSON><PERSON><PERSON>, Z<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>ck, CheckCircle } from "lucide-react";

export default function CumulativeImpactSection() {
  const impacts = [
    {
      icon: <TrendingUp className="h-7 w-7 text-[#028475]" />,
      title: "Reduced Operational Costs",
      description: "Through optimized logistics, fewer errors, and less manual intervention."
    },
    {
      icon: <Zap className="h-7 w-7 text-[#028475]" />,
      title: "Accelerated Transaction Cycles",
      description: "From faster quoting to quicker fulfillment and payment."
    },
    {
      icon: <Users className="h-7 w-7 text-[#028475]" />,
      title: "Improved Resource Allocation",
      description: "Freeing up your team to focus on strategic activities rather than repetitive tasks."
    },
    {
      icon: <ShieldCheck className="h-7 w-7 text-[#028475]" />,
      title: "Enhanced Agility & Resilience",
      description: "Ability to respond more quickly and effectively to market changes and potential disruptions."
    },
    {
      icon: <CheckCircle className="h-7 w-7 text-[#028475]" />,
      title: "Data-Backed Confidence",
      description: "Making critical business decisions based on intelligent insights rather than guesswork."
    }
  ];

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Smarter Decisions at Every Step, Compounding Benefits Across Your Business
          </h2>
          <p className="text-lg text-gray-700">
            The Cumulative Impact of AI-Driven Efficiency
          </p>
        </div>
        <div className="max-w-4xl mx-auto">
          <p className="text-lg text-gray-700 mb-10 text-center">
            The power of StreamLnk's AI is not in isolated features, but in how these intelligent capabilities work together across our integrated ecosystem. This holistic approach leads to:
          </p>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {impacts.map((item, index) => (
              <div key={index} className="flex flex-col items-center text-center p-6 bg-gray-50 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <div className="rounded-full bg-[#028475]/10 w-16 h-16 flex items-center justify-center mb-4">
                  {item.icon}
                </div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">{item.title}</h3>
                <p className="text-gray-600 text-sm">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}