"use client";

import { <PERSON><PERSON>ircle, <PERSON>, Target, Zap, <PERSON>hake, User<PERSON>he<PERSON> } from 'lucide-react'; // Example icons

const partnerQualities = [
  {
    icon: <Handshake className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />,
    title: "Shared Vision",
    description: "A mutual belief in the power of digitization and collaboration to transform industrial trade."
  },
  {
    icon: <Zap className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />,
    title: "Complementary Strengths",
    description: "Assets, expertise, market access, or technology that enhances StreamLnk's offerings and reach."
  },
  {
    icon: <UserCheck className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />,
    title: "Strong Market Presence & Reputation",
    description: "Established credibility and trust within their respective industries or regions."
  },
  {
    icon: <Target className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />,
    title: "Commitment to Innovation",
    description: "A forward-looking approach and a willingness to invest in co-creating new solutions."
  },
  {
    icon: <Users className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />,
    title: "Long-Term Strategic Alignment",
    description: "A desire to build a lasting partnership rather than a short-term tactical engagement."
  },
  {
    icon: <CheckCircle className="h-7 w-7 text-[#028475] mr-3 flex-shrink-0" />,
    title: "Executive-Level Commitment",
    description: "Strong sponsorship and engagement from senior leadership."
  }
];

export default function PartnerQualitiesSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-8 text-center">
            Building Alliances for Long-Term, Transformative Impact
          </h2>
          <p className="text-xl text-[#028475] font-semibold mb-10 text-center">
            What We Seek in Strategic Alliance Partners
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {partnerQualities.map((quality) => (
              <div key={quality.title} className="bg-[#f3f4f6] p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                <div className="flex items-start mb-3">
                  {quality.icon}
                  <h3 className="text-xl font-semibold text-[#004235]">{quality.title}</h3>
                </div>
                <p className="text-gray-600 text-sm leading-relaxed">{quality.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}