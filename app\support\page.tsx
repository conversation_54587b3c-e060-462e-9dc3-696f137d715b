import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import { Separator } from '@/components/ui/separator';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from 'next/link';
import { Search, HelpCircle, Wrench, Rocket, DollarSign, Shield, Mail, Phone, MessageSquare, ExternalLink, ChevronRight, Info } from 'lucide-react';

const SupportPage = () => {
  const supportTopics = [
    {
      icon: <HelpCircle className="w-10 h-10 text-[#028475] mb-4" />,
      title: "Portal-Specific Support",
      description: "Get help tailored to the StreamLnk portal you're using (MyStreamLnk, E-Stream, StreamFreight, etc.). Access user guides, FAQs, and specific troubleshooting.",
      buttonText: "Select Your Portal",
      buttonLink: "/portals", // Placeholder link
    },
    {
      icon: <HelpCircle className="w-10 h-10 text-[#028475] mb-4" />,
      title: "Frequently Asked Questions (FAQs)",
      description: "Find quick answers to common questions about account setup, billing, shipment tracking, compliance, and platform features.",
      buttonText: "Browse All FAQs",
      buttonLink: "/resources/faq", // Placeholder link
    },
    {
      icon: <Wrench className="w-10 h-10 text-[#028475] mb-4" />,
      title: "Troubleshooting & Technical Issues",
      description: "Guides for resolving common technical issues, error messages, or integration problems.",
      buttonText: "Go To Troubleshooting",
      buttonLink: "/portal-support/troubleshooting", // Placeholder link
    },
    {
      icon: <Rocket className="w-10 h-10 text-[#028475] mb-4" />,
      title: "Onboarding & Getting Started Guides",
      description: "Resources for new users, including step-by-step onboarding guides, platform tours, and best practices.",
      buttonText: "Access Onboarding Resources",
      buttonLink: "/get-started", // Placeholder link
    },
    {
      icon: <DollarSign className="w-10 h-10 text-[#028475] mb-4" />,
      title: "Billing & Payment Inquiries",
      description: "Help with understanding invoices, payment statuses, fee structures, and resolving billing discrepancies.",
      buttonText: "Billing Support",
      buttonLink: "/finance-payments/support", // Placeholder link
    },
    {
      icon: <Shield className="w-10 h-10 text-[#028475] mb-4" />,
      title: "Compliance & Document Management",
      description: "Assistance with uploading compliance documents, understanding regulatory requirements, and managing document expiry.",
      buttonText: "Compliance Assistance",
      buttonLink: "/legal/compliance", // Placeholder link
    },
  ];

  const topResources = [
    { title: "How to Track Your International Shipment in MyStreamLnk", link: "/resources/guides/tracking-shipment" },
    { title: "Troubleshooting Common E-Stream Product Listing Errors", link: "/resources/guides/estream-listing-errors" },
    { title: "Understanding Your First StreamFreight Payout Statement", link: "/resources/guides/streamfreight-payout" },
  ];

  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="bg-[#F2F2F2] py-16 md:py-24">
          <div className="container mx-auto px-4 md:px-6 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-4">
              StreamLnk Support Hub
            </h1>
            <p className="text-lg md:text-xl text-gray-700 mb-8 max-w-3xl mx-auto">
              Your Partner in Seamless Global Trade. Welcome to the StreamLnk Support Center. Find answers to your questions, troubleshoot issues, and get expert assistance to ensure you're getting the most out of our integrated platform.
            </p>
            <div className="max-w-xl mx-auto flex flex-col sm:flex-row gap-4">
              <Input
                type="search"
                placeholder="How can we assist you? Search FAQs, Guides, Troubleshooting..."
                className="flex-grow text-base py-3 px-4"
              />
              <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white text-base">
                <Search className="mr-2 h-5 w-5" /> Search Support
              </Button>
            </div>
          </div>
        </section>

        {/* How Can We Help You Today? Section */}
        <section className="py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] text-center mb-4">
              How Can We Help You Today?
            </h2>
            <p className="text-lg text-gray-600 text-center mb-12 max-w-2xl mx-auto">
              Find Support by Topic or Portal. We offer multiple ways to get the help you need quickly and efficiently.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {supportTopics.map((topic, index) => (
                <Card key={index} className="flex flex-col bg-gray-50 hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="items-center text-center">
                    {topic.icon}
                    <CardTitle className="text-[#004235]">{topic.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="flex-grow text-center">
                    <CardDescription className="text-gray-600 mb-6">
                      {topic.description}
                    </CardDescription>
                  </CardContent>
                  <div className="p-6 pt-0 text-center">
                    <Link href={topic.buttonLink} passHref>
                      <Button className="w-full bg-[#004235] hover:bg-[#028475] text-white">
                        {topic.buttonText} <ChevronRight className="ml-2 h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Top Support Resources Section */}
        <section className="bg-[#F2F2F2] py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] text-center mb-4">
              Top Support Resources
            </h2>
            <p className="text-lg text-gray-600 text-center mb-10">
              Popular Support Articles & Guides
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-10">
              {topResources.map((resource, index) => (
                <Card key={index} className="bg-white hover:shadow-md transition-shadow duration-300">
                  <CardContent className="p-6">
                    <Link href={resource.link} className="text-[#028475] hover:text-[#004235] hover:underline font-semibold text-lg">
                      {resource.title}
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="text-center">
              <Link href="/resources/guides" passHref> {/* Placeholder link */}
                <Button variant="link" className="text-[#028475] hover:text-[#004235] text-lg">
                  View All Support Guides <ChevronRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* Need to Speak with Someone? Section */}
        <section className="py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] text-center mb-4">
              Need to Speak with Someone?
            </h2>
            <p className="text-lg text-gray-600 text-center mb-12">
              We're Ready to Assist. Contact Our Support Teams.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-start">
              <Card className="bg-gray-50">
                <CardHeader>
                  <CardTitle className="text-[#004235] flex items-center">
                    <MessageSquare className="mr-3 h-6 w-6 text-[#028475]" /> Submit a Support Ticket
                  </CardTitle>
                  <CardDescription className="text-gray-600">
                    Recommended for Detailed Issues. Log your issue with all relevant details, and our team will investigate and respond. Track your ticket status online.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full bg-[#004235] hover:bg-[#028475] text-white mb-2">
                    Create New Support Ticket
                  </Button>
                  <p className="text-sm text-gray-500 text-center">
                    Targeted response within 4-8 business hours for standard issues, faster for critical.
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-gray-50">
                <CardHeader>
                  <CardTitle className="text-[#004235] flex items-center">
                    <Mail className="mr-3 h-6 w-6 text-[#028475]" /> Email Support
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <p className="text-gray-700">
                    <strong>General & Portal Support:</strong> <a href="mailto:<EMAIL>" className="text-[#028475] hover:text-[#004235] hover:underline"><EMAIL></a>
                  </p>
                  <p className="text-gray-700">
                    <strong>Technical & API Support:</strong> <a href="mailto:<EMAIL>" className="text-[#028475] hover:text-[#004235] hover:underline"><EMAIL></a>
                  </p>
                  <p className="text-gray-700">
                    <strong>Billing & Finance Support:</strong> <a href="mailto:<EMAIL>" className="text-[#028475] hover:text-[#004235] hover:underline"><EMAIL></a>
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-gray-50 md:col-span-2">
                <CardHeader>
                  <CardTitle className="text-[#004235] flex items-center">
                    <Info className="mr-3 h-6 w-6 text-[#028475]" /> Live Chat & Phone Support
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-lg text-[#004235] mb-2">Live Chat</h4>
                    <p className="text-gray-600 mb-3">Connect with a live support agent in real-time for immediate help with straightforward questions or urgent minor issues.</p>
                    <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto">
                      Start Live Chat
                    </Button>
                    <p className="text-sm text-gray-500 mt-2">Available Mon-Fri, 9 AM - 6 PM GMT</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-lg text-[#004235] mb-2">Phone Support</h4>
                    <p className="text-gray-600 mb-3">Direct phone lines for specific support needs (Premium/Tiered Users or Urgent Issues).</p>
                    {/* Placeholder for phone numbers */}
                    <p className="text-gray-700">
                      <strong>Sales & General Inquiries:</strong> +1-XXX-XXX-XXXX (9 AM - 5 PM EST)
                    </p>
                    <p className="text-gray-700">
                      <strong>Premium Support Line:</strong> +1-YYY-YYY-YYYY (24/7 for eligible users)
                    </p>
                    <p className="text-sm text-gray-500 mt-2">Please have your account ID ready.</p>
                  </div>
                </CardContent>
              </Card>
            </div>
            <div className="text-center mt-12">
              <Link href="/support/feedback" passHref> {/* Placeholder link */}
                <Button variant="link" className="text-[#028475] hover:text-[#004235]">
                  Rate Your Recent Support Experience
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* Platform Status Section */}
        <section className="py-16 bg-[#F2F2F2]">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
                Platform Status & Maintenance
              </h2>
              <p className="text-xl text-gray-700 mb-8">
                Check for real-time updates on StreamLnk portals' operational status and planned maintenance windows.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
                  size="lg"
                  asChild
                >
                  <Link href="/status"> {/* Placeholder link */}
                    VIEW STATUS
                    <ExternalLink className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

      </main>
      <BottomFooter />
    </div>
  );
};

export default SupportPage;