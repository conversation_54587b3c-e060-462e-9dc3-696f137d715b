import { CheckCircle } from "lucide-react";

export default function BenefitsSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Unlock Synergies, Automate Workflows, Enhance Data Accuracy
          </h2>
          <div className="w-20 h-1 bg-[#028475] mx-auto mb-12"></div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="flex items-start">
              <CheckCircle className="h-6 w-6 text-[#028475] mt-1 mr-4 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-[#004235] mb-2">Eliminate Manual Data Entry</h4>
                <p className="text-gray-700">Reduce errors and free up valuable staff time.</p>
              </div>
            </div>

            <div className="flex items-start">
              <CheckCircle className="h-6 w-6 text-[#028475] mt-1 mr-4 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-[#004235] mb-2">Single Source of Truth</h4>
                <p className="text-gray-700">Ensure data consistency across all your business applications.</p>
              </div>
            </div>

            <div className="flex items-start">
              <CheckCircle className="h-6 w-6 text-[#028475] mt-1 mr-4 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-[#004235] mb-2">Improved Operational Efficiency</h4>
                <p className="text-gray-700">Streamline end-to-end processes from quote-to-cash and procure-to-pay.</p>
              </div>
            </div>

            <div className="flex items-start">
              <CheckCircle className="h-6 w-6 text-[#028475] mt-1 mr-4 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-[#004235] mb-2">Enhanced Visibility & Control</h4>
                <p className="text-gray-700">Gain a holistic view of your trade operations integrated with your broader enterprise data.</p>
              </div>
            </div>

            <div className="flex items-start">
              <CheckCircle className="h-6 w-6 text-[#028475] mt-1 mr-4 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-[#004235] mb-2">Faster Decision-Making</h4>
                <p className="text-gray-700">Access real-time, accurate data from StreamLnk directly within your familiar systems.</p>
              </div>
            </div>

            <div className="flex items-start">
              <CheckCircle className="h-6 w-6 text-[#028475] mt-1 mr-4 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-[#004235] mb-2">Scalable Operations</h4>
                <p className="text-gray-700">Build a future-proof tech stack that can grow with your business.</p>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <h3 className="text-2xl font-bold text-[#004235] mb-4">
              Connect Your Business for a Smarter Supply Chain.
            </h3>
          </div>
        </div>
      </div>
    </section>
  );
}