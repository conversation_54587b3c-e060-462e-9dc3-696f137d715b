"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export default function PressCenterLinkSection() {
  return (
    <section className="py-16 bg-white"> {/* Adjusted padding and removed border */}
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center"> {/* Removed bg, padding, rounded, shadow */}
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6"> {/* Matched reference H2 style */}
            Visit Our Press Center
          </h2>
          <p className="text-xl text-gray-700 mb-8"> {/* Matched reference P style */}
            For media inquiries, brand assets, and detailed corporate information, please visit our comprehensive Press Center.
          </p>
          <Button
            variant="outline" // Standardized to outline variant
            className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto" // Matched reference outline button classes
            size="lg" // Ensured size is lg
            asChild
          >
            <Link href="/press-center"> {/* Assuming /press-center is the correct link */}
              VISIT PRESS CENTER {/* Shortened button text */}
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}