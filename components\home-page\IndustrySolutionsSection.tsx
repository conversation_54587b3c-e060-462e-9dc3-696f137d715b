"use client"

import { useState, useEffect, useRef } from "react"
import { Button } from "@/components/ui/button"
import { Plus } from "lucide-react"
import { motion, useAnimation, useInView } from "framer-motion"

export function IndustrySolutionsSection() {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({})
  const statsRef = useRef(null)
  const isInView = useInView(statsRef, { once: false, amount: 0.3 })
  const controls = useAnimation()

  useEffect(() => {
    if (isInView) {
      controls.start("visible")
    }
  }, [controls, isInView])

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  const stats = [
    { number: "50+", label: "Countries Connected" },
    { number: "1000+", label: "Verified Energy Suppliers" },
    { number: "24/7", label: "Live Market Data" },
    { number: "99.9%", label: "Platform Uptime" },
  ]

  const solutions = [
    {
      id: "supplier-verification",
      title: "Supplier Verification & Onboarding",
      content:
        "Comprehensive verification system for energy suppliers worldwide. Our platform validates business credentials, financial stability, and operational capacity to ensure you're working with trusted partners in the global energy market.",
    },
    {
      id: "real-time-pricing",
      title: "Real-Time Pricing & Market Analytics",
      content:
        "Access live market data, price trends, and predictive analytics to make informed trading decisions. Our AI-powered system analyzes global market conditions, supply-demand dynamics, and geopolitical factors affecting energy prices.",
    },
    {
      id: "smart-contracts",
      title: "Smart Contracts & Automated Trading",
      content:
        "Streamline your trading operations with automated smart contracts that execute based on predefined conditions. Reduce settlement times, minimize counterparty risk, and ensure transparent, secure transactions across all energy commodities.",
    },
    {
      id: "logistics-optimization",
      title: "Logistics & Supply Chain Optimization",
      content:
        "Optimize your supply chain with AI-driven logistics planning, route optimization, and real-time shipment tracking. Our platform integrates with global shipping networks to ensure efficient, cost-effective delivery of energy products.",
    },
    {
      id: "risk-management",
      title: "Advanced Risk Management & Compliance",
      content:
        "Comprehensive risk assessment tools including credit scoring, market volatility analysis, and regulatory compliance monitoring. Stay ahead of market risks while ensuring full compliance with international energy trading regulations.",
    },
  ]

  return (
    <div className="bg-white mb-16 md:mb-24">
      {/* Stats Section */}
      <div className="py-24 md:py-32 px-6" ref={statsRef}>
        <div className="max-w-8xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-16 md:gap-20">
            {stats.map((stat, index) => (
              <div key={index} className="flex flex-col items-center">
                <div className="relative w-64 h-64 md:w-72 md:h-72 lg:w-80 lg:h-80">
                  {/* Background circle */}
                  <div className="absolute inset-0 rounded-full border-4 border-[#18b793] border-opacity-15"></div>

                  {/* Radial fill animation using conic gradient */}
                  <motion.div
                    className="absolute inset-0 rounded-full"
                    style={{
                      background: `conic-gradient(from 0deg, #18b793 0deg, #18b793 0deg, transparent 0deg)`,
                      mask: `radial-gradient(circle, transparent 70%, black 72%, black 100%)`,
                      WebkitMask: `radial-gradient(circle, transparent 70%, black 72%, black 100%)`,
                    }}
                    initial={{ background: `conic-gradient(from 0deg, #18b793 0deg, #18b793 0deg, transparent 0deg)` }}
                    animate={controls}
                    variants={{
                      visible: {
                        background: `conic-gradient(from 0deg, #18b793 360deg, #18b793 360deg, transparent 360deg)`,
                      },
                      hidden: {
                        background: `conic-gradient(from 0deg, #18b793 0deg, #18b793 0deg, transparent 0deg)`,
                      },
                    }}
                    transition={{
                      duration: 2.5,
                      ease: "easeInOut",
                      delay: index * 0.4
                    }}
                  />

                  {/* Animated stroke circle */}
                  <svg className="absolute inset-0 w-full h-full -rotate-90">
                    <motion.circle
                      cx="50%"
                      cy="50%"
                      r="46%"
                      fill="none"
                      stroke="#18b793"
                      strokeWidth="6"
                      strokeLinecap="round"
                      strokeDasharray="100%"
                      initial={{ strokeDashoffset: "100%" }}
                      animate={controls}
                      variants={{
                        visible: { strokeDashoffset: "0%" },
                        hidden: { strokeDashoffset: "100%" },
                      }}
                      transition={{
                        duration: 2.5,
                        ease: "easeInOut",
                        delay: index * 0.4
                      }}
                      style={{
                        filter: "drop-shadow(0 0 12px rgba(24, 183, 147, 0.6))"
                      }}
                    />
                  </svg>

                  {/* Rotating accent rings */}
                  <motion.div
                    className="absolute inset-4 rounded-full border-2 border-[#18b793] border-opacity-30"
                    animate={{ rotate: 360 }}
                    transition={{
                      duration: 20,
                      repeat: Number.POSITIVE_INFINITY,
                      ease: "linear"
                    }}
                  />
                  <motion.div
                    className="absolute inset-8 rounded-full border border-[#18b793] border-opacity-20"
                    animate={{ rotate: -360 }}
                    transition={{
                      duration: 25,
                      repeat: Number.POSITIVE_INFINITY,
                      ease: "linear"
                    }}
                  />

                  {/* Inner radial fill */}
                  <motion.div
                    className="absolute inset-6 rounded-full bg-[#18b793] shadow-2xl"
                    initial={{ scale: 0, opacity: 0 }}
                    animate={controls}
                    variants={{
                      visible: { scale: 1, opacity: 1 },
                      hidden: { scale: 0, opacity: 0 },
                    }}
                    transition={{
                      duration: 0.8,
                      ease: "backOut",
                      delay: index * 0.4 + 1.5
                    }}
                    style={{
                      background: `radial-gradient(circle, #18b793 0%, #16a085 50%, #18b793 100%)`,
                      boxShadow: `
                        0 0 30px rgba(24, 183, 147, 0.4),
                        inset 0 0 20px rgba(255, 255, 255, 0.2)
                      `
                    }}
                  />

                  {/* Content inside circle */}
                  <div className="absolute inset-6 flex flex-col items-center justify-center rounded-full z-10">
                    <motion.div
                      className="text-4xl md:text-5xl lg:text-6xl font-bold text-white"
                      initial={{ scale: 0, opacity: 0, rotateY: 180 }}
                      animate={controls}
                      variants={{
                        visible: { scale: 1, opacity: 1, rotateY: 0 },
                        hidden: { scale: 0, opacity: 0, rotateY: 180 },
                      }}
                      transition={{
                        duration: 0.8,
                        ease: "backOut",
                        delay: index * 0.4 + 2
                      }}
                    >
                      {stat.number}
                    </motion.div>
                    <motion.div
                      className="text-base md:text-lg lg:text-xl text-white text-center px-4 leading-tight mt-3 font-semibold"
                      initial={{ y: 30, opacity: 0 }}
                      animate={controls}
                      variants={{
                        visible: { y: 0, opacity: 1 },
                        hidden: { y: 30, opacity: 0 },
                      }}
                      transition={{
                        duration: 0.8,
                        ease: "easeOut",
                        delay: index * 0.4 + 2.3
                      }}
                    >
                      {stat.label}
                    </motion.div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Global Energy Trading Section */}
      <div className="bg-[#172d2d] py-24 md:py-32 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-8">Global Energy Trading Network</h1>
          <p className="text-gray-300 text-lg mb-12 max-w-3xl mx-auto leading-relaxed">
            StreamLnk connects energy markets across 50+ countries with real-time data, verified suppliers, and
            secure trading infrastructure. Our platform enables seamless cross-border energy transactions with
            complete transparency and regulatory compliance.
          </p>

          {/* Divider line */}
          <div className="w-24 h-0.5 bg-[#18b793] mx-auto mb-16"></div>

          <h2 className="text-3xl font-bold text-[#18b793] mb-8">GLOBAL NETWORK MAP</h2>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-24 md:py-32 px-6 bg-gray-50">
        <div className="max-w-4xl mx-auto text-center">
          <h3 className="text-3xl font-bold text-[#004235] mb-4">Ready to Transform Your Energy Trading?</h3>
          <p className="text-gray-600 text-lg mb-8">
            Join thousands of energy professionals who trust StreamLnk for their global trading operations.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="bg-[#18b793] hover:bg-[#18b793]/90 text-white px-8 py-3 rounded-full font-semibold">
              Start Trading
            </Button>
            <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-8 py-3 rounded-full font-semibold">
              Schedule Demo
            </Button>
          </div>
        </div>
      </div>

      {/* Solutions Section */}
      <div className="py-24 md:py-32 px-6 bg-white">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] text-center mb-12">
            Advanced Energy Trading Solutions
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Platform visualization */}
            <div className="aspect-square rounded-xl overflow-hidden">
              <img
                src="/images/homepage/Supplier Verification & Onboarding.png"
                alt="Supplier Verification & Onboarding"
                className="w-full h-full object-cover"
              />
            </div>

            {/* Expandable sections */}
            <div className="space-y-6">
              {solutions.map((solution) => (
                <div key={solution.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                  <button
                    onClick={() => toggleSection(solution.id)}
                    className="flex items-center justify-between w-full text-left"
                  >
                    <h3 className="text-lg font-semibold text-[#004235] pr-4">{solution.title}</h3>
                    <Plus
                      className={`w-5 h-5 text-[#18b793] transition-transform ${
                        expandedSections[solution.id] ? "rotate-45" : ""
                      }`}
                    />
                  </button>
                  {expandedSections[solution.id] && (
                    <div className="mt-4 text-gray-600 leading-relaxed">{solution.content}</div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}