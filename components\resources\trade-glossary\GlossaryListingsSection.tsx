"use client";

import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

interface Term {
  term: string;
  definition: string;
  context?: string;
  relatedTerms?: string[];
  category?: string;
}

interface GlossaryListingsSectionProps {
  terms: Term[];
}

export default function GlossaryListingsSection({ terms }: GlossaryListingsSectionProps) {
  if (!terms || terms.length === 0) {
    return (
      <section className="py-16 md:py-20 bg-[#f3f4f6]">
        <div className="container mx-auto px-4 text-center">
          <p className="text-lg text-gray-600">No glossary terms available at the moment. Please check back later or suggest a term!</p>
        </div>
      </section>
    );
  }

  // Group terms by the first letter for alphabetical sections
  const groupedTerms: { [key: string]: Term[] } = terms.reduce((acc, term) => {
    let firstLetter = term.term[0].toUpperCase();
    if (!/[A-Z]/.test(firstLetter)) {
      firstLetter = '#'; // Group non-alphabetic terms under '#'
    }
    if (!acc[firstLetter]) {
      acc[firstLetter] = [];
    }
    acc[firstLetter].push(term);
    return acc;
  }, {} as { [key: string]: Term[] });

  const sortedLetters = Object.keys(groupedTerms).sort((a, b) => {
    if (a === '#') return 1; // '#' always at the end
    if (b === '#') return -1;
    return a.localeCompare(b);
  });

  return (
    <section className="py-16 md:py-20 bg-[#f3f4f6]" id="glossary-listings">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-10 text-center">
            Glossary Listings
          </h2>

          {sortedLetters.map((letter) => (
            <div key={letter} id={`letter-${letter}`} className="mb-12">
              <h3 className="text-5xl font-bold text-gray-200 mb-6 sticky top-0 bg-[#f3f4f6] py-2 z-10 border-b-2 border-gray-300">
                {letter}
              </h3>
              <Accordion type="single" collapsible className="w-full space-y-4">
                {groupedTerms[letter].map((item, index) => (
                  <AccordionItem key={`${letter}-${index}`} value={`item-${letter}-${index}`} className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold text-[#004235] hover:no-underline hover:bg-gray-50 rounded-t-lg">
                      {item.term}
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6 pt-2 text-gray-700">
                      <p className="mb-3 text-base">
                        {item.definition}
                      </p>
                      {item.context && (
                        <div className="mt-3 mb-3 p-3 bg-green-50 border-l-4 border-[#028475] rounded-r-md">
                          <p className="text-sm font-semibold text-[#004235] mb-1">StreamLnk Relevance:</p>
                          <p className="text-sm text-gray-600">{item.context}</p>
                        </div>
                      )}
                      {item.category && (
                        <div className="mt-3 mb-3">
                          <Badge variant="secondary" className="bg-[#028475] text-white hover:bg-[#004235]">{item.category}</Badge>
                        </div>
                      )}
                      {item.relatedTerms && item.relatedTerms.length > 0 && (
                        <div className="mt-3">
                          <p className="text-sm font-semibold text-gray-600 mb-1">Related Terms:</p>
                          <div className="flex flex-wrap gap-2">
                            {item.relatedTerms.map((related, idx) => (
                              // Assuming related terms might link to other parts of the glossary or site
                              // For now, just displaying them. Actual links would require more logic.
                              <Badge key={idx} variant="outline" className="border-[#028475] text-[#028475]">
                                {related}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}