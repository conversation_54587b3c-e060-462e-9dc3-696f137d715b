"use client"

import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Globe, Search, ArrowRight, ChevronDown, Pause } from "lucide-react"

export default function Component() {
  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <Image src="/hero-background.jpeg" alt="ExxonMobil industrial workers" fill className="object-cover" priority />
        {/* Dark overlay */}
        <div className="absolute inset-0 bg-black/40" />
      </div>

      {/* Content */}
      <div className="relative z-10 flex flex-col min-h-screen">
        {/* Navigation Header */}
        <header className="w-full">
          <nav className="flex items-center justify-between px-6 lg:px-12 py-4">
            {/* Navigation Links */}
            <div className="hidden lg:flex items-center space-x-8">
              <Link href="#" className="text-white text-sm hover:text-gray-300 transition-colors">
                Who we are
              </Link>
              <Link href="#" className="text-white text-sm hover:text-gray-300 transition-colors">
                What we do
              </Link>
              <Link href="#" className="text-white text-sm hover:text-gray-300 transition-colors">
                Sustainability
              </Link>
              <Link href="#" className="text-white text-sm hover:text-gray-300 transition-colors">
                Community
              </Link>
              <Link href="#" className="text-white text-sm hover:text-gray-300 transition-colors">
                Newsroom
              </Link>
              <Link href="#" className="text-white text-sm hover:text-gray-300 transition-colors">
                Investors
              </Link>
              <Link href="#" className="text-white text-sm hover:text-gray-300 transition-colors">
                Careers
              </Link>
            </div>

            {/* Right side - Global and Search */}
            <div className="flex items-center space-x-4 ml-auto">
              <div className="flex items-center space-x-2 text-white text-sm">
                <Globe className="w-4 h-4" />
                <span>Global</span>
              </div>
              <Button variant="ghost" size="icon" className="text-white hover:bg-white/10">
                <Search className="w-5 h-5" />
              </Button>
            </div>
          </nav>
        </header>

        {/* Hero Content */}
        <main className="flex-1 flex items-center">
          <div className="px-6 lg:px-12 w-full">
            <div className="max-w-4xl">
              {/* Investor Relations Label */}
              <div className="mb-6">
                <span className="text-white/80 text-xs font-medium tracking-widest uppercase">INVESTOR RELATIONS</span>
              </div>

              {/* Main Headline */}
              <h1 className="text-white text-4xl md:text-5xl lg:text-6xl font-light leading-tight mb-12 max-w-3xl">
                ExxonMobil announces first quarter 2025 financial results
              </h1>

              {/* Call to Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Button className="bg-white text-black hover:bg-gray-100 px-6 py-3 text-base font-medium h-auto">
                  Read the release
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>

                <Button
                  variant="outline"
                  className="border-white text-white hover:bg-white/10 hover:border-white px-6 py-3 text-base font-medium h-auto bg-transparent"
                >
                  Invest with us
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </div>
          </div>
        </main>

        {/* Bottom Content Section */}
        <footer className="w-full">
          <div className="px-6 lg:px-12 pb-8">
            {/* Content Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <div className="bg-black/20 backdrop-blur-sm p-4 border-t-2 border-green-400">
                <p className="text-white text-sm leading-relaxed">
                  ExxonMobil announces first quarter 2025 financial results
                </p>
              </div>

              <div className="bg-black/20 backdrop-blur-sm p-4">
                <p className="text-white text-sm leading-relaxed">
                  How we've harnessed the best of both in the Permian Basin
                </p>
              </div>

              <div className="bg-black/20 backdrop-blur-sm p-4">
                <p className="text-white text-sm leading-relaxed">Annual report & proxy</p>
              </div>

              <div className="bg-black/20 backdrop-blur-sm p-4">
                <p className="text-white text-sm leading-relaxed">Opening the doors of STEM education</p>
              </div>
            </div>

            {/* Bottom Controls */}
            <div className="flex items-center justify-between">
              <ChevronDown className="w-6 h-6 text-white" />

              <Button
                variant="ghost"
                size="icon"
                className="bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 rounded-full w-12 h-12"
              >
                <Pause className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </footer>
      </div>
    </div>
  )
}
