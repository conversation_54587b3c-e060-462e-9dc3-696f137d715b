import type { Metada<PERSON> } from "next";
import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";
import HeroSection from "@/components/info/hero-section";
import OverviewSection from "@/components/info/overview-section";
import PortalsSection from "@/components/info/portals-section";
import TechnologiesSection from "@/components/info/technologies-section";
import ValuePropositionSection from "@/components/info/value-proposition-section";
import CtaSection from "@/components/info/cta-section";

export const metadata: Metadata = {
  title: "About the StreamLnk Platform | StreamLnk",
  description:
    "Discover the architecture, core capabilities, and transformative potential of StreamLnk's unified B2B ecosystem.",
};

export default function InfoPage() {
  return (
    <div className="flex flex-col min-h-screen bg-white">
      <MainNav />
      <main>
        <HeroSection />
        <OverviewSection />
        <PortalsSection />
        <TechnologiesSection />
        <ValuePropositionSection />
        <CtaSection />
      </main>
      <MainFooter />
    </div>
  );
}