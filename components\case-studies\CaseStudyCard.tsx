"use client";

import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight } from "lucide-react";

export interface CaseStudy {
  slug: string; // For linking to the full case study page
  imageSrc: string;
  imageAlt: string;
  clientName: string;
  clientType?: string; // e.g., SME Manufacturer, Global Producer
  title: string;
  challenge: string;
  results: string[];
  tags: string[]; // e.g., ["Buyer", "Polymers", "Cost Savings"]
}

interface CaseStudyCardProps {
  study: CaseStudy;
}

export default function CaseStudyCard({ study }: CaseStudyCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden flex flex-col h-full hover:shadow-xl transition-shadow duration-300 border border-gray-200">
      <div className="relative w-full h-48 sm:h-56">
        <Image
          src={study.imageSrc}
          alt={study.imageAlt}
          fill
          className="object-cover"
        />
      </div>
      <div className="p-6 flex flex-col flex-grow">
        <div>
          <p className="text-sm text-[#028475] font-semibold mb-1">
            {study.clientName} {study.clientType && `(${study.clientType})`}
          </p>
          <h3 className="text-xl font-bold text-[#004235] mb-2 leading-tight">
            {study.title}
          </h3>
          <p className="text-sm text-gray-600 mb-3">
            <strong className="text-gray-800">Challenge:</strong> {study.challenge}
          </p>
          <div className="mb-4">
            <p className="text-sm text-gray-800 font-semibold mb-1">Key Results:</p>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
              {study.results.map((result, index) => (
                <li key={index}>{result}</li>
              ))}
            </ul>
          </div>
        </div>
        <div className="mt-auto">
          <div className="mb-4 flex flex-wrap gap-2">
            {study.tags.map((tag, index) => (
              <Badge key={index} variant="secondary" className="bg-[#e0e7ff] text-[#004235] hover:bg-[#c7d2fe]">
                {tag}
              </Badge>
            ))}
          </div>
          <Button
            variant="outline"
            className="w-full border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
            asChild
            size="sm"
          >
            <Link href={`/case-studies/${study.slug}`}>
              READ FULL CASE STUDY
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}