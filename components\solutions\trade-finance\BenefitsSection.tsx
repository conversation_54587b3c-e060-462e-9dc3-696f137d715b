"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { CheckCircle, ChevronRight } from "lucide-react"

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Unlock Capital, Mitigate Risk, Accelerate Growth
          </h2>

          <div className="grid md:grid-cols-3 gap-8 mb-12">
            {/* For Buyers */}
            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-[#004235] mb-4">For Buyers</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Improve working capital and manage cash flow more effectively with BNPL.</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Reduce risk when dealing with new suppliers by using Escrow.</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Access a wider range of global suppliers previously out of reach due to payment term inflexibility.</span>
                </li>
              </ul>
            </div>

            {/* For Suppliers */}
            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-[#004235] mb-4">For Suppliers</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Get paid faster and more reliably.</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Reduce exposure to buyer payment default with BNPL and Escrow.</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Offer flexible payment terms to attract more buyers without taking on credit risk themselves.</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Improve cash flow predictability.</span>
                </li>
              </ul>
            </div>

            {/* For the Ecosystem */}
            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-[#004235] mb-4">For the Ecosystem</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Facilitates larger and more frequent transactions by removing financial barriers.</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Builds a more trusted and secure trading environment.</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Enables SMEs to participate more actively in global trade.</span>
                </li>
              </ul>
            </div>
          </div>

          <div className="bg-[#F2F2F2] p-8 rounded-lg border border-gray-200">
            <h3 className="text-xl font-semibold text-[#004235] mb-4">Seamlessly Integrated into Your Transaction Workflow</h3>
            <p className="text-gray-700 mb-6">
              Trade finance options like BNPL and Escrow are presented to eligible users directly within the MyStreamLnk (Customer) portal during the quoting and checkout process. Eligibility is determined by factors including iScore™, transaction value, and partner underwriting criteria.
            </p>
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white"
              asChild
            >
              <Link href="/partners">
                LEARN MORE ABOUT OUR BNPL & ESCROW PARTNERS
                <ChevronRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}