import { <PERSON><PERSON>ight, CheckCircle, Globe, Truck, CreditCard, BarChart3, <PERSON><PERSON>heck } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/industries/polymers-plastics/HeroSection";
import ChallengesSection from "@/components/industries/polymers-plastics/ChallengesSection";
import SolutionsSection from "@/components/industries/polymers-plastics/SolutionsSection";
import UseCasesSection from "@/components/industries/polymers-plastics/UseCasesSection";
import SustainabilitySection from "@/components/industries/polymers-plastics/SustainabilitySection";
import CTASection from "@/components/industries/polymers-plastics/CTASection";

export default function PolymersPlasticsPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <SolutionsSection />

      <UseCasesSection />

      <SustainabilitySection />

      <CTASection />

      <BottomFooter />
    </div>
  );
}