import { XCircle } from "lucide-react";

export default function TraditionalInefficiencySection() {
  const inefficiencies = [
    "Time-consuming manual matching of buyers to suppliers, and products to needs.",
    "Subjective decision-making in pricing, carrier selection, and risk assessment.",
    "Reactive responses to disruptions rather than proactive, predictive interventions.",
    "Inefficient resource allocation due to lack of foresight into demand or logistics bottlenecks.",
    "Significant human effort required for repetitive tasks like document checking and status follow-ups.",
    "Missed opportunities for cost savings and operational optimization hidden within complex data.",
  ];

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Still Relying on Manual Processes and Guesswork?
          </h2>
          <p className="text-xl text-gray-700">
            The Inefficiency of Traditional, Non-Intelligent Trade
          </p>
        </div>
        <div className="max-w-2xl mx-auto">
          <p className="text-lg text-gray-700 mb-8">
            Without the power of AI, industrial supply chains are often burdened by:
          </p>
          <ul className="space-y-4">
            {inefficiencies.map((item, index) => (
              <li key={index} className="flex items-start">
                <XCircle className="h-6 w-6 text-red-500 mr-3 mt-1 flex-shrink-0" />
                <span className="text-gray-700">{item}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  );
}