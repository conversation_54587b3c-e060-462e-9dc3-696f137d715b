import type { Metadata } from "next"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import HeroSection from "@/components/solutions/iscore-partner-ratings/HeroSection"
import ChallengesSection from "@/components/solutions/iscore-partner-ratings/ChallengesSection"
import WhatIsIScoreSection from "@/components/solutions/iscore-partner-ratings/WhatIsIScoreSection"
import BenefitsSection from "@/components/solutions/iscore-partner-ratings/BenefitsSection"
import TransparencySection from "@/components/solutions/iscore-partner-ratings/TransparencySection"
import CTASection from "@/components/solutions/iscore-partner-ratings/CTASection"

export const metadata: Metadata = {
  title: "iScore™ Partner Ratings | StreamLnk",
  description:
    "Navigate the complexities of global partnerships with unparalleled clarity. iScore™ is StreamLnk's proprietary, AI-powered rating system, providing objective, data-driven assessments of every participant in our ecosystem.",
}

export default function IScorePartnerRatingsPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <WhatIsIScoreSection />

      <BenefitsSection />

      <TransparencySection />

      <CTASection />

      <MainFooter />
    </div>
  )
}