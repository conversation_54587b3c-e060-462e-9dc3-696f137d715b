import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"

export default function SubscriptionPlansSection() {
  return (
    <section className="py-16 bg-white" id="plans">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Choose the Intelligence Level That Fits Your Needs
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            StreamResources+ offers tiered access to meet diverse requirements:
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {[{
              title: "Market Watcher",
              subtitle: "Basic Premium",
              features: [
                "High-level StreamIndex™ summaries",
                "Monthly reports",
                "Basic dashboard access"
              ]
            },
            {
              title: "Professional Analyst",
              subtitle: "Advanced Premium",
              features: [
                "Detailed, filterable StreamIndex™",
                "Interactive charts",
                "Deeper analytics",
                "Basic API access"
              ]
            },
            {
              title: "Enterprise Intelligence",
              subtitle: "Full DaaS & Custom",
              features: [
                "Full data granularity",
                "Extensive API access",
                "Custom report builders",
                "Dedicated analyst support"
              ]
            }
          ].map((plan, index) => (
            <div key={index} className="border border-gray-200 rounded-lg overflow-hidden shadow-md flex flex-col">
              <div className="bg-[#004235] text-white p-6 text-center">
                <h3 className="text-xl font-bold mb-1">{plan.title}</h3>
                <p className="text-sm opacity-80">{plan.subtitle}</p>
              </div>
              <div className="p-6 flex-grow">
                <ul className="space-y-3">
                  {plan.features.map((feature, i) => (
                    <li key={i} className="flex items-start">
                      <span className="text-[#028475] mr-2">✓</span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="p-6 border-t border-gray-200">
                <Button 
                  className="w-full bg-[#004235] hover:bg-[#028475] text-white"
                  asChild
                >
                  <Link href="/request-demo">
                    Learn More
                  </Link>
                </Button>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-8">
          <Button 
            className="bg-white hover:bg-gray-100 text-[#004235] border border-[#004235]"
            asChild
          >
            <Link href="/solutions/data-analytics#compare-plans">
              Compare Subscription Plans
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}