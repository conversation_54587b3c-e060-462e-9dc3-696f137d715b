"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Measure Your Impact: StreamLnk's ESG Reporting & Analytics Tools
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Demonstrate your commitment to sustainability and meet growing stakeholder demands. StreamLnk provides integrated tools to track key ESG metrics, source responsible materials, and generate reports for your industrial supply chain.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                className="bg-[#004235] hover:bg-[#028475] text-white px-6"
                size="lg"
                asChild
              >
                <Link href="/request-demo?tool=esg-reporting&source=hero">
                  EXPLORE ESG REPORTING TOOLS
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/tools/esg-reporting/hero-placeholder.jpg" // Placeholder image, to be updated
              alt="StreamLnk ESG Reporting and Analytics Tools"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}