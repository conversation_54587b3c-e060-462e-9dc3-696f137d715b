"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/industries/construction-materials/HeroSection";
import ChallengesSection from "@/components/industries/construction-materials/ChallengesSection";
import SolutionsSection from "@/components/industries/construction-materials/SolutionsSection";
import SustainableConstructionSection from "@/components/industries/construction-materials/SustainableConstructionSection";
import BenefitsSection from "@/components/industries/construction-materials/BenefitsSection";
import CTASection from "@/components/industries/construction-materials/CTASection";

export default function ConstructionMaterialsPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <ChallengesSection />
      <SolutionsSection />
      <SustainableConstructionSection />
      <BenefitsSection />
      <CTASection />

      <BottomFooter />
    </div>
  );
}