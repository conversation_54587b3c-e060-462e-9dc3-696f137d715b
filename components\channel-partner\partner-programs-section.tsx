import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { User, Users, Building, ArrowRight } from "lucide-react";
import Link from "next/link";

const programs = [
  {
    id: "independent-agent",
    icon: <User className="h-10 w-10 text-[#004235]" />,
    title: "Independent Sales Agent (MyStreamLnk+)",
    idealFor: "Experienced industrial sales professionals, freelance B2B consultants, individuals with strong regional networks in target industries.",
    role: "Onboard new B2B customers (buyers) to the MyStreamLnk portal, manage their accounts, help them navigate the platform, facilitate their sourcing and transactions.",
    tools: "Access to the MyStreamLnk+ Agent Portal to manage client portfolios, generate quotes, track orders, and view commissions.",
    compensation: "Commission-based on the GMV of transactions from your onboarded clients. Tiered commission structures rewarding higher performance.",
    ctaLink: "/partners/channel-reseller/apply-agent", // Placeholder link
    ctaText: "Learn More & Apply as an Independent Agent",
  },
  {
    id: "regional-distributor",
    icon: <Building className="h-10 w-10 text-[#004235]" />,
    title: "Official Regional Distributor",
    idealFor: "Established distribution companies, trading houses, or larger consultancies with significant regional presence and a team capable of handling a larger territory and volume.",
    role: "Act as StreamLnk's primary representative in an assigned territory. Onboard and manage a larger portfolio of both buyers and potentially suppliers. May involve building a small local team of sub-agents (using MyStreamLnk+ team features). Provide first-line support and market development activities.",
    tools: "Access to MyStreamLnk+ (with enhanced distributor features for team management and reporting), dedicated support from StreamLnk regional managers.",
    compensation: "Higher volume-based commission tiers, potential for exclusivity bonuses, co-marketing funds, and deeper strategic collaboration.",
    ctaLink: "/partners/channel-reseller/inquire-distributor", // Placeholder link
    ctaText: "Inquire About Becoming an Official Regional Distributor",
  },
];

export default function PartnerProgramsSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Two Ways to Partner, Unlimited Potential
          </h2>
          <p className="text-xl font-semibold text-[#028475]">
            Our Channel Partner Programs – Choose Your Path
          </p>
        </div>

        <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {programs.map((program) => (
            <Card key={program.id} className="flex flex-col bg-white shadow-lg hover:shadow-xl transition-shadow border-2 border-transparent hover:border-[#004235]/20">
              <CardHeader className="items-center text-center">
                <div className="p-4 bg-[#028475]/10 rounded-full mb-4 inline-block">
                  {program.icon}
                </div>
                <CardTitle className="text-2xl text-[#004235]">{program.title}</CardTitle>
              </CardHeader>
              <CardContent className="flex-grow space-y-4 px-6 pb-6">
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Ideal For:</h4>
                  <p className="text-gray-600 text-sm">{program.idealFor}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Your Role:</h4>
                  <p className="text-gray-600 text-sm">{program.role}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Key Tools:</h4>
                  <p className="text-gray-600 text-sm">{program.tools}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Compensation:</h4>
                  <p className="text-gray-600 text-sm">{program.compensation}</p>
                </div>
              </CardContent>
              <CardFooter className="px-6 pb-6">
                <Button variant="outline" className="w-full border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white transition-colors" asChild>
                  <Link href={program.ctaLink}>
                    {program.ctaText} <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}