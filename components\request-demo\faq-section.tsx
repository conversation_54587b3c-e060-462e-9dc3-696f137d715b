import Link from "next/link"
import { HelpCircle, Mail, Calendar } from "lucide-react"

export function RequestDemoFAQ() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Still Have Questions?
          </h2>
          <h3 className="text-xl font-semibold text-[#028475] mb-6 text-center">We're Here to Help</h3>
            
          <div className="bg-[#F2F2F2] p-8 rounded-lg">
            <p className="text-center text-gray-700 mb-6">
              If you have preliminary questions before requesting a demo, feel free to:
            </p>
              
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <Link 
                href="/contact"
                className="flex flex-col items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow"
              >
                <HelpCircle className="h-8 w-8 text-[#004235] mb-3" />
                <span className="font-semibold text-[#004235]">Visit Our Help Center</span>
                <span className="text-sm text-gray-600">Find answers to common questions</span>
              </Link>
              <Link 
                href="/contact"
                className="flex flex-col items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow"
              >
                <Mail className="h-8 w-8 text-[#004235] mb-3" />
                <span className="font-semibold text-[#004235]">Email Us Directly</span>
                <span className="text-sm text-gray-600">Send us a message</span>
              </Link>
              <Link 
                href="/contact"
                className="flex flex-col items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow"
              >
                <Calendar className="h-8 w-8 text-[#004235] mb-3" />
                <span className="font-semibold text-[#004235]">Schedule a Call</span>
                <span className="text-sm text-gray-600">Book a time to speak with us</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}