import { <PERSON>, ListFilter, <PERSON>, <PERSON>, Award } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export default function AuctionLogicSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Smart Auction Logic & Fair Marketplace Rules
          </h2>
          <p className="text-lg text-gray-700">
            Our platform is designed with intelligent rules to ensure a balanced and effective marketplace.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
          <Card className="border-2 border-[#F2F2F2] shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="rounded-full bg-[#004235]/10 w-12 h-12 flex items-center justify-center mb-4">
                <Clock className="h-6 w-6 text-[#004235]" />
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">Controlled Frequency</h3>
              <p className="text-gray-600">
                Generally, only one auction per specific product/region combination runs at a time to maintain market
                integrity.
              </p>
            </CardContent>
          </Card>

          <Card className="border-2 border-[#F2F2F2] shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="rounded-full bg-[#004235]/10 w-12 h-12 flex items-center justify-center mb-4">
                <ListFilter className="h-6 w-6 text-[#004235]" />
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">Sequential Scheduling</h3>
              <p className="text-gray-600">
                Auctions are often automatically queued to ensure fair visibility and prevent market saturation.
              </p>
            </CardContent>
          </Card>

          <Card className="border-2 border-[#F2F2F2] shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="rounded-full bg-[#004235]/10 w-12 h-12 flex items-center justify-center mb-4">
                <Globe className="h-6 w-6 text-[#004235]" />
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">Regional & Product Specifics</h3>
              <p className="text-gray-600">
                Country-specific rules regarding product finish status or import/export requirements are considered.
              </p>
            </CardContent>
          </Card>

          <Card className="border-2 border-[#F2F2F2] shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="rounded-full bg-[#004235]/10 w-12 h-12 flex items-center justify-center mb-4">
                <Bell className="h-6 w-6 text-[#004235]" />
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">Intelligent Filtering & Notifications</h3>
              <p className="text-gray-600">
                Smart filters based on past bid history, participation frequency, and win rates help tailor the
                experience.
              </p>
            </CardContent>
          </Card>

          <Card className="border-2 border-[#F2F2F2] shadow-sm hover:shadow-md transition-shadow md:col-span-2 lg:col-span-1">
            <CardContent className="pt-6">
              <div className="rounded-full bg-[#004235]/10 w-12 h-12 flex items-center justify-center mb-4">
                <Award className="h-6 w-6 text-[#004235]" />
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">Participation Rewards & Incentives</h3>
              <p className="text-gray-600">
                Optional smart notifications and participation rewards can be offered during low-demand seasons or for
                specific clearance periods.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
