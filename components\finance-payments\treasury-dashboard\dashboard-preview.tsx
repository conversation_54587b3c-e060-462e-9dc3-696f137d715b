import Image from "next/image"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function DashboardPreview() {
  return (
    <div className="border border-[#f3f4f6] rounded-xl overflow-hidden bg-white shadow-lg">
      <div className="p-4 border-b border-[#f3f4f6] bg-[#f3f4f6]/50">
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full max-w-md mx-auto grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="fx">FX Rates</TabsTrigger>
            <TabsTrigger value="payments">Payments</TabsTrigger>
            <TabsTrigger value="liquidity">Liquidity</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      <div className="relative h-[400px] md:h-[500px] w-full">
        <Image
          src="/placeholder.svg?height=500&width=1000"
          alt="Treasury dashboard interface"
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
        <div className="absolute bottom-4 left-4 right-4 p-4 bg-white/90 backdrop-blur-sm rounded-lg border border-[#f3f4f6]">
          <h3 className="text-lg font-semibold text-[#004235] mb-2">Comprehensive Financial Dashboard</h3>
          <p className="text-gray-600 text-sm">
            Get a complete view of your global financial operations with real-time data and actionable insights.
          </p>
        </div>
      </div>
    </div>
  )
}
