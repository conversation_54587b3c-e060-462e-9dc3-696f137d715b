"use client";

import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Info, Star } from "lucide-react";
import Link from "next/link";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6 lg:text-left">
              StreamLnk Tier & Rewards Program
            </h1>
            <p className="text-xl text-gray-700 mb-10 lg:text-left">
              Unlock Exclusive Benefits: Welcome to the StreamLnk Tier & Rewards Program! Your commitment and performance on the StreamLnk platform deserve recognition. Our multi-tiered program rewards active Customers, Suppliers, Agents, and Service Providers with valuable benefits, exclusive access, and enhanced status.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 lg:justify-start">
              <Button 
                className="bg-[#004235] hover:bg-[#028475] text-white px-6 w-full sm:w-auto"
                size="lg"
                asChild
              >
                <Link href="#program-details"> {/* Placeholder link, update to section ID */}
                  LEARN MORE
                  <Info className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button 
                variant="outline"
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-6 w-full sm:w-auto"
                size="lg"
                asChild
              >
                <Link href="#how-to-earn"> {/* Placeholder link, update to section ID */}
                  HOW TO EARN
                  <Star className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl mt-8 lg:mt-0">
            <Image
              src="/images/rewards/rewards-hero.webp" // Placeholder - suggest user to replace
              alt="StreamLnk Tier & Rewards Program Hero Image"
              fill
              className="object-cover"
              priority
            />
            {/* Consider adding a more relevant image or illustration for rewards program */}
          </div>
        </div>
      </div>
    </section>
  );
}