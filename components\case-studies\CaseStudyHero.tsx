"use client";

import Image from "next/image";

interface CaseStudyHeroProps {
  clientName: string;
  caseStudyHeadline: string;
  heroImageUrl: string;
  heroImageAlt: string;
}

export default function CaseStudyHero({
  clientName,
  caseStudyHeadline,
  heroImageUrl,
  heroImageAlt,
}: CaseStudyHeroProps) {
  return (
    <section className="relative bg-gradient-to-r from-[#004235] to-[#028475] text-white py-20 md:py-32">
      <div className="absolute inset-0">
        <Image
          src={heroImageUrl}
          alt={heroImageAlt}
          fill
          className="object-cover opacity-20" // Reduced opacity for better readability
          priority
        />
        <div className="absolute inset-0 bg-[#004235] opacity-50"></div> {/* Darker overlay */}
      </div>
      <div className="container mx-auto px-4 relative z-10 text-center">
        <p className="text-lg uppercase tracking-wider text-gray-300 mb-2">
          StreamLnk Success Story
        </p>
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
          {clientName}
        </h1>
        <p className="text-2xl md:text-3xl font-light max-w-3xl mx-auto">
          {caseStudyHeadline}
        </p>
      </div>
    </section>
  );
}