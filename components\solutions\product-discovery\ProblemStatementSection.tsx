"use client"

import { CheckCircle } from "lucide-react"

export function ProblemStatementSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Tired of Inefficient Material Sourcing?
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Finding the specific grade of polymer, the right chemical composition, or the certified industrial material you need can be a major bottleneck. Traditional methods often involve:
          </p>
          
          <div className="space-y-4">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <p className="text-gray-700">Navigating fragmented supplier websites and outdated product lists.</p>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <p className="text-gray-700">Difficulty comparing specifications and certifications side-by-side.</p>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <p className="text-gray-700">Limited visibility into global availability or alternative material options.</p>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <p className="text-gray-700">Uncertainty about supplier reliability for niche or specialized products.</p>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <p className="text-gray-700">Wasting valuable time on preliminary supplier outreach just to confirm product availability.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}