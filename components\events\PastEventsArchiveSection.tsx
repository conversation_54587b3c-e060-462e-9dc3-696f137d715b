"use client";

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, Download, PlayCircle, BookOpen, Filter } from 'lucide-react';

interface ArchivedEvent {
  id: string;
  title: string;
  originalDate: string;
  description: string;
  type: 'Webinar Recording' | 'Presentation' | 'Keynote' | 'Event Recap';
  recordingLink?: string;
  slidesLink?: string;
  recapLink?: string;
  tags: string[]; // For filtering by topic/speaker
}

const sampleArchivedEvents: ArchivedEvent[] = [
  {
    id: "navigating-q4-logistics",
    title: "Navigating Q4: Key Logistics Challenges and How to Prepare",
    originalDate: "Oct 10, 2023",
    description: "A deep dive into the logistical hurdles of Q4 and strategic preparations.",
    type: "Webinar Recording",
    recordingLink: "#watch-q4-logistics",
    slidesLink: "#download-q4-slides",
    recapLink: "/resources/latest-articles/q4-logistics-recap",
    tags: ["logistics", "q4", "supply chain"],
  },
  {
    id: "recycled-polymers-sourcing",
    title: "The Rise of Recycled Polymers: Sourcing Challenges & Opportunities",
    originalDate: "Sept 28, 2023",
    description: "Exploring the complexities and potential in the recycled polymers market.",
    type: "Presentation",
    slidesLink: "#download-polymers-slides",
    tags: ["sustainability", "polymers", "sourcing"],
  },
  {
    id: "future-of-trade-finance-keynote",
    title: "Keynote: The Future of Digital Trade Finance",
    originalDate: "Aug 15, 2023",
    description: "Insights from our CEO on the evolving landscape of trade finance.",
    type: "Keynote",
    recordingLink: "#watch-trade-finance-keynote",
    tags: ["trade finance", "digitalization", "ceo insights"],
  },
];

// Dummy filter options - in a real app, these might be dynamic
const topicOptions = ["All Topics", "Logistics", "Sustainability", "AI", "Market Trends", "Polymers"];
const eventTypeOptions = ["All Types", "Webinar Recording", "Presentation", "Keynote", "Event Recap"];

export default function PastEventsArchiveSection() {
  // Basic state for filters - can be expanded
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTopic, setSelectedTopic] = useState('All Topics');
  const [selectedEventType, setSelectedEventType] = useState('All Types');

  const filteredEvents = sampleArchivedEvents.filter(event => {
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
                          event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          event.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesTopic = selectedTopic === 'All Topics' || event.tags.includes(selectedTopic.toLowerCase());
    const matchesEventType = selectedEventType === 'All Types' || event.type === selectedEventType;
    return matchesSearch && matchesTopic && matchesEventType;
  });

  return (
    <section id="past-events" className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-3">
            Catch Up Anytime: Our Library of Past Events & Recordings
          </h2>
          <p className="text-lg text-gray-700 max-w-2xl mx-auto">
            Missed a live session? Access our archive of webinar recordings, presentation slides, keynote speeches, and event recaps to learn at your convenience.
          </p>
        </div>

        {/* Search/Filter Controls */}
        <div className="bg-[#f3f4f6] p-6 rounded-lg shadow-sm mb-10">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
            <div className="col-span-1 md:col-span-2 lg:col-span-2">
              <label htmlFor="search-archive" className="block text-sm font-medium text-gray-700 mb-1">Search Archive</label>
              <div className="flex">
                <Input
                  type="text"
                  id="search-archive"
                  placeholder="Search by keyword, title, speaker..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-grow rounded-r-none border-gray-300 focus:border-[#004235] focus:ring-[#004235]"
                />
                <Button className="bg-[#004235] hover:bg-[#028475] text-white rounded-l-none px-4">
                  <Search className="h-5 w-5" />
                  <span className="sr-only">Search</span>
                </Button>
              </div>
            </div>
            <div>
              <label htmlFor="filter-topic" className="block text-sm font-medium text-gray-700 mb-1">Filter by Topic</label>
              <Select value={selectedTopic} onValueChange={setSelectedTopic}>
                <SelectTrigger id="filter-topic" className="w-full border-gray-300 focus:border-[#004235] focus:ring-[#004235]">
                  <SelectValue placeholder="Select Topic" />
                </SelectTrigger>
                <SelectContent>
                  {topicOptions.map(topic => (
                    <SelectItem key={topic} value={topic}>{topic}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label htmlFor="filter-event-type" className="block text-sm font-medium text-gray-700 mb-1">Filter by Event Type</label>
              <Select value={selectedEventType} onValueChange={setSelectedEventType}>
                <SelectTrigger id="filter-event-type" className="w-full border-gray-300 focus:border-[#004235] focus:ring-[#004235]">
                  <SelectValue placeholder="Select Event Type" />
                </SelectTrigger>
                <SelectContent>
                  {eventTypeOptions.map(type => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Archive Listings */}
        {filteredEvents.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {filteredEvents.map((event) => (
              <div key={event.id} className="bg-white rounded-lg shadow-md p-6 flex flex-col">
                <h4 className="text-lg font-semibold text-[#004235] mb-1">{event.title}</h4>
                <p className="text-xs text-gray-500 mb-1">Original Date: {event.originalDate}</p>
                <p className="text-xs text-gray-500 mb-3">Type: {event.type}</p>
                <p className="text-sm text-gray-600 mb-4 flex-grow">{event.description}</p>
                <div className="mt-auto space-y-2">
                  {event.recordingLink && (
                    <Button variant="outline" className="w-full border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white" asChild>
                      <Link href={event.recordingLink}><PlayCircle className="mr-2 h-4 w-4" /> WATCH RECORDING</Link>
                    </Button>
                  )}
                  {event.slidesLink && (
                    <Button variant="outline" className="w-full border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white" asChild>
                      <Link href={event.slidesLink}><Download className="mr-2 h-4 w-4" /> DOWNLOAD SLIDES</Link>
                    </Button>
                  )}
                  {event.recapLink && (
                    <Button variant="link" className="w-full text-[#028475] hover:text-[#004235] justify-start p-0" asChild>
                      <Link href={event.recapLink}><BookOpen className="mr-2 h-4 w-4" /> READ RECAP ARTICLE</Link>
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-center text-gray-600 py-8">No archived events match your current filters. Try broadening your search!</p>
        )}

        <div className="text-center">
          <Button className="bg-[#004235] hover:bg-[#028475] text-white" size="lg" asChild>
            {/* This link might go to a more comprehensive archive page if one exists, or just be for show if this is the main archive view */}
            <Link href="/resources/archive">BROWSE ALL ARCHIVED CONTENT</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}