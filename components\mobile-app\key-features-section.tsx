import React from 'react';
import { Lock, Bell, LayoutDashboard, MessageSquare, Users, ShoppingCart, Package, FileText, Truck, Briefcase, BarChart, ScanLine, Globe, CheckCircle } from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const coreFeatures = [
  {
    icon: <Lock className="h-6 w-6 text-[#028475]" />,
    title: "Secure Login & Profile Management",
    description: "Access your account safely with biometric login options (Face ID/Touch ID).",
  },
  {
    icon: <Bell className="h-6 w-6 text-[#028475]" />,
    title: "Real-Time Notifications",
    description:
      "Customizable push notifications for order updates, shipment alerts, RFQ responses, compliance reminders, and auction activity.",
  },
  {
    icon: <LayoutDashboard className="h-6 w-6 text-[#028475]" />,
    title: "Dashboard Overview",
    description: "A mobile-optimized view of your key metrics and pending actions.",
  },
  {
    icon: <MessageSquare className="h-6 w-6 text-[#028475]" />,
    title: "Communication Hub",
    description: "Access and respond to messages within the StreamLnk platform.",
  },
];

const myStreamLnkFeatures = [
  { icon: <Package className="h-6 w-6 text-[#028475]" />, title: "Shipment Tracking", description: "Live tracking of all orders with map views and milestone updates." },
  { icon: <ShoppingCart className="h-6 w-6 text-[#028475]" />, title: "Quote & Order Management", description: "Review quotes, approve orders, and view order history." },
  { icon: <Users className="h-6 w-6 text-[#028475]" />, title: "Product Discovery (Simplified)", description: "Browse key product categories and submit quick RFQs." },
  { icon: <FileText className="h-6 w-6 text-[#028475]" />, title: "Document Access", description: "View key documents related to your orders." },
];

const eStreamFeatures = [
  { icon: <ShoppingCart className="h-6 w-6 text-[#028475]" />, title: "Order Management", description: "Receive new order/RFQ notifications, confirm orders, update production status." },
  { icon: <Bell className="h-6 w-6 text-[#028475]" />, title: "Inventory Alerts", description: "Get notified of low stock for key products." },
  { icon: <BarChart className="h-6 w-6 text-[#028475]" />, title: "Auction Monitoring", description: "Track bids on your active auction listings." },
];

const myStreamLnkPlusFeatures = [
  { icon: <Briefcase className="h-6 w-6 text-[#028475]" />, title: "Client Portfolio Overview", description: "Quick access to key client information and order statuses." },
  { icon: <FileText className="h-6 w-6 text-[#028475]" />, title: "Quote Generation (Simplified)", description: "Create and send basic quotes on the go." },
  { icon: <BarChart className="h-6 w-6 text-[#028475]" />, title: "Commission Snapshot", description: "View recent earnings." },
];

const streamFreightFeatures = [
  { icon: <Truck className="h-6 w-6 text-[#028475]" />, title: "Job Management", description: "View assigned loads, route details, and contact information." },
  { icon: <Package className="h-6 w-6 text-[#028475]" />, title: "Status Updates", description: "Easily update pickup, in-transit, and delivery milestones." },
  { icon: <ScanLine className="h-6 w-6 text-[#028475]" />, title: "POD Scanning & Upload", description: "Utilize the device camera to instantly capture and upload Proof of Delivery." },
  { icon: <MessageSquare className="h-6 w-6 text-[#028475]" />, title: "Communication with Dispatch/StreamLnk Ops", description: "" },
];

const otherPortalsFeatures = [
  { icon: <Bell className="h-6 w-6 text-[#028475]" />, title: "Urgent Job Notifications" },
  { icon: <CheckCircle className="h-6 w-6 text-[#028475]" />, title: "Quick Status Updates" },
  { icon: <FileText className="h-6 w-6 text-[#028475]" />, title: "Essential Document Uploads/Viewing" },
];

interface FeatureItem {
  icon: JSX.Element;
  title: string;
  description?: string;
}

const FeatureCard = ({ feature }: { feature: FeatureItem }) => (
  <div className="flex items-start space-x-3 p-3 bg-white rounded-md shadow-sm border border-gray-100">
    <div className="flex-shrink-0 mt-1">{feature.icon}</div>
    <div>
      <h4 className="font-medium text-[#004235]">{feature.title}</h4>
      {feature.description && <p className="text-sm text-gray-600">{feature.description}</p>}
    </div>
  </div>
);

export default function KeyFeaturesSection() {
  return (
    <section id="mobile-features" className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Powerful Functionality, Optimized for Mobile
          </h2>
          <p className="text-lg text-gray-700">
            Key Features of the StreamLnk Mobile App (Planned). Our dedicated native mobile apps (for iOS and Android)
            will provide a streamlined and intuitive experience, offering access to core features from your primary
            StreamLnk portal:
          </p>
        </div>

        <Tabs defaultValue="core" className="max-w-4xl mx-auto">
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 mb-8 bg-gray-200">
            <TabsTrigger value="core" className="data-[state=active]:bg-[#004235] data-[state=active]:text-white">Core</TabsTrigger>
            <TabsTrigger value="mystreamlnk" className="data-[state=active]:bg-[#004235] data-[state=active]:text-white">MyStreamLnk</TabsTrigger>
            <TabsTrigger value="estream" className="data-[state=active]:bg-[#004235] data-[state=active]:text-white">E-Stream</TabsTrigger>
            <TabsTrigger value="mystreamlnkplus" className="data-[state=active]:bg-[#004235] data-[state=active]:text-white">MyStreamLnk+</TabsTrigger>
            <TabsTrigger value="streamfreight" className="data-[state=active]:bg-[#004235] data-[state=active]:text-white">StreamFreight</TabsTrigger>
            <TabsTrigger value="otherportals" className="data-[state=active]:bg-[#004235] data-[state=active]:text-white">Other</TabsTrigger>
          </TabsList>

          <TabsContent value="core">
            <Card className="bg-transparent border-none shadow-none">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl text-[#004235]">For All Users (Core Features)</CardTitle>
              </CardHeader>
              <CardContent className="grid md:grid-cols-2 gap-4">
                {coreFeatures.map((feature) => (
                  <FeatureCard key={feature.title} feature={feature} />
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="mystreamlnk">
            <Card className="bg-transparent border-none shadow-none">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl text-[#004235]">MyStreamLnk (Customer) Mobile Experience</CardTitle>
              </CardHeader>
              <CardContent className="grid md:grid-cols-2 gap-4">
                {myStreamLnkFeatures.map((feature) => (
                  <FeatureCard key={feature.title} feature={feature} />
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="estream">
            <Card className="bg-transparent border-none shadow-none">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl text-[#004235]">E-Stream (Supplier) Mobile Experience</CardTitle>
              </CardHeader>
              <CardContent className="grid md:grid-cols-2 gap-4">
                {eStreamFeatures.map((feature) => (
                  <FeatureCard key={feature.title} feature={feature} />
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="mystreamlnkplus">
            <Card className="bg-transparent border-none shadow-none">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl text-[#004235]">MyStreamLnk+ (Agent) Mobile Experience</CardTitle>
              </CardHeader>
              <CardContent className="grid md:grid-cols-2 gap-4">
                {myStreamLnkPlusFeatures.map((feature) => (
                  <FeatureCard key={feature.title} feature={feature} />
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="streamfreight">
            <Card className="bg-transparent border-none shadow-none">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl text-[#004235]">StreamFreight (Driver/Carrier) Mobile Functionality</CardTitle>
              </CardHeader>
              <CardContent className="grid md:grid-cols-2 gap-4">
                {streamFreightFeatures.map((feature) => (
                  <FeatureCard key={feature.title} feature={feature} />
                ))}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="otherportals">
            <Card className="bg-transparent border-none shadow-none">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl text-[#004235]">StreamGlobe+ & StreamPak Mobile (Lighter)</CardTitle>
              </CardHeader>
               <p className="text-center text-gray-600 mb-4">Primarily for receiving urgent job notifications, providing quick status updates, and essential document uploads/viewing.</p>
              <CardContent className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {otherPortalsFeatures.map((feature) => (
                  <FeatureCard key={feature.title} feature={feature} />
                ))}
              </CardContent>
            </Card>
          </TabsContent>

        </Tabs>
      </div>
    </section>
  );
}