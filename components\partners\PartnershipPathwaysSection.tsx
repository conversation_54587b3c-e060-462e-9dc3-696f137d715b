"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Factory, Truck, Users, BarChart3, Settings2, ArrowRight } from 'lucide-react';

interface Pathway {
  id: string;
  icon: React.ElementType;
  title: string;
  description: string;
  linkText: string;
  linkHref: string;
}

const pathways: Pathway[] = [
  {
    id: "suppliers",
    icon: Factory,
    title: "For Suppliers & Producers (E-Stream)",
    description: "List your polymers, chemicals, energy products, and other industrial materials to reach a global B2B buyer base. Benefit from AI-pricing, auction tools, and integrated logistics.",
    linkText: "Become Verified Supplier",
    linkHref: "/partners/suppliers" // Placeholder link
  },
  {
    id: "logistics",
    icon: Truck,
    title: "For Logistics & Freight Providers (StreamFreight, StreamGlobe, StreamPak)",
    description: "Offer your land transport (truck/rail), sea freight, customs clearance, warehousing, or packaging services to the StreamLnk network. Receive qualified job assignments and streamline operations.",
    linkText: "Join Logistics Network",
    linkHref: "/partners/logistics" // Placeholder link
  },
  {
    id: "agents",
    icon: Users,
    title: "For Sales Agents & Distributors (MyStreamLnk+)",
    description: "Represent StreamLnk in your region, onboard new customers, manage client portfolios, and earn commissions by facilitating trade on our platform.",
    linkText: "Become StreamLnk Agent",
    linkHref: "/partners/agents" // Placeholder link
  },
  {
    id: "technology",
    icon: BarChart3, // Using BarChart3 for Data, could also be Code, Database, Zap etc.
    title: "For Technology & Data Providers",
    description: "Integrate your solutions (ERP, SCM, Fintech, Compliance Tech) with StreamLnk via APIs, or partner with StreamResources+ to provide or consume valuable market data.",
    linkText: "Explore Tech Partnerships",
    linkHref: "/partners/technology" // Placeholder link
  },
  {
    id: "strategic",
    icon: Settings2, // Using Settings2 for Strategic Alliance, could also be Handshake, Zap etc.
    title: "For Strategic Alliance Partners",
    description: "Collaborate with StreamLnk on joint go-to-market initiatives, co-development of industry solutions, or broader strategic alignments to drive mutual growth and innovation.",
    linkText: "Discuss Strategic Alliance",
    linkHref: "/partners/strategic-alliance" // Placeholder link
  }
];

export default function PartnershipPathwaysSection() {
  return (
    <section id="partnership-pathways" className="py-16 md:py-20 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4 text-center">
            A Diverse Ecosystem: Partnership Pathways for Every Role
          </h2>
          <p className="text-xl md:text-2xl text-[#028475] mb-12 text-center">
            Who Can Partner with StreamLnk? Opportunities Across the Value Chain
          </p>
          <p className="text-lg text-gray-700 mb-12 text-center max-w-3xl mx-auto">
            StreamLnk offers tailored partnership programs for a wide range of businesses and professionals integral to the industrial materials supply chain:
          </p>

          <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
            {pathways.map((pathway) => (
              <div key={pathway.id} className="bg-white p-8 rounded-xl shadow-lg hover:shadow-2xl transition-shadow duration-300 flex flex-col border border-gray-200">
                <div className="flex items-center text-[#028475] mb-5">
                  <pathway.icon className="h-10 w-10 mr-4 flex-shrink-0" />
                  <h3 className="text-2xl font-semibold text-[#004235]">{pathway.title}</h3>
                </div>
                <p className="text-gray-600 mb-6 flex-grow text-base leading-relaxed">
                  {pathway.description}
                </p>
                <Button variant="outline" className="mt-auto border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full py-3 text-base group" asChild>
                  <Link href={pathway.linkHref}>
                    {pathway.linkText}
                    <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}