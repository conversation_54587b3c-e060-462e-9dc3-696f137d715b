import Link from 'next/link';
import { Button } from '@/components/ui/button'; // Assuming you have a Button component
import { ArrowRight, ChevronLeft } from 'lucide-react';

// Placeholder for fetching related links content based on slug or other criteria
// In a real app, this data would come from a CMS or database
const getRelatedLinksContent = async (/* caseStudySlug: string */) => {
  // Simulate fetching data
  await new Promise(resolve => setTimeout(resolve, 100));
  // Example content structure - adapt as needed
  return {
    relatedSolutions: [
      { name: "Buyer Portal Solutions", href: "/solutions/buyer-portal" },
      { name: "Logistics Management", href: "/solutions/logistics" },
      { name: "Supplier Collaboration Hub", href: "/solutions/supplier-hub" },
    ],
    ctaText: "Request a Demo Tailored to Your Business",
    ctaLink: "/request-demo",
    backToCaseStudiesText: "Back to All Case Studies",
    backToCaseStudiesLink: "/case-studies",
  };
};

const RelatedLinksSection: React.FC = async () => {
  const content = await getRelatedLinksContent();

  return (
    <section className="py-12 md:py-16 lg:py-20 bg-white border-t border-gray-200">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-start">
          <div>
            <h3 className="text-2xl font-semibold text-[#004235] mb-6">
              Related StreamLnk Solutions
            </h3>
            <ul className="space-y-3">
              {content.relatedSolutions.map((solution, index) => (
                <li key={index}>
                  <Link href={solution.href} legacyBehavior>
                    <a className="text-lg text-[#028475] hover:text-[#004235] hover:underline flex items-center group">
                      {solution.name}
                      <ArrowRight className="w-5 h-5 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </a>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-[#F2F2F2] p-8 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold text-[#004235] mb-4">
              Ready to Transform Your Business?
            </h3>
            <p className="text-gray-700 mb-6">
              Discover how StreamLnk can address your specific challenges and drive growth.
            </p>
            <Link href={content.ctaLink} passHref legacyBehavior>
              <Button 
                asChild 
                className="w-full md:w-auto bg-[#004235] hover:bg-[#028475] text-white text-lg py-3 px-6"
              >
                <a>
                  {content.ctaText}
                  <ArrowRight className="w-5 h-5 ml-2" />
                </a>
              </Button>
            </Link>
          </div>
        </div>

        <div className="mt-12 md:mt-16 text-center">
          <Link href={content.backToCaseStudiesLink} legacyBehavior>
            <a className="inline-flex items-center text-[#028475] hover:text-[#004235] font-medium text-lg group">
              <ChevronLeft className="w-5 h-5 mr-2 transition-transform group-hover:-translate-x-1" />
              {content.backToCaseStudiesText}
            </a>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default RelatedLinksSection;