"use client";

import { Bar<PERSON>hart2, BadgeInfo, Gift, Bell, ExternalLink } from "lucide-react";

export default function TrackProgressSection() {
  const features = [
    {
      icon: <BadgeInfo className="h-8 w-8 text-[#028475]" />,
      title: "View Current Tier & Badge",
      description: "Instantly see your achieved tier status and the corresponding digital badge."
    },
    {
      icon: <BarChart2 className="h-8 w-8 text-[#028475]" />,
      title: "Track Progress to Next Tier",
      description: "Monitor your performance metrics and understand what's needed to reach the next level."
    },
    {
      icon: <Gift className="h-8 w-8 text-[#028475]" />,
      title: "See Reward Points & Redeem",
      description: "Check your accumulated reward points balance and browse available rewards for redemption."
    },
    {
      icon: <Bell className="h-8 w-8 text-[#028475]" />,
      title: "View Points History & Notifications",
      description: "Access a detailed history of your points earnings and receive important program updates."
    }
  ];

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Track Your Progress & Rewards
          </h2>
          <p className="text-lg text-gray-700 leading-relaxed">
            Within your dedicated StreamLnk portal (MyStreamLnk, E-Stream, MyStreamLnk+, etc.), find the "My Tier & Rewards" section. Here you can:
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="bg-[#F2F2F2] p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center text-center">
              <div className="mb-4">
                {feature.icon}
              </div>
              <h3 className="font-semibold text-[#004235] text-lg mb-2">{feature.title}</h3>
              <p className="text-gray-600 text-sm leading-relaxed">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}