"use client";

import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight, DatabaseZap } from 'lucide-react';
import Image from 'next/image';

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Connect Your Fleet: Partner with StreamGlobe
            </h1>
            <p className="text-xl text-gray-700 mb-10">
              Ocean Carriers and NVOCCs: Integrate your services with StreamLnk's rapidly expanding B2B industrial trade ecosystem. StreamGlobe offers seamless API connectivity for schedules, bookings, tracking, and documentation, giving you access to a new stream of qualified cargo.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                className="bg-[#004235] hover:bg-[#028475] text-white px-8 py-3"
                size="lg"
                asChild
              >
                <Link href="/contact-us?subject=API+Integration+StreamGlobe">
                  DISCUSS INTEGRATION
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button 
                variant="outline" 
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-8 py-3"
                size="lg"
                asChild
              >
                <Link href="/developer/api-documentation/streamglobe-carrier">
                  VIEW API DOCS
                  <DatabaseZap className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[450px] rounded-lg overflow-hidden shadow-xl mt-12 lg:mt-0">
            <Image
              src="/images/placeholder-streamglobe-carrier.webp" // TODO: User to replace with a relevant image for StreamGlobe carriers/sea freight
              alt="StreamLnk StreamGlobe for Sea Freight Carriers"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: User to replace with a relevant image for StreamGlobe carriers/sea freight */}
          </div>
        </div>
      </div>
    </section>
  );
}