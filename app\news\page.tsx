"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import NewsHeroSection from "@/components/news/NewsHeroSection";
import NewsWelcomeSection from "@/components/news/NewsWelcomeSection";
import NewsFiltersSection from "@/components/news/NewsFiltersSection";
import NewsGridSection from "@/components/news/NewsGridSection";
import NewsSubscriptionCtaSection from "@/components/news/NewsSubscriptionCtaSection";
import PressCenterLinkSection from "@/components/news/PressCenterLinkSection";

export default function NewsPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <NewsHeroSection />
      <NewsWelcomeSection />
      <NewsFiltersSection />
      <NewsGridSection />
      <NewsSubscriptionCtaSection />
      <PressCenterLinkSection />

      <BottomFooter />
    </div>
  );
}