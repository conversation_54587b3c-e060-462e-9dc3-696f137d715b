"use client";

import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export default function HeroSection() {
  return (
    <section className="bg-[#172d2d] py-16 md:py-24 text-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl">
          <div className="flex items-center mb-6">
            <div className="w-12 h-1 bg-white mr-3"></div>
            <span className="font-medium">SMART SOURCING & PROCUREMENT</span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Revolutionize Your Sourcing & Procurement with StreamLnk
          </h1>
          <p className="text-lg mb-8">
            Move beyond manual processes and fragmented supplier networks. StreamLnk provides a unified, AI-driven platform to find the best materials, at the best price, from anywhere in the world – faster and more efficiently.
          </p>
          <div className="flex flex-wrap gap-4">
            <Button
              className="bg-[#172d2d] border border-white px-6 hover:bg-white hover:text-[#172d2d]"
              size="lg"
              asChild
            >
              <Link href="/request-demo" className="text-white">
                Request Demo
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="outline"
              className="bg-[#172d2d] border border-white px-6 hover:bg-white hover:text-[#172d2d]"
              size="lg"
              asChild
            >
              <Link href="/e-stream" className="text-white">
                Explore Suppliers
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}