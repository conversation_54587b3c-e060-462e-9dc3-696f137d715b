"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import EventsHeroSection from "@/components/events/EventsHeroSection";
import WhyEngageSection from "@/components/events/WhyEngageSection";
import UpcomingEventsSection from "@/components/events/UpcomingEventsSection";
import PastEventsArchiveSection from "@/components/events/PastEventsArchiveSection";
import PartnerOpportunitiesSection from "@/components/events/PartnerOpportunitiesSection";
import EventSubscriptionCtaSection from "@/components/events/EventSubscriptionCtaSection";

export default function EventsPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <EventsHeroSection />
      <WhyEngageSection />
      <UpcomingEventsSection />
      <PastEventsArchiveSection />
      <PartnerOpportunitiesSection />
      <EventSubscriptionCtaSection />

      <BottomFooter />
    </div>
  );
}