import { CheckCircle } from "lucide-react";

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-10 text-center">
            Gain Efficiency, Reduce Uncertainty, Improve Satisfaction
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Save Time & Resources</h3>
                  <p className="text-gray-600">Drastically reduce the manual effort of tracking multiple shipments across different systems.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Improve Planning & Forecasting</h3>
                  <p className="text-gray-600">More accurate ETAs allow for better production scheduling and inventory management.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Enhance Customer Satisfaction</h3>
                  <p className="text-gray-600">Provide timely and accurate shipment updates to internal teams and end customers.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Proactive Issue Resolution</h3>
                  <p className="text-gray-600">Early detection of delays allows for quicker intervention and mitigation.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Streamlined Record Keeping</h3>
                  <p className="text-gray-600">All shipment data and documents are centralized for easy access and review.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Reduce Risk</h3>
                  <p className="text-gray-600">Enhanced visibility helps prevent shipments from falling through the cracks.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}