"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowRight, ChevronRight } from "lucide-react";

interface RelatedSolutionLink {
  name: string;
  href: string;
}

interface RelatedSolutionsSectionProps {
  relatedSolutions: RelatedSolutionLink[];
  requestDemoLink: string;
  allCaseStudiesLink: string;
}

export default function RelatedSolutionsSection({
  relatedSolutions,
  requestDemoLink,
  allCaseStudiesLink,
}: RelatedSolutionsSectionProps) {
  return (
    <section className="py-10 border-t border-gray-200 mt-12">
      <h2 className="text-2xl font-semibold text-[#004235] mb-8 text-center">
        Explore Further
      </h2>
      <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
        <Button
          className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
          size="lg"
          asChild
        >
          <Link href={requestDemoLink}>
            REQUEST CUSTOM DEMO
            <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </Button>
        <Button
          variant="outline"
          className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
          size="lg"
          asChild
        >
          <Link href={allCaseStudiesLink}>
            ALL CASE STUDIES
            <ChevronRight className="ml-2 h-5 w-5" />
          </Link>
        </Button>
      </div>
      {relatedSolutions.length > 0 && (
        <div className="mt-10 text-center">
          <h3 className="text-lg font-medium text-gray-700 mb-4">
            Related StreamLnk Solutions:
          </h3>
          <div className="flex flex-wrap justify-center gap-x-4 gap-y-2">
            {relatedSolutions.map((solution, index) => (
              <Button
                key={index}
                variant="link"
                className="text-[#028475] hover:text-[#004235] px-1"
                asChild
              >
                <Link href={solution.href}>{solution.name}</Link>
              </Button>
            ))}
          </div>
        </div>
      )}
    </section>
  );
}