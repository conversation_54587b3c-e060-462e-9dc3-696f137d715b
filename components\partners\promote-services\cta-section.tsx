import { Button } from "@/components/ui/button"

export function CtaSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            The Future of Industrial Trade Needs Your Expertise. Be Seen Where It Counts.
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Your service is a critical component of the global supply chain. In the digital age, visibility,
            credibility, and precise timing determine success. StreamLnk provides you with all three.
          </p>
          <p className="text-lg text-gray-700 mb-12">
            Join a rapidly growing network of trusted, verified service providers who are helping to power global
            trade—more transparently, more efficiently, and more digitally than ever before.
          </p>

          <Button className="bg-[#004235] hover:bg-[#028475] text-white px-8 py-6 text-lg rounded-md mb-8">
            Apply to Promote Your Services Globally on StreamLnk Today
          </Button>

          <p className="text-md text-gray-600 italic">
            Reach active buyers and decision-makers exactly when they need your services—not just in a static directory,
            but within the dynamic flow of international trade.
          </p>

          <div className="mt-8">
            <p className="text-xl font-semibold text-[#004235]">
              StreamLnk: Where Trade Meets Technology, and Services Meet Opportunity.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
