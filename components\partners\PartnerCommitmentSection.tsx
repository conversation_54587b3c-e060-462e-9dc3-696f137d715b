"use client";

import { <PERSON><PERSON>heck, Zap, Eye, Users, Lightbulb, Award } from 'lucide-react';

interface CommitmentItem {
  icon: React.ElementType;
  title: string;
  description: string;
}

const commitments: CommitmentItem[] = [
  {
    icon: ShieldCheck,
    title: "A Robust & Reliable Platform",
    description: "Continuously investing in technology to ensure a seamless and secure experience."
  },
  {
    icon: Zap,
    title: "Tools for Growth",
    description: "Features and resources designed to help you manage your business efficiently and expand your reach."
  },
  {
    icon: Eye,
    title: "Transparency & Fair Practices",
    description: "Clear terms, transparent fee structures (where applicable), and objective performance metrics (iScore™)."
  },
  {
    icon: Users,
    title: "Dedicated Support",
    description: "Access to onboarding assistance, technical support, and partner management resources."
  },
  {
    icon: Lightbulb,
    title: "Opportunities for Collaboration & Innovation",
    description: "A willingness to co-create solutions that benefit the entire ecosystem."
  },
  {
    icon: Award,
    title: "Recognition & Rewards",
    description: "Through our Tier & Rewards program, we acknowledge and incentivize excellence."
  }
];

export default function PartnerCommitmentSection() {
  return (
    <section className="py-16 md:py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4 text-center">
            Building Success, Together: What You Can Expect as a StreamLnk Partner
          </h2>
          <p className="text-xl md:text-2xl text-[#028475] mb-12 text-center">
            Our Commitment to Our Partners
          </p>
          <p className="text-lg text-gray-700 mb-12 text-center max-w-3xl mx-auto">
            We view our partners as integral extensions of the StreamLnk ecosystem. We are committed to providing:
          </p>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-10">
            {commitments.map((commitment, index) => (
              <div key={index} className="flex flex-col items-center text-center p-6 bg-gray-50 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                <commitment.icon className="h-12 w-12 text-[#028475] mb-5" />
                <h3 className="text-xl font-semibold text-[#004235] mb-2">{commitment.title}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {commitment.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}