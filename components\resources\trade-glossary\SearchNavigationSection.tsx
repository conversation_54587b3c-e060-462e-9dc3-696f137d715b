"use client";

import { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from 'lucide-react';

export default function SearchNavigationSection() {
  const [searchTerm, setSearchTerm] = useState('');
  const alphabet = [...'ABCDEFGHIJKLMNOPQRSTUVWXYZ', '#'];

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Implement search functionality here
    console.log('Searching for:', searchTerm);
    // This would typically trigger a filter on the GlossaryListingsSection
  };

  const handleLetterClick = (letter: string) => {
    // Implement letter navigation functionality here
    console.log('Navigating to letter:', letter);
    // This would typically scroll to or filter the GlossaryListingsSection for terms starting with this letter
  };

  return (
    <section className="py-12 md:py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          <form onSubmit={handleSearch} className="flex gap-2 mb-8">
            <Input
              type="search"
              placeholder='Search Glossary (e.g., "Incoterms," "Bill of Lading," "StreamIndex™")'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-grow border-gray-300 focus:border-[#004235] focus:ring-[#004235]"
            />
            <Button type="submit" className="bg-[#004235] hover:bg-[#028475] text-white">
              <Search className="h-5 w-5 mr-2 sm:mr-0 md:mr-2" />
              <span className="hidden sm:inline">Search Term</span>
            </Button>
          </form>

          <div className="text-center mb-4">
            <p className="text-sm text-gray-600 font-medium">Browse A-Z:</p>
          </div>
          <nav className="flex flex-wrap justify-center gap-1 md:gap-2">
            {alphabet.map((letter) => (
              <Button
                key={letter}
                variant="outline"
                size="sm"
                onClick={() => handleLetterClick(letter)}
                className="border-gray-300 text-gray-700 hover:bg-gray-200 hover:border-gray-400 focus:bg-[#028475] focus:text-white focus:border-[#028475] w-8 h-8 md:w-9 md:h-9"
                aria-label={`Go to terms starting with ${letter}`}
              >
                {letter}
              </Button>
            ))}
          </nav>
        </div>
      </div>
    </section>
  );
}