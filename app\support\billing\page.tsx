import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import Link from 'next/link';
import { Search, ChevronRight, DollarSign, FileText, Users, CreditCard, Settings, AlertTriangle, Mail, MessageSquare, Edit3 } from 'lucide-react';

const billingFaqCategories = [
  {
    categoryTitle: "For Buyers (MyStreamLnk Users)",
    icon: <Users className="w-6 h-6 text-[#028475]" />,
    questions: [
      {
        question: "How do I receive my invoices?",
        answer: "Invoices are generated automatically upon order confirmation (proforma) and final delivery. They are accessible in your MyStreamLnk portal under 'Billing & Payments' and also sent via email to your registered address."
      },
      {
        question: "What are the accepted payment methods?",
        answer: "StreamLnk supports secure wire transfers, Escrow payments for eligible transactions, and Buy Now, Pay Later (BNPL) options through our integrated fintech partners for qualified buyers. Specific options are presented at checkout based on your transaction details."
      },
      {
        question: "How do I track my payment status?",
        answer: "You can view the status of all your payments (Pending, Processed, Cleared) in the 'Billing & Payments' section of your MyStreamLnk portal. Real-time updates are provided as payments are processed."
      },
      {
        question: "What happens if my payment is overdue?",
        answer: "Our automated system sends reminders following a 7/3/1 day schedule. If an invoice remains overdue beyond agreed terms, your ability to place new orders may be temporarily suspended, and pending shipments may be put on hold until the matter is resolved. Please contact support immediately if you anticipate payment issues."
      },
      {
        question: "How does the Buy Now, Pay Later (BNPL) option work?",
        answer: (
          <>
            BNPL allows qualified buyers to defer payment while receiving goods immediately. Terms vary by transaction size and buyer creditworthiness. Detailed terms are presented during checkout. For more information, visit our <Link href='/finance/bnpl' className='text-[#028475] hover:text-[#004235] hover:underline'>BNPL Guide</Link>.
          </>
        )
      }
    ]
  },
  {
    categoryTitle: "For Suppliers (E-Stream Users)",
    icon: <CreditCard className="w-6 h-6 text-[#028475]" />,
    questions: [
      {
        question: "How and when do I get paid?",
        answer: "Payouts are processed after successful delivery confirmation and buyer payment clearance (or per BNPL/Escrow terms). Payout schedules and methods are outlined in your supplier agreement and visible in your E-Stream 'Billing & Payments' dashboard."
      },
      {
        question: "How are StreamLnk platform fees calculated and deducted?",
        answer: "Our standard transaction fee is a percentage of the gross sale value, deducted before payout. All fees are clearly itemized in your payout statements. Specific rates are detailed in your supplier agreement."
      },
      {
        question: "Where can I see my payout history?",
        answer: "Your E-Stream portal has a detailed 'Payout History' section with downloadable statements. You can filter by date range, transaction type, and export data for your records."
      },
      {
        question: "What information is included in my payout statement?",
        answer: "Payout statements include gross transaction value, platform fees, any applicable taxes or adjustments, net payout amount, and payment method details. Each line item references the original order for easy reconciliation."
      }
    ]
  },
  {
    categoryTitle: "For Service Providers (StreamFreight, StreamGlobe+, StreamPak)",
    icon: <FileText className="w-6 h-6 text-[#028475]" />,
    questions: [
      {
        question: "How do I submit invoices for my services?",
        answer: "You can submit invoices for completed jobs/services directly through your respective portal (StreamFreight, StreamGlobe+, StreamPak). Upload supporting documentation and track approval status in real-time."
      },
      {
        question: "What are the payment terms for service providers?",
        answer: "Payment terms are outlined in your partner agreement and typically processed Net 30 days after invoice approval and service verification. Some premium partners may have expedited payment terms."
      },
      {
        question: "How do I track my service invoice status?",
        answer: "All submitted invoices show their current status (Pending Review, Approved, Paid) in your portal dashboard. You'll receive notifications at each status change."
      }
    ]
  },
  {
    categoryTitle: "Agents & Distributors (MyStreamLnk+ Users)",
    icon: <Users className="w-6 h-6 text-[#028475]" />,
    questions: [
      {
        question: "How are my commissions calculated and paid?",
        answer: "Commissions are calculated based on the GMV of successful transactions from your onboarded clients, as per your agent agreement. Payouts are typically made monthly and detailed in your MyStreamLnk+ 'Commissions & Payouts' dashboard."
      },
      {
        question: "Where can I view my commission history?",
        answer: "Your MyStreamLnk+ portal provides a comprehensive 'Commission History' section with monthly statements, client performance breakdowns, and downloadable reports."
      },
      {
        question: "What happens if a client transaction is disputed or refunded?",
        answer: "Commission adjustments due to disputes or refunds will be reflected in your next statement. You'll receive notification of any adjustments with detailed explanations."
      }
    ]
  },
  {
    categoryTitle: "General Financial Topics",
    icon: <DollarSign className="w-6 h-6 text-[#028475]" />,
    questions: [
      {
        question: "How does StreamLnk handle multi-currency transactions and FX rates?",
        answer: "StreamLnk uses real-time market FX rates for currency conversions. Rates are locked at the time of transaction confirmation. You can view historical rates and conversion details in your transaction history."
      },
      {
        question: "What are the terms for using Escrow services?",
        answer: (
          <>
            Escrow services provide secure payment holding until delivery confirmation. Terms include holding periods, release conditions, and dispute resolution procedures. Full terms are available in our <Link href='/legal/escrow-terms' className='text-[#028475] hover:text-[#004235] hover:underline'>Escrow Agreement</Link>.
          </>
        )
      },
      {
        question: "How do I dispute a charge or report a billing discrepancy?",
        answer: (
          <>
            Submit billing disputes through our support ticket system, selecting 'Billing Dispute' as the category. Include transaction IDs and detailed explanations. You can also contact our finance team directly at <a href="mailto:<EMAIL>" className="text-[#028475] hover:text-[#004235] hover:underline"><EMAIL></a>.
          </>
        )
      },
      {
        question: "How do I update my banking information for payouts?",
        answer: "Banking information can be updated in the 'Profile & Settings' section of your portal under 'Financial Information.' Changes require verification and may take 1-2 business days to process."
      }
    ]
  }
];

const popularBillingQuestions = [
  { question: "How do I track my payment status?", link: "#for-buyers-mystreamnk-users-2" },
  { question: "How are StreamLnk platform fees calculated?", link: "#for-suppliers-e-stream-users-1" },
  { question: "What are the terms for using BNPL?", link: "#for-buyers-mystreamnk-users-4" },
];

const BillingPaymentSupportPage = () => {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="bg-[#F2F2F2] py-16 md:py-24">
          <div className="container mx-auto px-4 md:px-6 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-4">
              Billing & Payment Support
            </h1>
            <p className="text-lg md:text-xl text-gray-700 mb-8 max-w-3xl mx-auto">
              Managing Your Trade Finances with Ease. Find answers to your questions about StreamLnk invoices, payment processes, platform fees, supplier/partner payouts, and our integrated financial solutions like BNPL and Escrow.
            </p>
            <div className="max-w-xl mx-auto flex flex-col sm:flex-row gap-4">
              <Input
                type="search"
                placeholder='Search Billing FAQs (e.g., "invoice due date," "commission payout," "BNPL terms")'
                className="flex-grow text-base py-3 px-4"
              />
              <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white text-base">
                <Search className="mr-2 h-5 w-5" /> Search FAQs
              </Button>
            </div>
          </div>
        </section>

        {/* Browse by Category Section */}
        <section className="py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] text-center mb-4">
              Transparency in Every Transaction
            </h2>
            <p className="text-lg text-gray-600 text-center mb-12 max-w-2xl mx-auto">
              StreamLnk is committed to providing a clear and transparent financial experience for all our users. Browse billing FAQs by user type and topic.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
              {billingFaqCategories.map((cat) => (
                <Link key={cat.categoryTitle} href={`#${cat.categoryTitle.toLowerCase().replace(/\s+/g, '-').replace(/[&+,()]/g, '')}`} passHref>
                  <Button variant="outline" className="w-full justify-start text-left h-auto py-4 px-4 border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white">
                    <div className="flex items-center">
                      {cat.icon}
                      <span className="ml-3">{cat.categoryTitle}</span>
                    </div>
                  </Button>
                </Link>
              ))}
            </div>

            {/* FAQ Display Area */}
            {billingFaqCategories.map((category, catIndex) => (
              <div key={catIndex} id={`${category.categoryTitle.toLowerCase().replace(/\s+/g, '-').replace(/[&+,()]/g, '')}`} className="mb-12">
                <h3 className="text-2xl font-semibold text-[#004235] mb-6 pt-4 border-t border-gray-200 flex items-center">
                  {category.icon}
                  <span className="ml-3">{category.categoryTitle}</span>
                </h3>
                <Accordion type="single" collapsible className="w-full">
                  {category.questions.map((faq, faqIndex) => (
                    <AccordionItem key={faqIndex} value={`item-${catIndex}-${faqIndex}`} id={`${category.categoryTitle.toLowerCase().replace(/\s+/g, '-').replace(/[&+,()]/g, '')}-${faqIndex}`}>
                      <AccordionTrigger className="text-left hover:no-underline text-lg font-medium text-gray-800">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent className="text-gray-600 text-base leading-relaxed pt-2 pb-4">
                        {faq.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            ))}
          </div>
        </section>

        {/* Popular Questions Section */}
        <section className="bg-[#F2F2F2] py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] text-center mb-10">
              Popular Billing Questions
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {popularBillingQuestions.map((pq, index) => (
                <Card key={index} className="bg-white hover:shadow-md transition-shadow duration-300">
                  <CardContent className="p-6">
                    <Link href={pq.link} className="text-[#028475] hover:text-[#004235] hover:underline font-semibold text-lg">
                      {pq.question}
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Manage Financial Settings Section */}
        <section className="py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6 text-center">
            <Card className="bg-gray-50 p-8 md:p-12 max-w-3xl mx-auto">
              <Settings className="h-12 w-12 text-[#028475] mx-auto mb-6" />
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
                Keep Your Financial Information Up-to-Date
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                You can manage your bank account details (for payouts), default currency preferences, and billing contact information securely within the "Profile & Settings" section of your respective StreamLnk portal.
              </p>
              <Link href="/profile/settings" passHref>
                <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white">
                  Go to My Profile & Settings <ChevronRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </Card>
          </div>
        </section>

        {/* Still Need Help Section */}
        <section className="py-16 bg-[#F2F2F2]">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
                Finance Support Team Here
              </h2>
              <p className="text-xl text-gray-700 mb-8">
                Need billing or payment assistance? For specific questions about invoices, payments, commissions, or any financial aspect of your StreamLnk account not covered in our FAQs:
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
                  size="lg"
                  asChild
                >
                  <Link href="/support/submit-ticket?category=billing">
                    SUBMIT TICKET
                    <MessageSquare className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button 
                  variant="outline" 
                  className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
                  size="lg"
                  asChild
                >
                  <Link href="mailto:<EMAIL>">
                    EMAIL SUPPORT
                    <Mail className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
              </div>
              <p className="mt-6 text-sm text-gray-500 max-w-xl mx-auto">
                For security, please avoid sending sensitive payment information like full credit card numbers via email. Use our secure support ticket system for such inquiries.
              </p>
            </div>
          </div>
        </section>

      </main>
      <BottomFooter />
    </div>
  );
};

export default BillingPaymentSupportPage;