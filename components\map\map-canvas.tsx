"use client"

import { useEffect, useRef, useMemo, useCallback } from "react"
import * as d3 from "d3"
import { partners, routes, regions } from "@/data/map-data"
import type {
  WorldData,
  Partner,
  Route,
  LiveAsset,
  RealTimeData
} from "@/types/map-types"

interface MapCanvasProps {
  worldData: WorldData | null
  width: number
  height: number
  currentZoom: number
  hoveredPartner: string | null
  selectedPartner: Partner | null
  hoveredRoute: string | null
  selectedRoute: Route | null
  selectedAsset: LiveAsset | null
  showRoutes: boolean
  showAssets: boolean
  showHeatmap: boolean
  showEvents: boolean
  showRegionalStats: boolean
  animationSpeed: number[]
  filteredPartners: Partner[]
  filteredRoutes: Route[]
  filteredAssets: LiveAsset[]
  realTimeData: RealTimeData
  onPartnerClick: (partner: Partner) => void
  onRouteClick: (route: Route) => void
  onAssetClick: (asset: LiveAsset) => void
  onPartnerHover: (partnerId: string | null) => void
  onRouteHover: (routeId: string | null) => void
}

export function MapCanvas({
  worldData,
  width,
  height,
  currentZoom,
  hoveredPartner,
  selectedPartner,
  hoveredRoute,
  selectedRoute,
  selectedAsset,
  showRoutes,
  showAssets,
  showHeatmap,
  showEvents,
  showRegionalStats,
  animationSpeed,
  filteredPartners,
  filteredRoutes,
  filteredAssets,
  realTimeData,
  onPartnerClick,
  onRouteClick,
  onAssetClick,
  onPartnerHover,
  onRouteHover,
}: MapCanvasProps) {
  const svgRef = useRef<SVGSVGElement>(null)

  // Stable projection
  const projection = useMemo(
    () =>
      d3
        .geoMercator()
        .scale(150)
        .translate([width / 2, height / 2]),
    [width, height],
  )

  const pathGenerator = useMemo(() => d3.geoPath().projection(projection), [projection])

  // Stable arc generator
  const createArc = useCallback(
    (from: [number, number], to: [number, number]) => {
      const geoInterpolate = d3.geoInterpolate(from, to)
      const points = []
      for (let i = 0; i <= 100; i++) {
        points.push(geoInterpolate(i / 100))
      }
      return points.map((point) => projection(point)).filter((p) => p !== null) as [number, number][]
    },
    [projection],
  )

  // Main rendering effect
  useEffect(() => {
    if (!worldData || !svgRef.current) return

    const svg = d3.select(svgRef.current)
    svg.selectAll("*").remove()

    // Create defs for patterns and gradients
    const defs = svg.append("defs")

    // Marching ants pattern
    const marchingAnts = defs
      .append("pattern")
      .attr("id", "marching-ants")
      .attr("patternUnits", "userSpaceOnUse")
      .attr("width", 20)
      .attr("height", 4)

    marchingAnts.append("rect").attr("width", 20).attr("height", 4).attr("fill", "transparent")
    marchingAnts.append("rect").attr("x", 0).attr("y", 0).attr("width", 10).attr("height", 4).attr("fill", "#52AAA3")

    marchingAnts
      .append("animateTransform")
      .attr("attributeName", "patternTransform")
      .attr("type", "translate")
      .attr("values", "0,0;20,0;0,0")
      .attr("dur", `${2 / animationSpeed[0]}s`)
      .attr("repeatCount", "indefinite")

    // Pulsing gradient
    const pulsingGradient = defs
      .append("linearGradient")
      .attr("id", "pulsing-gradient")
      .attr("x1", "0%")
      .attr("y1", "0%")
      .attr("x2", "100%")
      .attr("y2", "0%")

    pulsingGradient.append("stop").attr("offset", "0%").attr("stop-color", "#52AAA3").attr("stop-opacity", 0.3)
    pulsingGradient.append("stop").attr("offset", "50%").attr("stop-color", "#52AAA3").attr("stop-opacity", 1)
    pulsingGradient.append("stop").attr("offset", "100%").attr("stop-color", "#52AAA3").attr("stop-opacity", 0.3)

    pulsingGradient
      .selectAll("stop")
      .append("animate")
      .attr("attributeName", "stop-opacity")
      .attr("values", "0.3;1;0.3")
      .attr("dur", `${1.5 / animationSpeed[0]}s`)
      .attr("repeatCount", "indefinite")

    // Asset trail gradient
    const trailGradient = defs
      .append("linearGradient")
      .attr("id", "asset-trail")
      .attr("x1", "0%")
      .attr("y1", "0%")
      .attr("x2", "100%")
      .attr("y2", "0%")

    trailGradient.append("stop").attr("offset", "0%").attr("stop-color", "#52AAA3").attr("stop-opacity", 0)
    trailGradient.append("stop").attr("offset", "70%").attr("stop-color", "#52AAA3").attr("stop-opacity", 0.3)
    trailGradient.append("stop").attr("offset", "100%").attr("stop-color", "#52AAA3").attr("stop-opacity", 0.8)

    // Create country paths
    const countries = svg
      .selectAll("path")
      .data(worldData.countries.features || [])
      .enter()
      .append("path")
      .attr("d", pathGenerator as any)
      .attr("class", "country")
      .style("fill", "#5DC1B9")
      .style("stroke", "#52AAA3")
      .style("stroke-width", "0.5px")
      .style("cursor", "pointer")
      .style("transition", "all 0.3s ease")

    // Regional heatmap overlay
    if (showHeatmap && currentZoom < 3) {
      const heatmapGroup = svg.append("g").attr("class", "heatmap")

      regions.forEach((region) => {
        const stats = realTimeData.regionalStats.find((s) => s.regionId === region.id)
        if (!stats) return

        const [x, y] = projection(region.center) || [0, 0]
        const intensity = Math.min(stats.activeShipments / 100, 1)
        const radius = 50 + intensity * 100

        heatmapGroup
          .append("circle")
          .attr("cx", x)
          .attr("cy", y)
          .attr("r", radius)
          .style("fill", `rgba(82, 170, 163, ${intensity * 0.3})`)
          .style("stroke", "#52AAA3")
          .style("stroke-width", "1px")
          .style("stroke-opacity", intensity * 0.5)
          .style("pointer-events", "none")

        if (intensity > 0.7) {
          heatmapGroup
            .append("circle")
            .attr("cx", x)
            .attr("cy", y)
            .attr("r", radius * 0.8)
            .style("fill", "none")
            .style("stroke", "#52AAA3")
            .style("stroke-width", "2px")
            .style("opacity", 0.6)
            .style("pointer-events", "none")
            .append("animate")
            .attr("attributeName", "r")
            .attr("values", `${radius * 0.8};${radius * 1.2};${radius * 0.8}`)
            .attr("dur", "3s")
            .attr("repeatCount", "indefinite")
        }
      })
    }

    // Add connection lines (routes)
    if (showRoutes && currentZoom >= 0.8) {
      const routeGroup = svg.append("g").attr("class", "routes")

      filteredRoutes.forEach((route) => {
        const fromPartner = partners.find((p) => p.id === route.from)
        const toPartner = partners.find((p) => p.id === route.to)

        if (!fromPartner || !toPartner) return

        const arcPoints = createArc(fromPartner.coordinates, toPartner.coordinates)
        if (arcPoints.length === 0) return

        const lineGenerator = d3
          .line()
          .x((d) => d[0])
          .y((d) => d[1])
          .curve(d3.curveCardinal)

        const pathData = lineGenerator(arcPoints)
        if (!pathData) return

        const getRouteStyle = (route: Route) => {
          switch (route.intensity) {
            case "high":
              return { width: 4, opacity: 0.8, color: "#004235" }
            case "medium":
              return { width: 3, opacity: 0.6, color: "#028475" }
            case "low":
              return { width: 2, opacity: 0.4, color: "#52AAA3" }
            default:
              return { width: 2, opacity: 0.4, color: "#6b7280" }
          }
        }

        const style = getRouteStyle(route)

        // Background line
        routeGroup
          .append("path")
          .attr("d", pathData)
          .attr("class", `route-bg route-${route.id}`)
          .style("fill", "none")
          .style("stroke", "#000")
          .style("stroke-width", style.width + 2)
          .style("opacity", 0.3)
          .style("pointer-events", "none")

        // Main route line
        const routeLine = routeGroup
          .append("path")
          .attr("d", pathData)
          .attr("class", `route route-${route.id}`)
          .style("fill", "none")
          .style("stroke", style.color)
          .style("stroke-width", style.width)
          .style("opacity", hoveredRoute === route.id ? 1 : style.opacity)
          .style("cursor", "pointer")
          .style("transition", "all 0.3s ease")

        if (route.status === "planned") {
          routeLine.style("stroke-dasharray", "10,5")
        }

        routeLine
          .on("mouseenter", function () {
            onRouteHover(route.id)
            d3.select(this)
              .style("opacity", 1)
              .style("stroke-width", style.width + 2)
          })
          .on("mouseleave", function () {
            if (selectedRoute?.id !== route.id) {
              onRouteHover(null)
              d3.select(this).style("opacity", style.opacity).style("stroke-width", style.width)
            }
          })
          .on("click", () => {
            onRouteClick(route)
          })
      })
    }

    // Add assets layer
    if (showAssets && currentZoom >= 1.2) {
      const assetGroup = svg.append("g").attr("class", "assets")

      filteredAssets.forEach((asset) => {
        const route = routes.find((r) => r.id === asset.routeId)
        if (!route) return

        const fromPartner = partners.find((p) => p.id === route.from)
        const toPartner = partners.find((p) => p.id === route.to)
        if (!fromPartner || !toPartner) return

        const arcPoints = createArc(fromPartner.coordinates, toPartner.coordinates)
        if (arcPoints.length === 0) return

        const currentIndex = Math.floor(asset.progress * (arcPoints.length - 1))
        const currentPoint = arcPoints[currentIndex]
        if (!currentPoint) return

        const [x, y] = currentPoint

        // Asset marker
        const assetMarker = assetGroup
          .append("g")
          .attr("class", `asset-marker asset-${asset.id}`)
          .attr("transform", `translate(${x}, ${y})`)
          .style("cursor", "pointer")
          .on("click", () => onAssetClick(asset))

        const getAssetColor = (status: string) => {
          switch (status) {
            case "on-time":
              return "#028475"
            case "delayed":
              return "#004235"
            case "critical":
              return "#ef4444"
            default:
              return "#52AAA3"
          }
        }

        const assetColor = getAssetColor(asset.status)

        // Main asset circle
        assetMarker
          .append("circle")
          .attr("r", 6)
          .style("fill", assetColor)
          .style("stroke", "#fff")
          .style("stroke-width", "2px")

        // Asset type icon
        const getAssetIcon = (type: string) => {
          switch (type) {
            case "ship":
              return "🚢"
            case "truck":
              return "🚛"
            case "train":
              return "🚂"
            case "plane":
              return "✈️"
            default:
              return "📦"
          }
        }

        assetMarker
          .append("text")
          .attr("text-anchor", "middle")
          .attr("dy", "0.3em")
          .style("font-size", "8px")
          .style("pointer-events", "none")
          .text(getAssetIcon(asset.type))
      })
    }

    // Add regional statistics overlays
    if (showRegionalStats && currentZoom < 2.5) {
      const statsGroup = svg.append("g").attr("class", "regional-stats")

      regions.forEach((region) => {
        const stats = realTimeData.regionalStats.find((s) => s.regionId === region.id)
        if (!stats) return

        const [x, y] = projection(region.center) || [0, 0]

        const statsBox = statsGroup
          .append("g")
          .attr("class", `stats-box stats-${region.id}`)
          .attr("transform", `translate(${x}, ${y + 40})`)
          .style("cursor", "pointer")

        // Background box
        statsBox
          .append("rect")
          .attr("x", -50)
          .attr("y", -20)
          .attr("width", 100)
          .attr("height", 40)
          .attr("rx", 8)
          .style("fill", "rgba(255, 255, 255, 0.95)")
          .style("stroke", "#028475")
          .style("stroke-width", "1px")
          .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))")

        // Active shipments
        statsBox
          .append("text")
          .attr("text-anchor", "middle")
          .attr("y", -8)
          .style("fill", "#004235")
          .style("font-size", "10px")
          .style("font-weight", "600")
          .text(`${stats.activeShipments} Active`)

        // Total value
        statsBox
          .append("text")
          .attr("text-anchor", "middle")
          .attr("y", 5)
          .style("fill", "#475569")
          .style("font-size", "8px")
          .text(`$${(stats.totalValue / 1000000).toFixed(1)}M`)

        // Efficiency with color coding
        statsBox
          .append("text")
          .attr("text-anchor", "middle")
          .attr("y", 15)
          .style("fill", stats.efficiency > 90 ? "#028475" : stats.efficiency > 80 ? "#004235" : "#ef4444")
          .style("font-size", "8px")
          .style("font-weight", "500")
          .text(`${stats.efficiency}% Eff.`)
      })
    }

    // Add partners layer
    if (currentZoom >= 1.5) {
      const partnerGroup = svg.append("g").attr("class", "partners")

      filteredPartners.forEach((partner) => {
        const [x, y] = projection(partner.coordinates) || [0, 0]

        const marker = partnerGroup
          .append("g")
          .attr("class", `partner-marker partner-${partner.id}`)
          .style("cursor", "pointer")
          .on("click", () => onPartnerClick(partner))
          .on("mouseenter", () => onPartnerHover(partner.id))
          .on("mouseleave", () => onPartnerHover(null))

        // Main marker
        const markerSize = partner.tier === "premium" ? 12 : partner.tier === "standard" ? 10 : 8
        marker
          .append("circle")
          .attr("cx", x)
          .attr("cy", y)
          .attr("r", markerSize)
          .style("fill", "#52AAA3")
          .style("stroke", "#fff")
          .style("stroke-width", "2px")
          .style("filter", hoveredPartner === partner.id ? "brightness(1.3)" : "none")
          .style("transition", "all 0.3s ease")

        // Partner name label
        if (hoveredPartner === partner.id || selectedPartner?.id === partner.id) {
          marker
            .append("rect")
            .attr("x", x - 40)
            .attr("y", y - 35)
            .attr("width", 80)
            .attr("height", 20)
            .attr("rx", 10)
            .style("fill", "rgba(255, 255, 255, 0.95)")
            .style("stroke", "#52AAA3")
            .style("stroke-width", "1px")

          marker
            .append("text")
            .attr("x", x)
            .attr("y", y - 22)
            .attr("text-anchor", "middle")
            .style("fill", "#1e293b")
            .style("font-size", "10px")
            .style("font-weight", "600")
            .text(partner.name.length > 15 ? partner.name.substring(0, 15) + "..." : partner.name)
        }
      })
    }

  }, [
    worldData,
    pathGenerator,
    currentZoom,
    hoveredPartner,
    selectedPartner,
    hoveredRoute,
    selectedRoute,
    selectedAsset,
    showRoutes,
    showAssets,
    showHeatmap,
    showEvents,
    showRegionalStats,
    animationSpeed,
    filteredPartners,
    filteredRoutes,
    filteredAssets,
    realTimeData,
    createArc,
    projection,
    onPartnerClick,
    onRouteClick,
    onAssetClick,
    onPartnerHover,
    onRouteHover,
  ])

  return (
    <svg
      ref={svgRef}
      width={width}
      height={height}
      className="bg-white/50"
      style={{ maxWidth: "100%", height: "auto" }}
    />
  )
}
