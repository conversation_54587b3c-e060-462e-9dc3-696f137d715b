import Link from "next/link";
import Image from "next/image";
import { ArrowRight } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Drive Sustainability Across Your Supply Chain: ESG Tracking & Insights by StreamLnk
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Meet your corporate responsibility goals and build more resilient, ethical supply chains. StreamLnk provides the tools and data to track Environmental, Social, and Governance (ESG) factors in your industrial material procurement.
            </p>
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                EXPLORE SOLUTIONS – REQUEST DEMO
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/placeholder-image.svg" // Placeholder image
              alt="ESG Tracking & Insights"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}