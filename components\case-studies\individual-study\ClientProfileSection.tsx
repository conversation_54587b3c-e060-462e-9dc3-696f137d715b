interface ClientData {
  clientName: string;
  industry?: string; // Assuming industry might be optional or part of a richer data structure
  size?: string; // Optional
  keyRole?: string; // Optional
  // Add other relevant fields from your CaseStudyData type if needed
}

interface ClientProfileSectionProps {
  clientData: ClientData; // Using a subset or specific part of CaseStudyData
}

const ClientProfileSection: React.FC<ClientProfileSectionProps> = ({ clientData }) => {
  return (
    <section className="py-12 md:py-16 lg:py-20 bg-gray-50">
      <div className="container mx-auto px-4 md:px-6">
        <h2 className="text-2xl md:text-3xl font-semibold text-[#004235] mb-6 text-center md:text-left">
          Client Profile
        </h2>
        <div className="bg-white p-6 md:p-8 rounded-lg shadow-md grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-gray-700 mb-1">Company</h3>
            <p className="text-xl text-[#028475] font-semibold">{clientData.clientName}</p>
          </div>
          {clientData.industry && (
            <div>
              <h3 className="text-lg font-medium text-gray-700 mb-1">Industry</h3>
              <p className="text-xl text-gray-800">{clientData.industry}</p>
            </div>
          )}
          {clientData.size && (
            <div>
              <h3 className="text-lg font-medium text-gray-700 mb-1">Company Size</h3>
              <p className="text-xl text-gray-800">{clientData.size}</p>
            </div>
          )}
          {clientData.keyRole && (
            <div>
              <h3 className="text-lg font-medium text-gray-700 mb-1">Key Role</h3>
              <p className="text-xl text-gray-800">{clientData.keyRole}</p>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default ClientProfileSection;