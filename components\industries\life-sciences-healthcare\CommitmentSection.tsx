"use client"

import Link from 'next/link';
import { Shield<PERSON>heck, FileText, Users, Activity } from 'lucide-react';

const commitments = [
  {
    icon: Users,
    title: "Rigorous Supplier Vetting",
    description: "Only onboarding suppliers who can demonstrate adherence to necessary quality and regulatory standards for the life sciences sector."
  },
  {
    icon: FileText,
    title: "Documented Chain of Custody",
    description: "Facilitating the secure exchange and storage of all necessary documentation at each step."
  },
  {
    icon: ShieldCheck,
    title: "Specialized Logistics Partner Network",
    description: "Connecting you with carriers and warehouses equipped and certified for healthcare product handling."
  },
  {
    icon: Activity,
    title: "Real-Time Monitoring & Alerts",
    description: "Providing visibility and proactive notifications for any deviations in transit conditions or timelines."
  }
];

export default function CommitmentSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Ensuring Integrity from Source to Patient
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            A Commitment to Quality, Safety, and Regulatory Excellence
          </p>
        </div>
        <div className="grid md:grid-cols-2 gap-8 lg:gap-10 mb-12">
          {commitments.map((commitment, index) => (
            <div key={index} className="bg-gray-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 flex items-start">
              <commitment.icon className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">{commitment.title}</h3>
                <p className="text-gray-600 text-sm">
                  {commitment.description}
                </p>
              </div>
            </div>
          ))}
        </div>
        <div className="text-center">
          <Link href="/quality-assurance" className="text-[#028475] hover:text-[#004235] font-medium text-lg inline-flex items-center">
            Learn More About Our Quality Assurance Protocols
            <svg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round' className='ml-2 h-5 w-5'><path d='M5 12h14'/><path d='m12 5 7 7-7 7'/></svg>
          </Link>
        </div>
      </div>
    </section>
  );
}