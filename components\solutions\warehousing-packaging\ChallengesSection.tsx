"use client"

import { CheckCircle } from "lucide-react"

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Struggling with Storage, Repackaging, or Final Prep?
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            For many industrial materials, the journey from production to end-user involves critical intermediate steps. Businesses often face difficulties with:
          </p>
          
          <div className="space-y-4">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <p className="text-gray-700">Finding reliable third-party providers for specialized packaging (e.g., bulk-to-bag, labeling, kitting).</p>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <p className="text-gray-700">Securing compliant and appropriate warehousing for temporary or strategic inventory holding.</p>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <p className="text-gray-700">Lack of visibility into inventory stored at third-party facilities.</p>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <p className="text-gray-700">Coordinating the movement of goods between suppliers, packagers, warehouses, and final carriers.</p>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <p className="text-gray-700">Ensuring quality control and adherence to specifications during repackaging or value-added services.</p>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <p className="text-gray-700">Managing costs and SLAs with multiple service providers.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}