import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export function CustomerCoordinationSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
              Customer-Facing Coordination
            </h2>
            <p className="mx-auto max-w-[700px] text-gray-600 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              Managed by StreamLnk
            </p>
          </div>
        </div>
        <div className="mx-auto max-w-4xl mt-12">
          <div className="bg-white rounded-xl p-6 shadow-md">
            <p className="text-gray-600 mb-8 text-lg">
              Buyers and other stakeholders accessing their MyStreamLnk or E-Stream portals will rely on the clearance
              updates you provide via StreamGlobe.
            </p>
            <div className="grid gap-6 md:grid-cols-3">
              {[
                {
                  icon: <Bell className="h-10 w-10 text-[#028475]" />,
                  title: "Automated Alerts",
                  description: "The system prompts you when updates are needed.",
                },
                {
                  icon: <Lock className="h-10 w-10 text-[#028475]" />,
                  title: "Secure Messaging",
                  description: "One-directional messaging tools for shipment-specific notes or queries.",
                },
                {
                  icon: <MessageSquare className="h-10 w-10 text-[#028475]" />,
                  title: "Focused Communication",
                  description: "Focus on clearance, not extensive multi-party communication.",
                },
              ].map((item, index) => (
                <Card key={index} className="border-none shadow-sm">
                  <CardContent className="p-4 flex flex-col items-center text-center">
                    <div className="mb-4">{item.icon}</div>
                    <h3 className="text-lg font-bold mb-2 text-[#004235]">{item.title}</h3>
                    <p className="text-gray-600 text-sm">{item.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
            <p className="text-gray-600 mt-8 text-lg">
              StreamLnk routes the necessary information to customers and other parties, allowing you to focus on the
              core task of clearance, not extensive multi-party communication.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
