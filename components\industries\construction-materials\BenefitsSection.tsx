"use client";

import { ArrowR<PERSON>, Users, TrendingUp, Truck, ShieldCheck, CheckSquare, Clock } from "lucide-react"; // Adjusted icons for construction benefits

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Keep Your Projects On Time, On Budget, and Built to Last
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Benefits for the Construction Industry
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[ 
              { title: "Optimized Material Sourcing", description: "Access a wider range of suppliers and achieve more competitive pricing.", icon: <Users className="h-8 w-8 text-[#028475] mb-3" /> },
              { title: "Improved Project Timeline", description: "Ensure timely delivery of materials with JIT coordination and real-time tracking.", icon: <Clock className="h-8 w-8 text-[#028475] mb-3" /> },
              { title: "Reduced Material Costs", description: "Benefit from transparent pricing, bulk RFQs, and optimized logistics.", icon: <TrendingUp className="h-8 w-8 text-[#028475] mb-3" /> },
              { title: "Enhanced Quality Control", description: "Source from vetted suppliers with verifiable material certifications.", icon: <ShieldCheck className="h-8 w-8 text-[#028475] mb-3" /> },
              { title: "Streamlined Procurement", description: "Digitize RFQs, order management, and document handling.", icon: <CheckSquare className="h-8 w-8 text-[#028475] mb-3" /> },
              { title: "Support Sustainable Building", description: "Easily find and procure green building materials to meet project and regulatory requirements.", icon: <ArrowRight className="h-8 w-8 text-[#028475] mb-3" /> }, // Using ArrowRight as a placeholder, consider a more specific icon like Leaf or Recycle if available and appropriate
              { title: "Better Site Inventory", description: "Improved visibility into material delivery schedules reduces on-site congestion and spoilage.", icon: <Truck className="h-8 w-8 text-[#028475] mb-3" /> }
            ].map((benefit, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-sm text-center">
                {benefit.icon}
                <h3 className="font-semibold text-[#004235] text-lg mb-2">{benefit.title}</h3>
                <p className="text-gray-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}