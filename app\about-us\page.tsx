'use client'

import Image from 'next/image'
import Link from 'next/link'
import { ArrowRight } from 'lucide-react'
import { CountrySelector } from '@/components/country-selector'
import { MainNav } from '@/components/main-nav'
import { MainFooter } from '@/components/main-footer'
import { Button } from '@/components/ui/button'
import { CertificationCarousel } from '@/components/about-us/certification-carousel'

export default function AboutUsPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <CountrySelector />
      <MainNav />

      <main>
        {/* Hero Section */}
        <section className="relative h-[600px] flex items-center justify-center text-white">
          {/* Background Image */}
          <Image
            src="/images/about-us/about-us-banner.webp"
            alt="About Us Banner"
            fill
            className="object-cover z-0"
            priority
          />
          {/* Overlay */}
          <div className="absolute inset-0 bg-black/50 z-10"></div>
          {/* Content */}
          <div className="container mx-auto px-4 z-20 relative text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Pioneering the Future of Global Industrial Trade
            </h1>
            <p className="text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed">
              We are building the world's first AI-powered, fully integrated B2B ecosystem for industrial supply chains, transforming how businesses source, sell, finance, and transport materials worldwide.
            </p>
          </div>
          <div className="absolute bottom-0 w-full h-1 bg-[#028475] z-30"></div>
        </section>

        {/* Our Story Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="mb-12">
              <div className="w-16 h-1 bg-[#028475] mb-4"></div>
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">Our Story: Born from a Vision for Smarter Trade</h2>
            </div>
            
            <div className="max-w-5xl space-y-6 text-lg leading-relaxed">
              <p className="text-gray-700">
                StreamLnk was founded on a simple yet powerful idea: the fragmented, opaque, and inefficient nature of traditional industrial trade was holding businesses back. Our founders, seasoned veterans from the supply chain, technology, and finance sectors, recognized a critical need for a unified digital platform.
              </p>
              
              <p className="text-gray-700">
                A platform that could not only connect disparate parties but also inject intelligence, transparency, and trust into every transaction. From this vision, StreamLnk was born – a commitment to digitizing the entire industrial value chain, unlocking unprecedented efficiencies and global opportunities for every participant.
              </p>
            </div>
          </div>
        </section>

        {/* Mission and Vision Section */}
        <section className="py-16 bg-[#F2F2F2]">
          <div className="container mx-auto px-4">
            <div className="mb-12">
              <div className="w-16 h-1 bg-[#028475] mb-4"></div>
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">Our Mission & Vision</h2>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Mission */}
              <div className="bg-white rounded-xl p-8 shadow-sm">
                <h3 className="text-2xl font-bold text-[#004235] mb-6">Mission</h3>
                <p className="text-gray-700 text-lg leading-relaxed">
                  To revolutionize global industrial trade by providing an intelligent, integrated, and trusted digital ecosystem that empowers businesses to operate with unparalleled efficiency, transparency, and resilience.
                </p>
              </div>
              
              {/* Vision */}
              <div className="bg-white rounded-xl p-8 shadow-sm">
                <h3 className="text-2xl font-bold text-[#004235] mb-6">Vision</h3>
                <p className="text-gray-700 text-lg leading-relaxed">
                  To be the indispensable platform that facilitates frictionless, secure, and sustainable industrial commerce worldwide, driving economic growth and fostering a truly connected global supply chain.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* What We Do Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="mb-12">
              <div className="w-16 h-1 bg-[#028475] mb-4"></div>
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">What We Do: The Power of an Integrated Ecosystem</h2>
              <p className="text-xl text-gray-700 max-w-4xl">
                StreamLnk is more than just a marketplace or a logistics tool. We are a comprehensive B2B ecosystem that unifies every critical function of the industrial materials supply chain:
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Intelligent Sourcing */}
              <div className="bg-[#F2F2F2] rounded-xl p-8">
                <div className="w-12 h-12 bg-[#028475] rounded-lg mb-6 flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-[#004235] mb-4">Intelligent Sourcing</h3>
                <p className="text-gray-700">
                  Connect with verified global suppliers and buyers, leveraging AI for smart matching, real-time quoting, and market price benchmarks (StreamIndex™).
                </p>
              </div>

              {/* End-to-End Logistics */}
              <div className="bg-[#F2F2F2] rounded-xl p-8">
                <div className="w-12 h-12 bg-[#028475] rounded-lg mb-6 flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-[#004235] mb-4">End-to-End Logistics</h3>
                <p className="text-gray-700">
                  Seamlessly coordinate multi-modal freight, customs clearance, warehousing, and packaging through an integrated network of vetted service providers.
                </p>
              </div>

              {/* Embedded Finance */}
              <div className="bg-[#F2F2F2] rounded-xl p-8">
                <div className="w-12 h-12 bg-[#028475] rounded-lg mb-6 flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-[#004235] mb-4">Embedded Finance</h3>
                <p className="text-gray-700">
                  Access secure multi-currency payments, B2B Buy Now, Pay Later (BNPL), Escrow services, and automated AR/AP management.
                </p>
              </div>

              {/* Risk & Compliance */}
              <div className="bg-[#F2F2F2] rounded-xl p-8">
                <div className="w-12 h-12 bg-[#028475] rounded-lg mb-6 flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-[#004235] mb-4">Robust Risk & Compliance</h3>
                <p className="text-gray-700">
                  Ensure trusted transactions with rigorous KYC/AML verification, automated document tracking, and our proprietary iScore™ partner rating system.
                </p>
              </div>

              {/* Data Insights */}
              <div className="bg-[#F2F2F2] rounded-xl p-8">
                <div className="w-12 h-12 bg-[#028475] rounded-lg mb-6 flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-[#004235] mb-4">Actionable Data Insights</h3>
                <p className="text-gray-700">
                  Transform transactional data into strategic intelligence with StreamResources+, providing predictive analytics, supply/demand forecasts, and in-depth market reports.
                </p>
              </div>

              {/* AI Integration */}
              <div className="bg-[#F2F2F2] rounded-xl p-8">
                <div className="w-12 h-12 bg-[#028475] rounded-lg mb-6 flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-[#004235] mb-4">AI-Powered Intelligence</h3>
                <p className="text-gray-700">
                  Every element of our platform works in harmony, powered by advanced AI and machine learning, to eliminate silos, reduce manual effort, and provide complete, real-time visibility.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-16 bg-[#F2F2F2]">
          <div className="container mx-auto px-4">
            <div className="mb-12">
              <div className="w-16 h-1 bg-[#028475] mb-4"></div>
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">Our Values: The Foundation of Our Success</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: "Transparency",
                  description: "Fostering clear, honest, and accessible information in all interactions and transactions."
                },
                {
                  title: "Trust",
                  description: "Building a secure and reliable ecosystem through rigorous verification, objective performance ratings (iScore™), and ethical practices."
                },
                {
                  title: "Innovation",
                  description: "Continuously leveraging AI and cutting-edge technology to solve complex industry challenges and drive future-forward solutions."
                },
                {
                  title: "Efficiency",
                  description: "Streamlining processes, reducing friction, and optimizing costs for all participants."
                },
                {
                  title: "Global Reach",
                  description: "Connecting businesses across continents, breaking down barriers to international trade."
                },
                {
                  title: "Sustainability",
                  description: "Championing responsible sourcing, promoting circular economy principles, and enabling ESG tracking for a greener future."
                },
                {
                  title: "Collaboration",
                  description: "Working hand-in-hand with our users, partners, and industry leaders to co-create value."
                }
              ].map((value, index) => (
                <div key={index} className="bg-white rounded-xl p-6 shadow-sm">
                  <h3 className="text-xl font-bold text-[#004235] mb-4">{value.title}</h3>
                  <p className="text-gray-700">{value.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="mb-12">
              <div className="w-16 h-1 bg-[#028475] mb-4"></div>
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">Our Team: Experts Driving Innovation</h2>
            </div>
            
            <div className="max-w-5xl space-y-6 text-lg leading-relaxed">
              <p className="text-gray-700">
                StreamLnk is powered by a diverse team of passionate experts in industrial trade, supply chain management, artificial intelligence, software development, finance, and logistics. Our collective experience and dedication enable us to build a platform that truly understands and addresses the complex needs of the global industrial economy.
              </p>
              
              <p className="text-gray-700">
                We are committed to continuous learning, relentless improvement, and supporting our users every step of the way.
              </p>
            </div>
          </div>
        </section>

        {/* Commitment Section */}
        <section className="py-16 bg-[#F2F2F2]">
          <div className="container mx-auto px-4">
            <div className="mb-12">
              <div className="w-16 h-1 bg-[#028475] mb-4"></div>
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">Our Commitment: Empowering a Better Way to Trade</h2>
            </div>
            
            <div className="max-w-5xl space-y-6 text-lg leading-relaxed">
              <p className="text-gray-700">
                We believe that by building a more intelligent, transparent, and connected digital ecosystem, we can create immense value – not just for individual businesses, but for the global economy at large.
              </p>
              
              <p className="text-gray-700">
                StreamLnk is committed to driving sustainable growth, fostering reliable partnerships, and ensuring compliant trade for all.
              </p>
            </div>
          </div>
        </section>

        {/* Certifications Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="mb-12">
              <div className="w-16 h-1 bg-[#028475] mb-4"></div>
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">Certifications & Accreditations</h2>
              <p className="text-lg text-gray-600 max-w-3xl">
                Our commitment to excellence is validated through industry-leading certifications and compliance standards, 
                ensuring the highest levels of security, quality, and reliability in all our operations.
              </p>
            </div>
            
            <CertificationCarousel 
              certifications={[
                {
                  src: "/images/about-us/aeo-certification-web-01 1.png",
                  alt: "AEO Certification - Authorized Economic Operator"
                },
                {
                  src: "/images/about-us/ISO_27001 1.png",
                  alt: "ISO 27001 Information Security Management"
                },
                {
                  src: "/images/about-us/logo-pci-dss-500 1.png",
                  alt: "PCI DSS Payment Card Industry Data Security Standard"
                },
                {
                  src: "/images/about-us/PNG_GDPR-e1672263252689 1.png",
                  alt: "GDPR General Data Protection Regulation Compliance"
                },
                {
                  src: "/images/about-us/Screenshot 2025-04-24 at 3.14.29 PM-Photoroom 1.png",
                  alt: "Industry Certification"
                },
                {
                  src: "/images/about-us/what-is-iso-9001-compliance 1.png",
                  alt: "ISO 9001 Quality Management System"
                },
                {
                  src: "/images/about-us/2.png",
                  alt: "Professional Certification"
                },
                {
                  src: "/images/about-us/3.png",
                  alt: "Industry Standard Compliance"
                },
                {
                  src: "/images/about-us/4.png",
                  alt: "Quality Assurance Certification"
                },
                {
                  src: "/images/about-us/5.png",
                  alt: "Security Standard Certification"
                },
                {
                  src: "/images/about-us/6.png",
                  alt: "Compliance Certification"
                },
                {
                  src: "/images/about-us/7.png",
                  alt: "Professional Standards Certification"
                },
                {
                  src: "/images/about-us/8.png",
                  alt: "Industry Excellence Award"
                },
                {
                  src: "/images/about-us/9.png",
                  alt: "Quality Management Certification"
                },
                {
                  src: "/images/about-us/10.png",
                  alt: "Security Compliance Standard"
                },
                {
                  src: "/images/about-us/12.png",
                  alt: "Professional Accreditation"
                },
                {
                  src: "/images/about-us/13.png",
                  alt: "Industry Recognition Award"
                },
                {
                  src: "/images/about-us/15.png",
                  alt: "Excellence in Service Certification"
                }
              ]}
              autoPlay={true}
              autoPlayInterval={4000}
            />
          </div>
        </section>

        {/* Join Our Journey CTA Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
                Join Our Journey
              </h2>
              <p className="text-xl text-gray-700 mb-8">
                Whether you're looking to optimize your supply chain, expand your global reach, or contribute to a more efficient and sustainable future for industrial trade, we invite you to connect with us.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
                  size="lg"
                  asChild
                >
                  <Link href="/request-demo">
                    REQUEST DEMO
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                
                <Button 
                  variant="outline"
                  className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
                  size="lg"
                  asChild
                >
                  <Link href="/careers">
                    EXPLORE CAREERS
                  </Link>
                </Button>
              </div>

              <div className="mt-8 flex flex-col sm:flex-row gap-4 sm:gap-8 justify-center">
                <Button 
                  variant="link"
                  className="text-[#028475] hover:text-[#004235]"
                  asChild
                >
                  <Link href="/partners">
                    BECOME PARTNER
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                
                <Button 
                  variant="link"
                  className="text-[#028475] hover:text-[#004235]"
                  asChild
                >
                  <Link href="/contact-us">
                    CONTACT US
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      <MainFooter />
    </div>
  )
}