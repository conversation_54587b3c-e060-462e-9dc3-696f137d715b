"use client";

import { <PERSON><PERSON><PERSON><PERSON>, TrendingUp, <PERSON>Che<PERSON> } from 'lucide-react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';

export default function ConclusionSection() {
  return (
    <section className="py-12 md:py-20 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            The StreamLnk Advantage: Your Partner in Supply Chain Excellence
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center leading-relaxed">
            Optimizing your supply chain is not just about cost reduction; it's about building resilience, fostering innovation, and gaining a significant competitive edge. StreamLnk provides the technology, expertise, and integrated platform to transform your supply chain into a strategic asset.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <TrendingUp className="h-12 w-12 text-[#028475] mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-[#004235] mb-2">Enhanced Efficiency</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Streamline operations, reduce lead times, and minimize waste with our AI-powered tools and integrated workflows.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <ShieldCheck className="h-12 w-12 text-[#028475] mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-[#004235] mb-2">Increased Resilience</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Mitigate risks with iScore™, diversify your supplier base, and gain real-time visibility into your supply network.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <CheckCircle className="h-12 w-12 text-[#028475] mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-[#004235] mb-2">Strategic Growth</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Leverage data-driven insights from StreamResources+ to make informed decisions and unlock new market opportunities.
              </p>
            </div>
          </div>

          <p className="text-xl text-gray-800 font-medium mb-8 text-center leading-relaxed">
            Partner with StreamLnk to navigate the complexities of modern supply chains and achieve sustainable success.
          </p>
          
          <div className="text-center">
            <Button className="bg-[#004235] hover:bg-[#028475] text-white px-8 py-3 text-lg" asChild>
              <Link href="/contact-us">
                Optimize Your Supply Chain
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}