import * as React from "react"

const MO<PERSON>LE_BREAKPOINT = 768

export function useIsMobile() {
  // Initialize with false to avoid hydration mismatch
  const [isMobile, setIsMobile] = React.useState(false)
  
  // Use useEffect to update the state only on the client side
  React.useEffect(() => {
    // Now we're on the client side, it's safe to check window
    const checkMobile = () => window.innerWidth < MOBILE_BREAKPOINT
    
    // Set the initial value
    setIsMobile(checkMobile())
    
    // Set up the event listener
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(checkMobile())
    }
    
    // Modern API for event listener
    mql.addEventListener("change", onChange)
    
    // Clean up
    return () => mql.removeEventListener("change", onChange)
  }, []) // Empty dependency array means this runs once on mount

  return isMobile
}
