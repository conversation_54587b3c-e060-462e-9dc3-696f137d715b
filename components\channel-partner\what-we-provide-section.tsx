import { BookOpen, Presentation, Users, Bar<PERSON>hart<PERSON>ig, Award, Shield<PERSON>he<PERSON>, Settings } from "lucide-react";

const provisions = [
  {
    icon: <BookOpen className="h-8 w-8 text-[#028475]" />,
    title: "Comprehensive Platform Training",
    description: "In-depth understanding of all StreamLnk portals and features.",
  },
  {
    icon: <Settings className="h-8 w-8 text-[#028475]" />,
    title: "MyStreamLnk+ Agent Portal",
    description: "Your dedicated command center for managing clients, quotes, orders, and earnings.",
  },
  {
    icon: <Presentation className="h-8 w-8 text-[#028475]" />,
    title: "Marketing & Sales Collateral",
    description: "Professionally designed brochures, presentations, case studies, and co-brandable assets.",
  },
  {
    icon: <Users className="h-8 w-8 text-[#028475]" />,
    title: "Lead Generation Support (for select Distributors)",
    description: "Potential for qualified leads from StreamLnk's central marketing efforts in your region.",
  },
  {
    icon: <BarChartBig className="h-8 w-8 text-[#028475]" />,
    title: "Ongoing Product Updates & Support",
    description: "Access to new features, regular updates, and dedicated partner support channels.",
  },
  {
    icon: <Award className="h-8 w-8 text-[#028475]" />,
    title: "Competitive Commission Structure",
    description: "Transparent and rewarding compensation plans.",
  },
  {
    icon: <ShieldCheck className="h-8 w-8 text-[#028475]" />,
    title: "StreamLnk Tier & Rewards Program",
    description: "Additional incentives and recognition for top-performing agents and distributors.",
  },
];

export default function WhatWeProvideSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Equipping You for Success in Your Market
          </h2>
          <p className="text-xl font-semibold text-[#028475]">
            What We Provide Our Channel Partners
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {provisions.map((provision, index) => (
            <div key={index} className="bg-[#f3f4f6] p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow flex flex-col items-center text-center">
              <div className="mb-4 bg-[#004235]/10 rounded-full p-3 inline-block">
                {provision.icon}
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">{provision.title}</h3>
              <p className="text-gray-600 text-sm">{provision.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}