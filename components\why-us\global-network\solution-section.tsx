import { Globe, Users, Truck, Anchor, Building, Package, BarChartBig, ShieldCheck } from "lucide-react";

interface NetworkComponentCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  platform?: string;
}

const NetworkComponentCard: React.FC<NetworkComponentCardProps> = ({ icon, title, description, platform }) => (
  <div className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col">
    <div className="flex items-center mb-4">
      <div className="rounded-full bg-[#028475]/10 w-12 h-12 flex items-center justify-center mr-4 flex-shrink-0">
        {icon}
      </div>
      <div>
        <h3 className="text-xl font-semibold text-[#004235]">{title}</h3>
        {platform && <p className="text-xs text-[#028475] font-medium">({platform})</p>}
      </div>
    </div>
    <p className="text-sm text-gray-600 flex-grow">{description}</p>
  </div>
);

export default function SolutionSection() {
  const networkComponents = [
    {
      icon: <Users className="h-6 w-6 text-[#028475]" />,
      title: "Verified Global Suppliers",
      platform: "E-Stream",
      description: "Access producers, manufacturers, and distributors of polymers, chemicals, energy products, and more from key industrial hubs and emerging markets across North America, Europe, Asia-Pacific, MENA, and LATAM. All suppliers undergo rigorous KYC/AML and compliance verification."
    },
    {
      icon: <Users className="h-6 w-6 text-[#028475]" />,
      title: "Qualified International Buyers",
      platform: "MyStreamLnk",
      description: "Connect with a growing base of manufacturers and procurement teams actively sourcing materials for diverse industrial applications worldwide."
    },
    {
      icon: <Users className="h-6 w-6 text-[#028475]" />,
      title: "Expansive Agent & Distributor Network",
      platform: "MyStreamLnk+",
      description: "Leverage local market expertise and customer relationships through our network of independent sales agents and official regional distributors in numerous countries."
    },
    {
      icon: <Truck className="h-6 w-6 text-[#028475]" />,
      title: "Multi-Modal Logistics Providers",
      platform: "StreamFreight & StreamGlobe",
      description: "Integrated access to a wide array of vetted land freight carriers (trucking, rail) and major global ocean freight carriers, covering key international trade lanes."
    },
    {
      icon: <Anchor className="h-6 w-6 text-[#028475]" />,
      title: "Licensed Customs Agents",
      platform: "StreamGlobe+",
      description: "Connect with specialized customs clearance agents in ports and border crossings worldwide, ensuring compliant and efficient import/export processes."
    },
    {
      icon: <Package className="h-6 w-6 text-[#028475]" />,
      title: "Packaging & Warehousing Partners",
      platform: "StreamPak",
      description: "Access certified partners for specialized packaging, labeling, and secure warehousing in strategic locations globally."
    },
    {
      icon: <BarChartBig className="h-6 w-6 text-[#028475]" />,
      title: "Data & Technology Partners",
      platform: "StreamResources+",
      description: "Collaborations with leading providers of market intelligence, risk analytics, and financial technology to enrich our ecosystem."
    }
  ];

  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Global Reach, Local Expertise: The Power of the StreamLnk Network
          </h2>
          <p className="text-lg text-gray-700 mb-2">
            StreamLnk's Solution: A Connected, Vetted, Worldwide Ecosystem
          </p>
          <p className="text-gray-600">
            StreamLnk is meticulously building and curating a comprehensive global network that brings together all essential stakeholders for industrial trade onto a single, integrated platform:
          </p>
        </div>
        <div className="max-w-5xl mx-auto mb-12">
          <h3 className="text-2xl font-semibold text-[#004235] mb-8 text-center md:text-left">Key Components of Our Global Network:</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8">
            {networkComponents.map((component, index) => (
              <NetworkComponentCard 
                key={index} 
                icon={component.icon} 
                title={component.title} 
                platform={component.platform}
                description={component.description} 
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}