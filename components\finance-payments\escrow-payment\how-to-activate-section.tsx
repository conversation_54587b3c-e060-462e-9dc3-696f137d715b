import { StandardizedTimeline } from "@/components/ui/standardized-timeline";
import { DollarSign, CheckCircle, Banknote, Bell, Handshake } from "lucide-react";

export default function HowToActivateSection() {
  const steps = [
    {
      number: 1,
      icon: <DollarSign className="h-8 w-8 text-white" />,
      title: "Choose Escrow/Milestone Payment",
      description: "Select this option during quote or order confirmation"
    },
    {
      number: 2,
      icon: <CheckCircle className="h-8 w-8 text-white" />,
      title: "Define Key Milestones",
      description: "Specify milestones and responsible parties"
    },
    {
      number: 3,
      icon: <Banknote className="h-8 w-8 text-white" />,
      title: "Deposit Funds",
      description: "Via wire, ACH, or StreamLnk-approved partners"
    },
    {
      number: 4,
      icon: <Bell className="h-8 w-8 text-white" />,
      title: "Track Payment Status",
      description: "Monitor release status and notifications"
    },
    {
      number: 5,
      icon: <Handshake className="h-8 w-8 text-white" />,
      title: "Funds Released",
      description: "Automatically or with mutual confirmation"
    }
  ];

  return (
    <StandardizedTimeline
      title="How to Activate"
      description="Getting started with StreamLnk's escrow and milestone payment system is simple."
      steps={steps}
      bgColor="bg-[#f3f4f6]"
    />
  );
}