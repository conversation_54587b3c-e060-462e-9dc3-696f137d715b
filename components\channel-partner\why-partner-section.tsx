import { Briefcase, Zap, Users, TrendingUp, BadgeDollarSign, Handshake, Network } from "lucide-react";

const benefits = [
  {
    icon: <Briefcase className="h-7 w-7 text-[#028475]" />,
    title: "Offer a Unique, High-Value Solution",
    description: "Provide your clients with access to a comprehensive, AI-powered platform that solves critical supply chain pain points.",
  },
  {
    icon: <BadgeDollarSign className="h-7 w-7 text-[#028475]" />,
    title: "Build a Recurring Revenue Stream",
    description: "Earn attractive commissions on transactions facilitated for your onboarded customers.",
  },
  {
    icon: <Users className="h-7 w-7 text-[#028475]" />,
    title: "Leverage Your Existing Network",
    description: "Introduce StreamLnk to your established portfolio of industrial buyers and suppliers.",
  },
  {
    icon: <TrendingUp className="h-7 w-7 text-[#028475]" />,
    title: "Expand Your Service Offering",
    description: "Add a cutting-edge digital trade solution to your existing consultancy or distribution services.",
  },
  {
    icon: <Zap className="h-7 w-7 text-[#028475]" />,
    title: "No Inventory, No Upfront Product Cost",
    description: "Focus on client relationships and sales; StreamLnk provides the platform and ecosystem.",
  },
  {
    icon: <Handshake className="h-7 w-7 text-[#028475]" />,
    title: "Receive Training & Support",
    description: "Get comprehensive onboarding, marketing materials, and ongoing support from the StreamLnk team.",
  },
  {
    icon: <Network className="h-7 w-7 text-[#028475]" />,
    title: "Be Part of a Growing Global Network",
    description: "Align with an innovative leader in industrial trade technology.",
  },
];

export default function WhyPartnerSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Why Become a StreamLnk Agent or Distributor?
          </h2>
          <p className="text-xl font-semibold text-[#028475] mb-6">
            The Opportunity: Representing a Leading-Edge B2B Platform
          </p>
          <p className="text-lg text-gray-700">
            The industrial world is rapidly digitizing, and businesses are seeking efficient, transparent solutions for global sourcing and logistics. As a StreamLnk Channel Partner, you can:
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {benefits.map((benefit, index) => (
            <div key={index} className="bg-[#f3f4f6] p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-center mb-4 bg-[#004235]/10 rounded-full w-14 h-14 mx-auto">
                {benefit.icon}
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2 text-center">{benefit.title}</h3>
              <p className="text-gray-600 text-center text-sm">{benefit.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}