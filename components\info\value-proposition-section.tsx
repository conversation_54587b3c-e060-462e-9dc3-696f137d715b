import { Zap, Eye, DollarSign, Shield, Globe, BarChart, Scale } from "lucide-react";

const valuePropositions = [
  { title: "Efficiency", description: "Streamline complex processes, reduce manual work.", icon: Zap },
  { title: "Transparency", description: "Clear visibility into pricing, logistics, and partner performance.", icon: Eye },
  { title: "Cost Reduction", description: "Optimize sourcing, reduce logistics spend, minimize errors.", icon: DollarSign },
  { title: "Risk Mitigation", description: "Transact securely with vetted partners and robust compliance tools.", icon: Shield },
  { title: "Global Reach", description: "Access international markets and a worldwide network.", icon: Globe },
  { title: "Data-Driven Decisions", description: "Leverage unique market intelligence for strategic advantage.", icon: BarChart },
  { title: "Scalability", description: "A platform designed to grow with your business.", icon: Scale }
];

export default function ValuePropositionSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">The StreamLnk Value Proposition</h2>
          <p className="text-xl text-[#028475] font-semibold mb-6">
            Transforming Trade, Delivering Value
          </p>
          <p className="text-lg text-gray-700 leading-relaxed">
            Discover the core benefits StreamLnk offers to enhance your business operations and drive growth in the global marketplace.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
          {valuePropositions.map(item => (
            <div key={item.title} className="bg-[#f3f4f6] p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center text-center">
              <item.icon className="h-12 w-12 text-[#028475] mb-4" />
              <h3 className="text-xl font-semibold text-[#004235] mb-2">{item.title}</h3>
              <p className="text-gray-600 text-sm leading-relaxed">{item.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}