import { UserPlus, LineChart, Truck, MessageSquare, ShoppingBag, Gavel, CreditCard } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export function PortalFeaturesSection() {
  const features = [
    {
      icon: <UserPlus className="h-10 w-10 text-[#028475]" />,
      title: "Client Onboarding & Management",
      description:
        "Register and onboard new verified industrial buyers, and manage their profiles, preferences (Incoterms, product interests), and essential documents.",
    },
    {
      icon: <LineChart className="h-10 w-10 text-[#028475]" />,
      title: "Commission & Performance Tracking",
      description:
        "View detailed commission reports, track your sales volume, and monitor your overall performance against targets.",
    },
    {
      icon: <Truck className="h-10 w-10 text-[#028475]" />,
      title: "Order & Shipment Monitoring",
      description:
        "Keep a close eye on every stage of your clients' orders: from quote request (RFQ) → order confirmation → shipment → customs clearance → final delivery.",
    },
    {
      icon: <MessageSquare className="h-10 w-10 text-[#028475]" />,
      title: "Client Interaction Tools",
      description:
        "Add notes to client accounts, log specific client requests, and flag urgent RFQs for priority attention.",
    },
    {
      icon: <ShoppingBag className="h-10 w-10 text-[#028475]" />,
      title: "Supplier Catalog Access",
      description:
        "Browse StreamLnk's extensive supplier catalog, filtered by region, brand, product type, and availability, to assist your clients.",
    },
    {
      icon: <Gavel className="h-10 w-10 text-[#028475]" />,
      title: "Promote Auctions",
      description:
        "Introduce and recommend relevant StreamLnk+ Auctions to your buyers, unlocking potential savings and new sourcing opportunities for them.",
    },
    {
      icon: <CreditCard className="h-10 w-10 text-[#028475]" />,
      title: "AR Status Tracking",
      description: "Monitor the accounts receivable status for your clients' transactions.",
    },
  ]

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            What You Can Do in the MyStreamLnk+ Portal
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            Your MyStreamLnk+ portal is your central hub for all agent activities:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="border border-gray-200 hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">{feature.title}</h3>
                <p className="text-gray-700">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
