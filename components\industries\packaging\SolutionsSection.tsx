"use client";

import { Globe, Search, Package, Truck, FileText, BarChart3 } from "lucide-react";

export default function SolutionsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">
            Your End-to-End Digital Platform for the Entire Packaging Value Chain
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk's Tailored Solutions for the Packaging Industry
          </p>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk supports converters, packaging manufacturers, and brands by providing specialized tools for sourcing raw materials, managing logistics for finished packaging, and promoting sustainable solutions:
          </p>

          <div className="space-y-8">
            {/* E-Stream for Packaging Material Suppliers */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Globe className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">E-Stream for Packaging Material Suppliers</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>List raw materials like polymer resins (PET, PE, PP, PS), masterbatches, additives, films, adhesives, paperboard, and sustainable alternatives (bio-plastics, compostable materials).</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Showcase certifications (e.g., FDA, BRC, FSC, recycled content verification).</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Use StreamIndex™ for pricing insights on packaging resins and feedstocks.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* MyStreamLnk for Packaging Converters & Brands */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Search className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">MyStreamLnk for Packaging Converters & Brands (Buyers)</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Source a wide range of packaging raw materials and finished packaging components.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Filter by material type, recycled content percentage, compostability, food-grade certifications, and supplier iScore™.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Request quotes for bulk or custom orders with integrated logistics.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Track the ESG footprint of sourced packaging materials.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* StreamPak for Value-Added Packaging Services */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Package className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">StreamPak for Value-Added Packaging Services</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Connect with partners for specialized services like custom printing, labeling, kitting, or converting bulk materials into consumer-ready packaging formats.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Integrated Logistics */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Truck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Integrated Logistics (StreamFreight, StreamGlobe+)</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Coordinate efficient and cost-effective shipping for both raw materials and finished packaging goods, domestically and internationally.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Real-time tracking to support JIT manufacturing schedules.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Compliance & Documentation Management */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <FileText className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Compliance & Documentation Management</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Centralized access to material safety data sheets (MSDS), certificates of analysis (CoA), food contact compliance documents, and recycled content certifications.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* StreamResources+ for Packaging Market Intelligence */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">StreamResources+ for Packaging Market Intelligence</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Insights into pricing trends for key packaging polymers.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Analysis of demand for sustainable packaging solutions by region and application.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Forecasting for recycled material availability and pricing.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}