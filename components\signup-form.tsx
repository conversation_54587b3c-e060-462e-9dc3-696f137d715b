"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { signUpWithEmail } from "@/app/actions/auth-actions"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { CheckCircle, AlertCircle, AlertTriangle, Eye, EyeOff, ChevronDown } from "lucide-react"
import Link from "next/link"
import { useSearchParams, useRouter } from "next/navigation"
import { isSupabaseConfigured } from "@/lib/supabase"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Portal options from customer-portal-logins component
const portalOptions = [
  { value: "mystreamlnk", label: "MyStreamLnk" },
  { value: "mystreamlnk-plus", label: "MyStreamLnk+" },
  { value: "e-stream", label: "E-Stream" },
  { value: "streampak", label: "StreamPak" },
  { value: "streamglobe", label: "StreamGlobe" },
  { value: "streamglobe-plus", label: "StreamGlobe+" },
  { value: "streamfreight", label: "StreamFreight" },
  { value: "streamresources-plus", label: "StreamResources+" },
]

export function SignUpForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const portalParam = searchParams.get("portal")
  
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [portal, setPortal] = useState(portalParam || "")
  const [showPassword, setShowPassword] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [result, setResult] = useState<{ success?: boolean; message?: string; error?: string; emailProviderUrl?: string } | null>(null)
  const [isConfigured, setIsConfigured] = useState(true)

  useEffect(() => {
    // Check if Supabase is properly configured
    setIsConfigured(isSupabaseConfigured())
  }, [])

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault()

    if (!isConfigured) {
      setResult({
        error: "Authentication service is not properly configured. Please contact support.",
      })
      return
    }

    setIsSubmitting(true)
    setResult(null)

    const formData = new FormData()
    formData.append("email", email)
    formData.append("password", password)
    formData.append("portal", portal)

    const response = await signUpWithEmail(formData)
    setResult(response)
    setIsSubmitting(false)

    if (response.success) {
      // Clear the form on success
      setEmail("")
      setPassword("")
      setPortal("")
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-2xl p-8">
      <h1 className="text-2xl font-bold text-[#004235] mb-6 text-center">Create Your Account</h1>

      {!isConfigured && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 flex items-start mb-6">
          <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" />
          <div>
            <h3 className="text-sm font-medium text-yellow-800">Configuration Required</h3>
            <p className="text-sm text-yellow-700 mt-1">
              The authentication service is not properly configured. This is a development environment issue.
            </p>
            <p className="text-sm text-yellow-700 mt-1">
              Please set the NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY environment variables.
            </p>
          </div>
        </div>
      )}

      {result?.success ? (
        <div className="space-y-6">
          <div className="bg-green-50 border border-green-200 rounded-md p-4 flex items-start">
            <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h3 className="text-sm font-medium text-green-800">Email Sent</h3>
              <p className="text-sm text-green-700 mt-1">{result.message}</p>
            </div>
          </div>
          <p className="text-sm text-gray-600">
            Please check your email inbox and click the link to complete your registration. The link will direct you to
            our secure registration page.
          </p>
          <p className="text-sm text-gray-600">
            Didn't receive an email?{" "}
            <button onClick={() => setResult(null)} className="text-[#07BC94] hover:underline">
              Try again
            </button>
          </p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-gray-600">
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full"
                placeholder="Enter your email"
                required
                disabled={isSubmitting || !isConfigured}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="text-gray-600">
                Password
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pr-10"
                  placeholder="Create a password"
                  required
                  disabled={isSubmitting || !isConfigured}
                  minLength={8}
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  onClick={() => setShowPassword(!showPassword)}
                  tabIndex={-1}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
              <p className="text-xs text-gray-500">Must be at least 8 characters</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="portal" className="text-gray-600">
                Portal
              </Label>
              <Select
                value={portal}
                onValueChange={(value) => {
                  setPortal(value)
                  // Update URL parameters when portal changes
                  const url = new URL(window.location.href)
                  url.searchParams.set("portal", value)
                  router.push(url.pathname + url.search)
                }}
                disabled={isSubmitting || !isConfigured}
                required
              >
                <SelectTrigger id="portal" className="w-full">
                  <SelectValue placeholder="Select a portal" />
                </SelectTrigger>
                <SelectContent>
                  {portalOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">Select the portal you want to access.</p>
              <p className="text-xs text-gray-500">
                Unsure which portal is right for you? 
                <Link href="/portals" className="text-[#028475] hover:text-[#004235] hover:underline">
                  Visit our portals page
                </Link> for more information.
              </p>
            </div>

            {result?.error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3 flex items-start mt-2">
                <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                <p className="text-sm text-red-700">{result.error}</p>
              </div>
            )}
          </div>

          <Button
            type="submit"
            className="w-full bg-gradient-to-r from-[#004235] to-[#07BC94] hover:opacity-90"
            disabled={isSubmitting || !isConfigured}
          >
            {isSubmitting ? "Sending..." : "Continue with Email"}
          </Button>

          <div className="text-center text-sm text-gray-600">
            <p>
              By signing up, you agree to our{" "}
              <Link href="/terms" className="text-[#07BC94] hover:underline">
                Terms of Service
              </Link>{" "}
              and{" "}
              <Link href="/privacy" className="text-[#07BC94] hover:underline">
                Privacy Policy
              </Link>
            </p>
          </div>

          <div className="border-t border-gray-200 pt-4 text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{" "}
              <Link href="/login" className="text-[#07BC94] hover:underline">
                Sign in
              </Link>
            </p>
          </div>
        </form>
      )}
    </div>
  )
}

