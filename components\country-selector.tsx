"use client"

import { Globe } from "lucide-react"

export function CountrySelector() {
  return (
    <div className="bg-[#1C1C1C] text-white">
      <div className="container mx-auto px-4">
        <div className="flex justify-center items-center h-10 text-sm gap-4">
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            <span>You are in</span>
            <span>America</span>
          </div>
          <button className="bg-[#00B881] hover:bg-[#00B881] text-white px-4 py-1 rounded text-sm">
            Stay on this site
          </button>
          <span>or</span>
          <button className="border border-[#00B881] text-[#00B881] px-4 py-1 rounded text-sm">Change Country</button>
        </div>
      </div>
    </div>
  )
}

