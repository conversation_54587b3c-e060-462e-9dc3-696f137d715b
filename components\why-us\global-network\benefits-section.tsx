import { ShoppingCart, TrendingUp, Globe, ShieldCheck, Users, Scaling, CheckCircle, Lightbulb } from "lucide-react";

interface BenefitItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const BenefitItem: React.FC<BenefitItemProps> = ({ icon, title, description }) => (
  <div className="flex flex-col items-center text-center p-6 bg-gray-50 rounded-lg shadow-sm hover:shadow-md transition-shadow">
    <div className="rounded-full bg-[#028475]/10 w-16 h-16 flex items-center justify-center mb-4">
      {icon}
    </div>
    <h3 className="text-xl font-semibold text-[#004235] mb-2">{title}</h3>
    <p className="text-gray-600 text-sm">{description}</p>
  </div>
);

export default function BenefitsSection() {
  const buyerBenefits = [
    {
      icon: <ShoppingCart className="h-7 w-7 text-[#028475]" />,
      title: "Wider Sourcing Options",
      description: "Discover new international suppliers and materials, enhancing choice and price competitiveness."
    },
    {
      icon: <Globe className="h-7 w-7 text-[#028475]" />,
      title: "Supply Chain Diversification",
      description: "Reduce reliance on single regions or suppliers, building resilience."
    }
  ];

  const supplierBenefits = [
    {
      icon: <TrendingUp className="h-7 w-7 text-[#028475]" />,
      title: "Access New Global Markets",
      description: "Reach qualified B2B buyers in countries previously inaccessible without significant investment."
    },
    {
      icon: <Users className="h-7 w-7 text-[#028475]" />,
      title: "Increased Sales Opportunities",
      description: "Expand your customer base and grow your export business."
    }
  ];

  const allUserBenefits = [
    {
      icon: <CheckCircle className="h-7 w-7 text-[#028475]" />,
      title: "Simplified Global Trade",
      description: "Navigate international logistics, customs, and payments more easily through a unified platform with vetted partners."
    },
    {
      icon: <ShieldCheck className="h-7 w-7 text-[#028475]" />,
      title: "Reduced Counterparty Risk",
      description: "Engage with a network where participants are verified and their performance is tracked (iScore™)."
    },
    {
      icon: <Lightbulb className="h-7 w-7 text-[#028475]" />,
      title: "Access to Local Expertise",
      description: "Benefit from the knowledge of regional agents, customs brokers, and logistics providers within the StreamLnk network."
    },
    {
      icon: <Scaling className="h-7 w-7 text-[#028475]" />,
      title: "Scalability",
      description: "Our network is designed to support your growth, whether you're entering your first international market or managing a complex global supply chain."
    }
  ];

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Expand Your Horizons, Optimize Your Operations, Mitigate Your Risks
          </h2>
          <p className="text-lg text-gray-700">
            Benefits of Connecting Through StreamLnk's Global Network
          </p>
        </div>

        <div className="mb-16">
          <h3 className="text-2xl font-semibold text-[#004235] mb-8 text-center">For Buyers:</h3>
          <div className="grid md:grid-cols-2 gap-8 max-w-3xl mx-auto">
            {buyerBenefits.map((item, index) => (
              <BenefitItem key={index} icon={item.icon} title={item.title} description={item.description} />
            ))}
          </div>
        </div>

        <div className="mb-16">
          <h3 className="text-2xl font-semibold text-[#004235] mb-8 text-center">For Suppliers:</h3>
          <div className="grid md:grid-cols-2 gap-8 max-w-3xl mx-auto">
            {supplierBenefits.map((item, index) => (
              <BenefitItem key={index} icon={item.icon} title={item.title} description={item.description} />
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-2xl font-semibold text-[#004235] mb-8 text-center">For All Users:</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {allUserBenefits.map((item, index) => (
              <BenefitItem key={index} icon={item.icon} title={item.title} description={item.description} />
            ))}
          </div>
        </div>

      </div>
    </section>
  );
}