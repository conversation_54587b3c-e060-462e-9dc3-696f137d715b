"use client";

import { CheckCircle, Award, Users, TrendingUp } from "lucide-react";

export default function ProgramRationaleSection() {
  const rationales = [
    {
      icon: <CheckCircle className="h-7 w-7 text-[#028475]" />,
      title: "Incentivize Positive Behaviors",
      description: "Encourage on-time performance, compliance, and engagement across the platform."
    },
    {
      icon: <Award className="h-7 w-7 text-[#028475]" />,
      title: "Recognize & Reward Top Performers",
      description: "Provide tangible, escalating benefits to our most valuable and active partners."
    },
    {
      icon: <Users className="h-7 w-7 text-[#028475]" />,
      title: "Foster Loyalty & Partnership",
      description: "Build stronger, more collaborative, and mutually beneficial long-term relationships."
    },
    {
      icon: <TrendingUp className="h-7 w-7 text-[#028475]" />,
      title: "Drive Continuous Improvement",
      description: "Motivate enhanced operational excellence and innovation within the StreamLnk network."
    }
  ];

  return (
    <section id="program-details" className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Why a Tier & Rewards Program?
          </h2>
          <p className="text-lg text-gray-700 leading-relaxed">
            This program is a cornerstone of our commitment to building a high-performing, trusted, and collaborative global trade network. It's meticulously designed to:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {rationales.map((rationale, index) => (
            <div key={index} className="bg-[#F2F2F2] p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex items-start space-x-4">
              <div className="flex-shrink-0 mt-1">
                {rationale.icon}
              </div>
              <div>
                <h3 className="font-semibold text-[#004235] text-xl mb-2">{rationale.title}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">{rationale.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}