import { Check } from "lucide-react"

export function PaymentModelsSection() {
  const paymentModels = [
    "Standard Net-Term Invoicing",
    "Milestone-Based Payouts for longer hauls or complex jobs",
    "StreamLnk Escrow Service for secure transaction management",
    "Integration with Buyer-Side BNPL (Buy Now, Pay Later) options for reliable job funding",
  ]

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-8 text-center">Flexible Payment Models</h2>
        <p className="text-lg text-gray-700 max-w-4xl mx-auto mb-8 text-center">
          We offer various payment models to suit your business needs:
        </p>

        <div className="max-w-2xl mx-auto bg-[#F2F2F2] rounded-lg p-8">
          <ul className="space-y-4">
            {paymentModels.map((model, index) => (
              <li key={index} className="flex items-start">
                <Check className="h-6 w-6 text-[#028475] mr-3 flex-shrink-0 mt-0.5" />
                <span className="text-gray-700">{model}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  )
}
