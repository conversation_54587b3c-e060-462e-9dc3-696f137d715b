import { Card, CardContent } from "@/components/ui/card"

export function ValuePropositionSection() {
  return (
    <section className="py-16 bg-[#F2F2F2] text-[#004235]">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold mb-8 text-center">
          Why It Matters: Your Role in the Global Economy
        </h2>
        <p className="text-lg max-w-4xl mx-auto mb-12 text-center">
          The materials you transport are the lifeblood of the global economy. StreamFreight ensures your vital services
          are visible to a wide network of clients, valued appropriately through competitive bidding, and protected by a
          transparent, compliant, and efficient platform.
        </p>

        <div className="grid md:grid-cols-3 gap-6">
          <Card className="bg-white border-none text-[#004235] shadow-md">
            <CardContent className="p-6 text-center">
              <h3 className="text-xl font-semibold mb-4">Bid Competitively and Win More Business</h3>
              <p>
                Access a steady stream of verified shipment opportunities that match your capabilities and location.
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white border-none text-[#004235] shadow-md">
            <CardContent className="p-6 text-center">
              <h3 className="text-xl font-semibold mb-4">Stay in Full Control of Your Operations and Earnings</h3>
              <p>Manage your schedule, track your performance, and monitor your payments all in one place.</p>
            </CardContent>
          </Card>

          <Card className="bg-white border-none text-[#004235] shadow-md">
            <CardContent className="p-6 text-center">
              <h3 className="text-xl font-semibold mb-4">Build a Lasting, Mutually Beneficial Partnership</h3>
              <p>Join a network that values reliability, quality service, and long-term relationships.</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
