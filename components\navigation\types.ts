// Navigation component type definitions

export interface NavItemBase {
  href: string;
  label: string;
  icon?: React.ReactElement;
  description?: string;
}

export interface MegaMenuSectionData {
  title: string;
  items: NavItemBase[];
}

export interface MegaMenuBottomGridSection {
  title: string;
  links: NavItemBase[];
}

export interface MegaMenuFooterData {
  text: string;
  link: string;
  linkText: string;
}

// Keeping column structure for potential future use, but the current data uses 'custom'
export interface MegaMenuColumnStructure {
  type: 'columns';
  maxWidthClass: string;
  columns: MegaMenuSectionData[];
  footer?: MegaMenuFooterData;
}

export interface MegaMenuCustomStructure {
  type: 'custom';
  title: string; // Used for the main title in the left sidebar
  description: string; // Used for the description in the left sidebar
  sidebarLinks: NavItemBase[]; // Used for 'Quick Links' in DHL style
  mainColumns: MegaMenuSectionData[]; // Used for the main content columns
  bottomGrid?: { // Optional bottom grid area
    title: string;
    description: string;
    sections: MegaMenuBottomGridSection[];
  };
  footer?: MegaMenuFooterData; // Optional footer area
  maxWidthClass: string; // Used for overall max width
}

export interface MegaMenuNavItem {
  id: string;
  trigger: string;
  content: MegaMenuColumnStructure | MegaMenuCustomStructure;
}