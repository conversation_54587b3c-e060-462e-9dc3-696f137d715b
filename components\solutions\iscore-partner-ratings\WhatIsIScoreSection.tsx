import { Clock, FileCheck, Wallet, MessageSquare, CheckCircle } from "lucide-react";

export default function WhatIsIScoreSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            What is iScore™? Quantifying Trust and Performance.
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            iScore™ is StreamLnk's intelligent rating system, exclusively powered by data from our integrated StreamResources+ engine. It analyzes a multitude of real-time and historical data points from across all StreamLnk portals to generate a comprehensive, dynamic score for every registered supplier, buyer, and service provider.
          </p>

          <div className="space-y-8">
            {/* Operational Excellence Score */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Clock className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Operational Excellence Score</h3>
                  <p className="text-gray-700 mb-4">
                    Inputs: On-time delivery/readiness, order fulfillment accuracy, inventory availability (for suppliers), customs clearance speed, packaging quality, job acceptance rates (for service providers).
                  </p>
                </div>
              </div>
            </div>

            {/* Compliance & Reliability Score */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <FileCheck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Compliance & Reliability Score</h3>
                  <p className="text-gray-700 mb-4">
                    Inputs: Timeliness and validity of all required documentation (licenses, certifications, insurance), adherence to platform terms, dispute history.
                  </p>
                </div>
              </div>
            </div>

            {/* Financial Trustworthiness Score */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Wallet className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Financial Trustworthiness Score</h3>
                  <p className="text-gray-700 mb-4">
                    Inputs (for Buyers): On-time payment history, BNPL repayment record, data from integrated credit agencies (premium feature).
                  </p>
                  <p className="text-gray-700 mb-4">
                    Inputs (for Suppliers/Service Providers): Stability indicators, payment history for platform fees (if applicable).
                  </p>
                </div>
              </div>
            </div>

            {/* Communication & Responsiveness Score */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <MessageSquare className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Communication & Responsiveness Score</h3>
                  <p className="text-gray-700 mb-4">
                    Inputs: Responsiveness to RFQs, order confirmations, platform alerts, and support interactions.
                  </p>
                </div>
              </div>
            </div>

            {/* Platform-Sourced Feedback */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Platform-Sourced Feedback</h3>
                  <p className="text-gray-700 mb-4">
                    Inputs: Aggregated and verified ratings/feedback from other counterparties on the platform (e.g., buyer ratings of suppliers, StreamLnk Ops ratings of service providers).
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-[#004235] p-6 text-white rounded-lg shadow-sm">
              <p className="text-lg font-semibold mb-2">The Result:</p>
              <p>A Composite Overall iScore™ (e.g., 1-100 or A-F) plus detailed sub-scores, providing a holistic view of a partner's track record and reliability within the StreamLnk ecosystem.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}