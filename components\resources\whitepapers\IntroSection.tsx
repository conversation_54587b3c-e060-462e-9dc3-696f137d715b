"use client";

import { CheckCircle } from 'lucide-react';

const benefits = [
  {
    title: "Expert Analysis",
    description: "Insights from StreamLnk's industry specialists, data scientists, and technology leaders."
  },
  {
    title: "Actionable Strategies",
    description: "Practical recommendations and frameworks you can apply to your business."
  },
  {
    title: "Data-Backed Research",
    description: "Findings often supported by data from the StreamLnk ecosystem and credible industry sources."
  },
  {
    title: "Comprehensive Coverage",
    description: "Deep dives into topics ranging from AI and digitization to sustainability and risk management."
  },
  {
    title: "Future-Focused Perspectives",
    description: "Understanding the forces that will shape the industrial landscape of tomorrow."
  }
];

export default function IntroSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
              Gain In-Depth Understanding, Make More Informed Decisions
            </h2>
            <p className="text-xl text-[#028475] mb-2">
              Why Read StreamLnk Whitepapers?
            </p>
            <div className="w-20 h-1 bg-[#028475] mx-auto mb-6"></div>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              StreamLnk's whitepapers provide detailed exploration of key challenges, emerging trends, and innovative solutions in the world of industrial trade. Our research is designed to offer:
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-start p-6 bg-gray-50 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
                <CheckCircle className="h-7 w-7 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">{benefit.title}</h3>
                  <p className="text-gray-600 text-base">{benefit.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}