import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react"; // Added ArrowRight import

export default function CtaSection() {
  return (
    <section className="py-16 bg-white"> {/* Standardized section style */}
      <div className="container mx-auto px-4"> {/* Standardized container style */}
        <div className="max-w-3xl mx-auto text-center"> {/* Added wrapper div with text-center */}
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            Discover StreamLnk Potential
          </h2>
          <p className="text-xl text-gray-700 mb-8"> {/* Standardized paragraph style */}
            Learn More & Get Started
          </p>
          <div className="flex flex-col sm:flex-row flex-wrap gap-4 justify-center">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                REQUEST DEMO
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/about">ABOUT STREAMLNK</Link>
            </Button>
          </div>
          <div className="mt-6 flex flex-col sm:flex-row flex-wrap gap-4 justify-center"> {/* Group link buttons */}
            <Button 
              variant="link" 
              className="text-[#028475] hover:text-[#004235]"
              asChild
            >
              <Link href="/solutions">
                VIEW SOLUTIONS
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button 
              variant="link" 
              className="text-[#028475] hover:text-[#004235]"
              asChild
            >
              <Link href="/contact-us">
                CONTACT US
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}