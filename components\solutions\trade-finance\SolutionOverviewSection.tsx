"use client"

import { Credit<PERSON>ard, Lock, Layers, Globe, BarChart3, ShieldCheck } from "lucide-react"

export default function SolutionOverviewSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Facilitating Smoother, More Secure Transactions: Finance Built for B2B Trade
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            StreamLnk integrates a suite of innovative financial tools and partnerships to address these challenges, making global trade more accessible and secure:
          </p>

          <div className="space-y-8">
            {/* Feature 1 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <CreditCard className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Buy Now, Pay Later (BNPL) for B2B Buyers</h3>
                  <p className="text-gray-700 mb-2">
                    Qualified buyers (assessed via iScore™ and partner criteria) can opt for extended payment terms (e.g., Net 30, 60, 90) at checkout.
                  </p>
                  <p className="text-gray-700 mb-2">
                    StreamLnk partners with leading B2B BNPL providers (e.g., Resolve, TreviPay - illustrative) who pay the supplier upfront (minus a small fee), while collecting from the buyer later.
                  </p>
                  <p className="text-gray-700">
                    <span className="font-semibold">Benefit:</span> Buyers improve cash flow; Suppliers get paid quickly and without credit risk.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 2 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <Lock className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Secure Escrow Services</h3>
                  <p className="text-gray-700 mb-2">
                    For high-value transactions or new trading relationships, funds can be held in a neutral, secure escrow account managed by StreamLnk or a trusted third-party provider.
                  </p>
                  <p className="text-gray-700 mb-2">
                    Funds are released to the supplier upon confirmation of predefined milestones (e.g., shipment, delivery, quality inspection).
                  </p>
                  <p className="text-gray-700">
                    <span className="font-semibold">Benefit:</span> Protects both buyer and supplier, building trust and mitigating transaction risk.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 3 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <Layers className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Milestone-Based Payments</h3>
                  <p className="text-gray-700 mb-2">
                    For complex orders or projects, payments can be structured around the achievement of specific, verifiable milestones in the production or delivery process.
                  </p>
                  <p className="text-gray-700 mb-2">
                    Funds are released incrementally as each milestone is met and confirmed on the platform.
                  </p>
                  <p className="text-gray-700">
                    <span className="font-semibold">Benefit:</span> Aligns payment with progress, reducing risk for buyers and providing interim cash flow for suppliers.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 4 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <Globe className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Multi-Currency Transaction Management</h3>
                  <p className="text-gray-700 mb-2">
                    Facilitates quoting, invoicing, and settlement in multiple major trade currencies.
                  </p>
                  <p className="text-gray-700">
                    Integrated FX solutions provide transparent and competitive rates.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 5 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">AI-Powered Financial Risk Assessment (iScore™)</h3>
                  <p className="text-gray-700">
                    Our iScore™ system evaluates the financial trustworthiness and payment history of platform participants, informing eligibility for BNPL and other credit-related services.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 6 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <ShieldCheck className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Secure Payment Processing & AR Automation</h3>
                  <p className="text-gray-700 mb-2">
                    Robust infrastructure for secure wire transfers and digital payments.
                  </p>
                  <p className="text-gray-700">
                    Automated AR reminders and enforcement mechanisms to ensure timely payments.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}