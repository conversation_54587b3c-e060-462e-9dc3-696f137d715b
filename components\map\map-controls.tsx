"use client"

import { ZoomIn, ZoomOut, RotateCcw, Eye, EyeOff, Layers } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface MapControlsProps {
  showRoutes: boolean
  showAssets: boolean
  onZoomIn: () => void
  onZoomOut: () => void
  onReset: () => void
  onToggleRoutes: () => void
  onToggleAssets: () => void
}

export function MapControls({
  showRoutes,
  showAssets,
  onZoomIn,
  onZoomOut,
  onReset,
  onToggleRoutes,
  onToggleAssets,
}: MapControlsProps) {
  return (
    <div className="absolute top-4 right-4 z-20 flex flex-col gap-2">
      <Button
        onClick={onZoomIn}
        size="sm"
        className="glassmorphism border-slate-200 bg-[#52AAA3]/20 text-slate-900"
      >
        <ZoomIn className="w-4 h-4" />
      </Button>
      <Button
        onClick={onZoomOut}
        size="sm"
        className="glassmorphism border-slate-200 bg-[#52AAA3]/20 text-slate-900"
      >
        <ZoomOut className="w-4 h-4" />
      </Button>
      <Button
        onClick={onReset}
        size="sm"
        className="glassmorphism border-slate-200 bg-[#52AAA3]/20 text-slate-900"
      >
        <RotateCcw className="w-4 h-4" />
      </Button>
      <Button
        onClick={onToggleRoutes}
        size="sm"
        className={`glassmorphism border-slate-200 bg-[#52AAA3]/20 text-slate-900 ${showRoutes ? "bg-[#52AAA3]/20" : ""}`}
      >
        {showRoutes ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
      </Button>
      <Button
        onClick={onToggleAssets}
        size="sm"
        className={`glassmorphism border-slate-200 bg-[#52AAA3]/20 text-slate-900 ${showAssets ? "bg-[#52AAA3]/20" : ""}`}
      >
        <Layers className="w-4 h-4" />
      </Button>
    </div>
  )
}
