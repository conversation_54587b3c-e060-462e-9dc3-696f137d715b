import { Lay<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>3, <PERSON>, FileText } from "lucide-react";

export default function PlatformOverviewSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Your Command Center for Global Shipments – All in One Place
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            StreamLnk revolutionizes shipment management by integrating data from all our specialized portals (StreamFreight, StreamGlobe, StreamGlobe+, StreamPak) into your personalized MyStreamLnk (Customer) or E-Stream (Supplier) dashboard.
          </p>

          <div className="space-y-8">
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Layers className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Unified Shipment Dashboard</h3>
                  <p className="text-gray-700 mb-4">
                    View all your active and past shipments, regardless of origin, destination, or transport mode, in a single, intuitive interface.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Clock className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Real-Time Milestone Tracking</h3>
                  <p className="text-gray-700 mb-4">
                    Get automated updates as your shipment progresses through key stages: Order Confirmed, Production Complete, Ready for Pickup, In Transit (with specific carrier updates), Port Arrival, Customs Submitted, Customs Cleared, Out for Delivery, Delivered.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Predictive ETAs</h3>
                  <p className="text-gray-700 mb-4">
                    Our AI analyzes carrier data, port congestion, and historical performance to provide more accurate and dynamic ETAs.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Bell className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Automated Delay Alerts</h3>
                  <p className="text-gray-700 mb-4">
                    Receive instant notifications if a potential delay is detected in any leg of the journey, allowing for proactive intervention.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <FileText className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Integrated Document Access</h3>
                  <p className="text-gray-700 mb-4">
                    Directly access all relevant shipping documents (Bill of Lading, Packing List, Commercial Invoice, Customs Declarations, Proof of Delivery) linked to each shipment record.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}