"use client"

import { DollarSign, Clock, Lock, FileText, Globe } from "lucide-react"

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Are Payment Complexities and Cash Flow Gaps Stifling Your Trade?
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            High-value, cross-border industrial transactions often come with significant financial challenges for both buyers and suppliers:
          </p>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Challenge 1 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <DollarSign className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  Buyer Cash Flow Constraints: Difficulty paying upfront for large material orders, limiting purchasing power.
                </p>
              </div>
            </div>

            {/* Challenge 2 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <Clock className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  Supplier Payment Risk & Delays: Concern over buyer default on net terms; long waiting periods for payment impacting working capital.
                </p>
              </div>
            </div>

            {/* Challenge 3 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <Lock className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  Lack of Trust Between New Trading Partners: Hesitancy to commit large sums without payment security mechanisms.
                </p>
              </div>
            </div>

            {/* Challenge 4 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <FileText className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  Limited Access to Traditional Trade Finance: Letters of Credit can be slow, costly, and inaccessible for many SMEs.
                </p>
              </div>
            </div>

            {/* Challenge 5 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <Globe className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  Complexity of Managing Cross-Border Payments: Dealing with different currencies, FX risk, and international banking fees.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}