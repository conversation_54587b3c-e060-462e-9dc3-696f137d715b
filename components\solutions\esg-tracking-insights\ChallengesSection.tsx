import { CheckCircle } from "lucide-react";

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Why Sustainability is No Longer Optional, It's Essential
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Businesses worldwide face increasing pressure from investors, customers, regulators, and employees to demonstrate strong ESG performance. In industrial supply chains, this means:
          </p>

          <div className="space-y-4 max-w-3xl mx-auto">
            {[
                "Difficulty in obtaining reliable data on the environmental impact (e.g., carbon footprint, recycled content) of sourced materials.",
                "Lack of visibility into suppliers' social practices (labor conditions, diversity).",
                "Challenges in meeting corporate sustainability targets and regulatory reporting requirements (e.g., CSRD, TCFD).",
                "Risk of reputational damage from associating with suppliers with poor ESG records.",
                "Missed opportunities to align procurement with brand values and appeal to ESG-conscious customers.",
                "Complexity in tracking and reporting on the sustainability of globally dispersed supply chains."
              ].map((challenge, index) => (
              <div key={index} className="flex items-start p-4 bg-gray-50 rounded-lg">
                <CheckCircle className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
                <p className="text-gray-800">{challenge}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}