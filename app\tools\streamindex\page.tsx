"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/tools/streamindex/HeroSection";
import HubSection from "@/components/tools/streamindex/HubSection";
import HowToLeverageSection from "@/components/tools/streamindex/HowToLeverageSection";
import DataAdvantageSection from "@/components/tools/streamindex/DataAdvantageSection";
import CTASection from "@/components/tools/streamindex/CTASection";

export default function StreamIndexPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <HubSection />
      <HowToLeverageSection />
      <DataAdvantageSection />
      <CTASection />

      <BottomFooter />
    </div>
  );
}