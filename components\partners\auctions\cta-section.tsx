import { ArrowR<PERSON> } from "lucide-react"
import { Button } from "@/components/ui/button"

export default function CtaSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">Access StreamLnk Auctions Today</h2>
          <p className="text-lg md:text-xl text-gray-700 mb-8 max-w-3xl mx-auto">
            Requires approved access to E-Stream, MyStreamLnk, or MyStreamLnk+ portals
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="bg-[#004235] hover:bg-[#003228] text-white px-8 py-6 text-lg">
              Access Auctions
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              className="border-[#028475] text-[#028475] hover:bg-[#028475]/10 px-8 py-6 text-lg"
            >
              Learn More About Portals
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
