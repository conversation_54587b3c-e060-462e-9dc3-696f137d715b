"use client"

import { useEffect } from "react"
import * as d3 from "d3"
import { partners, routes } from "@/data/map-data"
import type { LiveAsset, Route } from "@/types/map-types"

interface AssetsLayerProps {
  svg: d3.Selection<SVGSVGElement, unknown, null, undefined>
  showAssets: boolean
  currentZoom: number
  filteredAssets: LiveAsset[]
  selectedAsset: LiveAsset | null
  createArc: (from: [number, number], to: [number, number]) => [number, number][]
  onAssetClick: (asset: LiveAsset) => void
}

export function useAssetsLayer({
  svg,
  showAssets,
  currentZoom,
  filteredAssets,
  selectedAsset,
  createArc,
  onAssetClick,
}: AssetsLayerProps) {
  useEffect(() => {
    if (!svg || !showAssets || currentZoom < 1.2) return

    const assetGroup = svg.append("g").attr("class", "assets")

    filteredAssets.forEach((asset) => {
      const route = routes.find((r) => r.id === asset.routeId)
      if (!route) return

      const fromPartner = partners.find((p) => p.id === route.from)
      const toPartner = partners.find((p) => p.id === route.to)
      if (!fromPartner || !toPartner) return

      const arcPoints = createArc(fromPartner.coordinates, toPartner.coordinates)
      if (arcPoints.length === 0) return

      const currentIndex = Math.floor(asset.progress * (arcPoints.length - 1))
      const currentPoint = arcPoints[currentIndex]
      if (!currentPoint) return

      const [x, y] = currentPoint

      // Asset trail
      if (asset.progress > 0.1) {
        const trailStart = Math.max(0, currentIndex - 10)
        const trailPoints = arcPoints.slice(trailStart, currentIndex + 1)

        if (trailPoints.length > 1) {
          const trailGenerator = d3
            .line()
            .x((d) => d[0])
            .y((d) => d[1])
            .curve(d3.curveCardinal)

          const trailPath = trailGenerator(trailPoints)
          if (trailPath) {
            assetGroup
              .append("path")
              .attr("d", trailPath)
              .attr("class", `asset-trail asset-${asset.id}`)
              .style("fill", "none")
              .style("stroke", "url(#asset-trail)")
              .style("stroke-width", 3)
              .style("pointer-events", "none")
          }
        }
      }

      // Asset marker
      const assetMarker = assetGroup
        .append("g")
        .attr("class", `asset-marker asset-${asset.id}`)
        .attr("transform", `translate(${x}, ${y})`)
        .style("cursor", "pointer")
        .on("click", () => onAssetClick(asset))

      const getAssetColor = (status: string) => {
        switch (status) {
          case "on-time":
            return "#10b981"
          case "delayed":
            return "#f59e0b"
          case "critical":
            return "#ef4444"
          default:
            return "#52AAA3"
        }
      }

      const assetColor = getAssetColor(asset.status)

      // Pulsing background for critical assets
      if (asset.status === "critical") {
        assetMarker
          .append("circle")
          .attr("r", 12)
          .style("fill", assetColor)
          .style("opacity", 0.3)
          .append("animate")
          .attr("attributeName", "r")
          .attr("values", "12;18;12")
          .attr("dur", "2s")
          .attr("repeatCount", "indefinite")
      }

      // Main asset circle
      assetMarker
        .append("circle")
        .attr("r", 6)
        .style("fill", assetColor)
        .style("stroke", "#fff")
        .style("stroke-width", "2px")

      // Asset type icon
      const getAssetIcon = (type: string) => {
        switch (type) {
          case "ship":
            return "🚢"
          case "truck":
            return "🚛"
          case "train":
            return "🚂"
          case "plane":
            return "✈️"
          default:
            return "📦"
        }
      }

      assetMarker
        .append("text")
        .attr("text-anchor", "middle")
        .attr("dy", "0.3em")
        .style("font-size", "8px")
        .style("pointer-events", "none")
        .text(getAssetIcon(asset.type))

      // Asset label (show when selected)
      if (selectedAsset?.id === asset.id) {
        assetMarker
          .append("rect")
          .attr("x", -30)
          .attr("y", -25)
          .attr("width", 60)
          .attr("height", 15)
          .attr("rx", 7)
          .style("fill", "rgba(255, 255, 255, 0.9)")
          .style("stroke", assetColor)
          .style("stroke-width", "1px")

        assetMarker
          .append("text")
          .attr("text-anchor", "middle")
          .attr("y", -17)
          .style("fill", "#1e293b")
          .style("font-size", "8px")
          .style("font-weight", "600")
          .style("pointer-events", "none")
          .text(asset.cargo)
      }
    })
  }, [svg, showAssets, currentZoom, filteredAssets, selectedAsset, createArc, onAssetClick])
}
