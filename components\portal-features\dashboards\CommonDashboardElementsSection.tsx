import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON><PERSON>ig, AlertTriangle, SlidersHorizontal, RefreshCw } from 'lucide-react';

export default function CommonDashboardElementsSection() {
  return (
    <section className="w-full py-12 md:py-24 bg-white">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center space-y-4 text-center mb-10">
          <h2 className="text-2xl font-bold tracking-tighter sm:text-3xl md:text-4xl text-[#004235]">
            Common Dashboard Elements
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="flex flex-col items-center text-center p-6 shadow-md">
            <div className="rounded-full bg-[#004235]/10 p-4 mb-4">
              <BarChartBig className="text-[#028475] h-6 w-6" />
            </div>
            <h3 className="text-lg font-bold mb-2 text-[#004235]">Clear Visualizations</h3>
            <p className="text-sm text-gray-700">Use of charts, graphs, progress bars, and status indicators.</p>
          </Card>

          <Card className="flex flex-col items-center text-center p-6 shadow-md">
            <div className="rounded-full bg-[#004235]/10 p-4 mb-4">
              <AlertTriangle className="text-[#028475] h-6 w-6" />
            </div>
            <h3 className="text-lg font-bold mb-2 text-[#004235]">Actionable Alerts & Notifications</h3>
            <p className="text-sm text-gray-700">Highlighting items that require immediate attention.</p>
          </Card>

          <Card className="flex flex-col items-center text-center p-6 shadow-md">
            <div className="rounded-full bg-[#004235]/10 p-4 mb-4">
              <SlidersHorizontal className="text-[#028475] h-6 w-6" />
            </div>
            <h3 className="text-lg font-bold mb-2 text-[#004235]">Customization</h3>
            <p className="text-sm text-gray-700">Future enhancement: Ability for users to personalize their dashboard layout and widget selection.</p>
          </Card>

          <Card className="flex flex-col items-center text-center p-6 shadow-md">
            <div className="rounded-full bg-[#004235]/10 p-4 mb-4">
              <RefreshCw className="text-[#028475] h-6 w-6" />
            </div>
            <h3 className="text-lg font-bold mb-2 text-[#004235]">Real-Time Data</h3>
            <p className="text-sm text-gray-700">Information is continuously updated from across the StreamLnk ecosystem.</p>
          </Card>
        </div>
      </div>
    </section>
  );
}