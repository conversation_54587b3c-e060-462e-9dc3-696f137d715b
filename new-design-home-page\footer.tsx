import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export default function Component() {
  return (
    <footer className="bg-[#172d2d] text-white py-16 px-6">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="mb-12">
          <h2 className="text-4xl font-serif mb-4">Trulioo</h2>
          <p className="text-lg mb-8 max-w-md">You can trust us to keep you up to date</p>

          {/* Newsletter Subscription */}
          <div className="flex max-w-md">
            <Input
              placeholder="Subscribe to our Newsletter"
              className="bg-transparent border-[#a4dcb4] border-2 text-white placeholder:text-gray-300 rounded-r-none focus:border-[#a4dcb4] focus:ring-[#a4dcb4]"
            />
            <Button className="bg-[#a4dcb4] text-[#172d2d] hover:bg-[#a4dcb4]/90 rounded-l-none px-6">→</Button>
          </div>
        </div>

        {/* Navigation Links */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8 mb-16">
          {/* Solutions */}
          <div>
            <h3 className="font-semibold mb-4 text-[#a4dcb4]">Solutions</h3>
            <ul className="space-y-3 text-sm">
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Global Identity Platform
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Person Match
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Identity Document Verification
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Electronic Identification
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Business Verification
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Business Essentials
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Business Complete
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Watchlist Screening
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Fraud Intelligence
                </a>
              </li>
            </ul>
          </div>

          {/* Industries */}
          <div>
            <h3 className="font-semibold mb-4 text-[#a4dcb4]">Industries</h3>
            <ul className="space-y-3 text-sm">
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Banking
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Cryptocurrency
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Financial Services
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Forex
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Marketplaces
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Online Trading
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Payments
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Remittance
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Wealth Management
                </a>
              </li>
            </ul>
          </div>

          {/* Use Cases */}
          <div>
            <h3 className="font-semibold mb-4 text-[#a4dcb4]">Use Cases</h3>
            <ul className="space-y-3 text-sm">
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Customer Onboarding
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Business Onboarding
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Regulatory Compliance
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Trust & Safety
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Global Expansion
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Business Identity Theft Protection
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Digital Identity Verification
                </a>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="font-semibold mb-4 text-[#a4dcb4]">Resources</h3>
            <ul className="space-y-3 text-sm">
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Blog
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Library
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Developer Portal
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  KYC
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  AML Compliance
                </a>
              </li>
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="font-semibold mb-4 text-[#a4dcb4]">Company</h3>
            <ul className="space-y-3 text-sm">
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  About Us
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Leadership
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Security and Compliance
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Careers
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  News & Press
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Media Kit
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Events
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Partners
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                  Contact Us
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="border-t border-gray-600 pt-8">
          <div className="flex flex-wrap items-center justify-between text-sm text-gray-400">
            <p>Copyright 2025, Trulioo. All rights reserved.</p>
            <div className="flex flex-wrap gap-4 mt-4 lg:mt-0">
              <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                Privacy Policies
              </a>
              <span>/</span>
              <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                Cookie Policy
              </a>
              <span>/</span>
              <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                Research
              </a>
              <span>/</span>
              <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                Sitemap
              </a>
              <span>/</span>
              <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                Do Not Sell or Share My Personal Information
              </a>
              <span>/</span>
              <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                SOC 2 Type II
              </a>
              <span>/</span>
              <a href="#" className="hover:text-[#a4dcb4] transition-colors">
                ISO 27001:2022
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
