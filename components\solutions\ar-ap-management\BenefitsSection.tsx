"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CheckCircle, ChevronRight } from "lucide-react"

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Better Cash Flow, Reduced Risk, Less Admin Work
          </h2>

          <div className="grid md:grid-cols-3 gap-8 mb-12">
            {/* Benefits Column 1 */}
            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Accelerate Receivables: Automated reminders and proactive overdue management significantly reduce payment delays and improve your DSO.</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Simplify Payables: Buyers get a clear, centralized view of all invoices and due dates, making payment processing easier.</span>
                </li>
              </ul>
            </div>

            {/* Benefits Column 2 */}
            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Reduce Bad Debt Risk: Early identification of overdue accounts and automated holds on new orders/shipments minimize exposure to defaults.</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Lower Administrative Costs: Automation of invoicing, reminders, and status tracking frees up your finance team from manual follow-ups.</span>
                </li>
              </ul>
            </div>

            {/* Benefits Column 3 */}
            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Enhance Transparency: All parties have clear visibility into invoice statuses and payment timelines.</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Improve Financial Planning: More predictable cash inflows and outflows enable better working capital management.</span>
                </li>
              </ul>
            </div>
          </div>

          <div className="bg-[#F2F2F2] p-8 rounded-lg border border-gray-200">
            <h3 className="text-xl font-semibold text-[#004235] mb-4">Seamless Financial Interactions for Every Transaction</h3>
            
            <div className="grid md:grid-cols-2 gap-8 mb-6">
              {/* For Buyers */}
              <div>
                <h4 className="font-semibold text-[#004235] mb-2">For Buyers (MyStreamLnk):</h4>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                    <span className="text-gray-700">Never miss a payment with automated reminders.</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                    <span className="text-gray-700">Easily access all your invoices and payment history in one place.</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                    <span className="text-gray-700">Utilize flexible payment options like BNPL or Escrow.</span>
                  </li>
                </ul>
              </div>
              
              {/* For Suppliers */}
              <div>
                <h4 className="font-semibold text-[#004235] mb-2">For Suppliers (E-Stream):</h4>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                    <span className="text-gray-700">Get paid faster and more reliably.</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                    <span className="text-gray-700">Reduce time spent chasing payments.</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#028475] mt-0.5 mr-2 flex-shrink-0" />
                    <span className="text-gray-700">Clear visibility into payout schedules and deducted platform fees.</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}