"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Newspaper, BarChart3, ArrowRight } from "lucide-react";
import Link from "next/link";

export default function GatewaysSection() {
  const gateways = [
    {
      icon: <Newspaper className="h-12 w-12 text-[#028475] mb-5" />,
      title: "Delivered by StreamLnk (Our Thought Leadership Hub)",
      description: "Explore our regularly updated blog and digital magazine featuring articles, expert interviews, case studies, and whitepapers on key topics impacting global industrial trade, technology, sustainability, and compliance. Ideal for staying informed on broader trends and best practices.",
      contentHighlights: [
        "Latest Market Commentary",
        "Technology Spotlights (AI, Digitization)",
        "Supply Chain Optimization Strategies",
        "Sustainability & ESG Developments"
      ],
      buttonText: "VISIT DELIVERED BY STREAMLNK",
      buttonLink: "/resources/blog", // Placeholder link
      buttonIcon: <ArrowRight className="ml-2 h-5 w-5" />
    },
    {
      icon: <BarChart3 className="h-12 w-12 text-[#028475] mb-5" />,
      title: "StreamResources+ (Premium Data & Analytics Portal)",
      description: "For businesses requiring deep, granular, and real-time market intelligence. StreamResources+ offers subscription-based access to our proprietary StreamIndex™ benchmarks, iScore™ partner ratings, detailed market reports, demand/supply forecasts, and DaaS API integrations.",
      contentHighlights: [
        "Real-Time Pricing & Logistics Benchmarks (StreamIndex™)",
        "Comprehensive Partner Risk & Performance Data (iScore™)",
        "Predictive Analytics & Custom Reporting",
        "Direct Data Feeds for Enterprise Systems"
      ],
      buttonText: "LEARN ABOUT STREAMRESOURCES+",
      buttonLink: "/solutions/streamresources",
      buttonIcon: <ArrowRight className="ml-2 h-5 w-5" />
    }
  ];

  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Your Gateways to Industrial Trade Intelligence
          </h2>
          <p className="text-xl text-gray-700">
            Our Primary Insight Platforms & Resources
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10">
          {gateways.map((gateway, index) => (
            <div key={index} className="bg-white p-8 rounded-xl shadow-lg flex flex-col">
              <div className="flex justify-center mb-4">{gateway.icon}</div>
              <h3 className="text-2xl font-semibold text-[#004235] mb-4 text-center">{gateway.title}</h3>
              <p className="text-gray-600 mb-5 text-sm text-center">{gateway.description}</p>
              
              <div className="mb-6">
                <h4 className="font-semibold text-[#004235] mb-2 text-center">Content Highlights:</h4>
                <ul className="list-disc list-inside text-gray-600 space-y-1 text-sm text-center mx-auto max-w-xs">
                  {gateway.contentHighlights.map((highlight, i) => (
                    <li key={i}>{highlight}</li>
                  ))}
                </ul>
              </div>

              <div className="mt-auto flex justify-center">
                <Button 
                  className={`${index === 0 ? 'bg-[#004235] hover:bg-[#028475] text-white' : 'border-[#004235] text-white hover:bg-[#004235] hover:text-white'} px-6 py-3 w-full sm:w-auto max-w-xs`}
                  size="lg"
                  asChild
                >
                  <Link href={gateway.buttonLink}>
                    {gateway.buttonText}
                    {gateway.buttonIcon}
                  </Link>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}