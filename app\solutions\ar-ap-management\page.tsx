"use client"

import Link from "next/link"
import Image from "next/image"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import HeroSection from "@/components/solutions/ar-ap-management/HeroSection"
import ChallengesSection from "@/components/solutions/ar-ap-management/ChallengesSection"
import SolutionOverviewSection from "@/components/solutions/ar-ap-management/SolutionOverviewSection"
import BenefitsSection from "@/components/solutions/ar-ap-management/BenefitsSection"
import CTASection from "@/components/solutions/ar-ap-management/CTASection"

export default function ARAPManagementPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <SolutionOverviewSection />

      <BenefitsSection />

      <CTASection />

      <MainFooter />
    </div>
  )
}