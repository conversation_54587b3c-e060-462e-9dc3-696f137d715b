"use client";

import Link from "next/link";
import { ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function CTASection() {
  return (
    <section className="py-16 bg-white"> {/* Changed background to white */}
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6"> {/* Added md:text-4xl */}
            Build Smarter, Faster?
          </h2>
          <p className="text-xl text-gray-700 mb-8"> {/* Changed text-lg to text-xl */}
            Streamline your construction material procurement and project management with our innovative digital solutions. Enhance collaboration, gain real-time insights, and optimize your supply chain for greater efficiency and profitability.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center"> {/* Removed mb-6 from this div */}
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto" /* Added w-full sm:w-auto */
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                REQUEST DEMO {/* Standardized text */}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto" /* Added w-full sm:w-auto */
              size="lg"
              asChild
            >
              <Link href="/solutions/construction"> {/* Updated link and text */}
                EXPLORE PRODUCTS
              </Link>
            </Button>
          </div>
          <div className="mt-6"> {/* Added new div for link button */}
            <Button 
              variant="link" 
              className="text-[#028475] hover:text-[#004235]"
              asChild
            >
              <Link href="/contact-us">
                TALK TO EXPERT
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}