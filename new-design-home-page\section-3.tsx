"use client"

import { useState, useEffect, useRef } from "react"
import { Button } from "@/components/ui/button"
import { Plus } from "lucide-react"
import { motion, useAnimation, useInView } from "framer-motion"

export default function Component() {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({})
  const statsRef = useRef(null)
  const isInView = useInView(statsRef, { once: false, amount: 0.3 })
  const controls = useAnimation()

  useEffect(() => {
    if (isInView) {
      controls.start("visible")
    }
  }, [controls, isInView])

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  const stats = [
    { number: "5+", label: "Billion Potential Customers" },
    { number: "700M", label: "Verifiable Business Entities" },
    { number: "195", label: "Countries Covered" },
    { number: "450+", label: "Global and Local Data Sources" },
  ]

  const solutions = [
    {
      id: "person-match",
      title: "Person Match",
      content:
        "Trulioo accurately verifies digital identities around the world with industry-leading regional match rates, data and local results. Easily expand across borders and onboard more users.",
    },
    {
      id: "identity-document",
      title: "Learn more about Person Match Identity Document Verification",
      content:
        "Capture and authenticate more than 14,000 identity documents worldwide. Elevate the customer experience with our award-winning user interface, advanced liveness and image-capture SDKs that offer unlimited flexibility in the front-end design. Access the powerful anti-fraud capability through the Trulioo API or a hosted no-code platform.",
    },
    {
      id: "business-verification",
      title: "Learn more about Identity Document Verification Business Verification",
      content:
        "Build a 360-degree view of business customers, from confirming registration numbers to identifying ultimate beneficial ownership. The Business Verification suite of tools enables authentication of more than 700 million businesses worldwide.",
    },
    {
      id: "global-watchlist",
      title: "Learn more about Business Verification Global Watchlist Screening",
      content:
        "Screen customers against more than 6,000 global watchlists and 20,000 adverse media sources to achieve AML, sanction screening and KYC compliance. Reduce false positives and minimize manual reviews to meet customer expectations and protect your business.",
    },
    {
      id: "person-fraud",
      title: "Learn more about Global Watchlist Screening Person Fraud",
      content:
        "Shield your organization from synthetic identity and third-party fraud. Trulioo Person Fraud harnesses powerful risk intelligence from email, phone and network data to stop bad actors while ensuring a smooth experience for legitimate customers.",
    },
  ]

  return (
    <div className="min-h-screen bg-[#ffffff]">
      {/* Stats Section */}
      <div className="py-12 px-4" ref={statsRef}>
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="flex flex-col items-center">
                <div className="relative w-32 h-32">
                  {/* Rotating circle background */}
                  <motion.div
                    className="absolute inset-0 rounded-full border-4 border-[#a4dcb4] border-opacity-30"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 20, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                  />

                  {/* Filling circle animation */}
                  <svg className="absolute inset-0 w-full h-full -rotate-90">
                    <motion.circle
                      cx="64"
                      cy="64"
                      r="60"
                      fill="none"
                      stroke="#a4dcb4"
                      strokeWidth="8"
                      strokeLinecap="round"
                      initial={{ pathLength: 0 }}
                      animate={controls}
                      variants={{
                        visible: { pathLength: 1 },
                        hidden: { pathLength: 0 },
                      }}
                      transition={{ duration: 1.5, ease: "easeInOut", delay: index * 0.2 }}
                    />
                  </svg>

                  {/* Static content inside circle */}
                  <div className="absolute inset-0 flex flex-col items-center justify-center bg-[#a4dcb4] rounded-full z-10">
                    <div className="text-2xl font-bold text-[#000000]">{stat.number}</div>
                    <div className="text-xs text-[#000000] text-center px-2 leading-tight mt-1">{stat.label}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Identity Verification Section */}
      <div className="bg-[#172d2d] py-16 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-[#ffffff] mb-8">Identity Verification Knows No Border</h1>
          <p className="text-[#ffffff] text-lg mb-12 max-w-3xl mx-auto leading-relaxed">
            With more than 450 data sources and coverage of 14,000-plus document types from around the world, we help
            you navigate the unique verification challenges of any country. Browse document types available for
            verification.
          </p>

          {/* Divider line */}
          <div className="w-24 h-0.5 bg-[#ffffff] mx-auto mb-16"></div>

          <h2 className="text-3xl font-bold text-[#ffffff] mb-8">MAP</h2>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-12 px-4 border-t border-b border-[#d9d9d9]">
        <div className="max-w-4xl mx-auto flex flex-col md:flex-row items-center justify-between">
          <h3 className="text-2xl font-semibold text-[#000000] mb-4 md:mb-0">Our Experts Are Ready to Help You Grow</h3>
          <Button className="bg-[#c35056] hover:bg-[#c35056]/90 text-[#ffffff] px-6 py-3 rounded-full font-medium">
            Book a demo
          </Button>
        </div>
      </div>

      {/* Solutions Section */}
      <div className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-[#000000] text-center mb-12">
            Sophisticated Solutions That Minimize Risk
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Image placeholder */}
            <div className="bg-[#d9d9d9] aspect-square rounded-lg"></div>

            {/* Expandable sections */}
            <div className="space-y-4">
              {solutions.map((solution) => (
                <div key={solution.id} className="border-b border-[#d9d9d9] pb-4">
                  <button
                    onClick={() => toggleSection(solution.id)}
                    className="flex items-center justify-between w-full text-left"
                  >
                    <h3 className="text-lg font-semibold text-[#000000] pr-4">{solution.title}</h3>
                    <Plus
                      className={`w-5 h-5 text-[#000000] transition-transform ${
                        expandedSections[solution.id] ? "rotate-45" : ""
                      }`}
                    />
                  </button>
                  {expandedSections[solution.id] && (
                    <div className="mt-4 text-[#808080] leading-relaxed">{solution.content}</div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
