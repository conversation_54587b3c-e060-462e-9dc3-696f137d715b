"use client";

import Image from "next/image";
import Link from "next/link";
import { ChevronRight, Download, Mail, Send, Users, Award, BookOpen, FileText, Megaphone, ArrowRight } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";
import { CountrySelector } from "@/components/country-selector";
import React from 'react';
// Assuming a similar NewsCard component or adapting it for featured news
// import { NewsCard } from "@/components/news/NewsCard"; // Or a new PressCenterNewsCard

// Placeholder for NewsCardProps if adapting NewsCard
interface NewsCardProps {
  slug: string;
  imageUrl: string;
  category?: string; // Category might be optional here or different
  title: string;    // Changed from headline to title to match user's text
  excerpt: string;
  date: string;     // Changed from publicationDate to date
  linkText?: string;
}

const FeaturedNewsCard: React.FC<NewsCardProps> = ({ slug, imageUrl, title, excerpt, date, linkText = "READ MORE" }) => {
  return (
    <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden flex flex-col border border-gray-200">
      <Link href={`/news/${slug}`} className="block group">
        <div className="relative w-full h-56 md:h-64">
          <Image
            src={imageUrl}
            alt={title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </div>
        <div className="p-6 flex flex-col flex-grow">
          <h3 className="text-xl font-semibold text-[#004235] mb-3 group-hover:text-[#028475] transition-colors duration-300">
            {title}
          </h3>
          <p className="text-gray-600 text-sm mb-3">
            {date}
          </p>
          <p className="text-gray-700 text-sm mb-4 line-clamp-3 flex-grow">
            {excerpt}
          </p>
        </div>
      </Link>
      <div className="p-6 pt-0 mt-auto">
        <Button
          className="bg-[#004235] hover:bg-[#028475] text-white w-full text-sm"
          asChild
        >
          <Link href={`/news/${slug}`}>
            {linkText}
            <ChevronRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </div>
    </div>
  );
};

const featuredNewsItems: NewsCardProps[] = [
  {
    slug: "ai-powered-streamindex-launch", // Example slug
    imageUrl: "/images/placeholder-ai-graphic.jpg", // Replace with actual image path
    title: "StreamLnk Launches AI-Powered StreamIndex™ to Revolutionize Industrial Material Price Transparency",
    date: "October 29, 2023", // Example Date
    excerpt: "Our groundbreaking AI-powered market intelligence suite offers real-time benchmarks for pricing, logistics, and risk.",
    linkText: "READ MORE"
  },
  {
    slug: "sustainable-sourcing-initiative-press", // Example slug
    imageUrl: "/images/placeholder-green-industrial.jpg", // Replace with actual image path
    title: "StreamLnk Champions Sustainable Future with New Global Sourcing Initiative for Industrial Materials",
    date: "October 27, 2023", // Example Date
    excerpt: "Platform enhancements and strategic partnerships aim to boost transparency and adoption of recycled materials.",
    linkText: "READ MORE"
  },
];

export default function PressCenterPage() {
  // Basic state for subscription form
  const [email, setEmail] = React.useState('');

  const handleSubscriptionSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Handle news alert subscription logic here
    console.log({ email, subscribedTo: 'Press Center News Alerts' });
    alert(`Subscribed with ${email} for StreamLnk Press Center News Alerts!`);
    setEmail('');
  };

  return (
    <div className="flex min-h-screen flex-col bg-white">
      <CountrySelector />
      <MainNav />

      <main>
        {/* Hero Section */}
        <section className="bg-[#F2F2F2] py-16 md:py-24">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
                  StreamLnk Press Center & Media Room: Your Hub for Our Story
                </h1>
                <p className="text-xl text-gray-700 mb-8">
                  Access the latest news, announcements, media kits, and expert insights from StreamLnk, the leader in digitizing global industrial supply chains.
                </p>
                <Button
                  className="bg-[#004235] hover:bg-[#028475] text-white px-6"
                  size="lg"
                  asChild
                >
                  <Link href="/news?source=press-center-hero">
                    LATEST NEWS
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
              </div>
              <div className="relative w-full h-[300px] md:h-[450px] rounded-lg overflow-hidden shadow-xl">
                <Image
                  src="/images/press-center/press-hero-placeholder.webp" // Placeholder - suggest user to replace
                  alt="StreamLnk Press Center"
                  fill
                  className="object-cover"
                  priority
                />
                {/* TODO: User to replace with a relevant image for the press center hero */}
              </div>
            </div>
          </div>
        </section>

        {/* Welcome Section */}
        <section className="py-12 md:py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center mb-12">
              <h2 className="text-3xl font-bold text-[#004235] mb-6">
                Welcome to the StreamLnk Media Room
              </h2>
              <p className="text-lg text-gray-700">
                We are committed to transparent communication about our innovations, partnerships, and impact on the global industrial trade landscape. This Press Center is designed to provide journalists, analysts, and media professionals with readily accessible resources to tell the StreamLnk story. Here you'll find our official announcements, media assets, leadership insights, and contact information for our communications team.
              </p>
            </div>

            {/* What You'll Find Here Section */}
            <div className="max-w-4xl mx-auto">
              <h3 className="text-2xl font-semibold text-[#004235] mb-8 text-center">What You'll Find Here</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {
                  [
                    { title: "Latest News & Announcements", href: "/news", icon: Megaphone, description: "Stay up-to-date with StreamLnk's most recent breakthroughs, product launches, strategic partnerships, and significant company milestones.", linkText: "VIEW ALL NEWS" },
                    { title: "Press Releases", href: "/press-releases", icon: FileText, description: "Access our official press releases detailing key developments, financial updates, and corporate communications.", linkText: "BROWSE RELEASES" },
                    { title: "Media Kit & Brand Assets", href: "/media-kit", icon: Download, description: "Download high-resolution StreamLnk logos, brand guidelines, and other essential visual assets for your coverage.", linkText: "DOWNLOAD MEDIA KIT" },
                    { title: "Leadership Team & Bios", href: "/about-us#leadership", icon: Users, description: "Get to know the visionaries behind StreamLnk. Access biographies and high-resolution headshots of our executive team.", linkText: "MEET LEADERSHIP" },
                    { title: "Awards & Recognition", href: "/about-us#awards", icon: Award, description: "Discover the accolades and industry recognition StreamLnk has received for its innovation and market impact.", linkText: "VIEW AWARDS" },
                    { title: "StreamLnk In The News", href: "/news/media-coverage", icon: BookOpen, description: "Explore a curated collection of recent media coverage, articles, and interviews featuring StreamLnk.", linkText: "SEE COVERAGE" },
                  ].map((item) => (
                    <div key={item.title} className="bg-gray-50 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col">
                      <item.icon className="h-10 w-10 text-[#028475] mb-4" />
                      <h4 className="text-xl font-semibold text-[#004235] mb-2">{item.title}</h4>
                      <p className="text-gray-600 text-sm mb-4 flex-grow">{item.description}</p>
                      <Button variant="link" className="text-[#028475] hover:text-[#004235] p-0 self-start mt-auto" asChild>
                        <Link href={item.href}>
                          {item.linkText} <ChevronRight className="ml-1 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  ))
                }
              </div>
            </div>
          </div>
        </section>

        {/* Featured News Section */}
        <section className="py-12 md:py-20 bg-[#f3f4f6]">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-[#004235] mb-10 text-center">
              Featured News
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {featuredNewsItems.map((newsItem) => (
                <FeaturedNewsCard key={newsItem.slug} {...newsItem} />
              ))}
            </div>
          </div>
        </section>

        {/* Stay Informed Section (Subscription) */}
        <section className="py-16 md:py-24 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto text-center bg-[#F2F2F2] p-8 md:p-12 rounded-xl shadow-xl">
              <Mail className="h-12 w-12 text-[#028475] mb-6 mx-auto" />
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
                Stay Informed: Get StreamLnk News Alerts
              </h2>
              <p className="text-lg text-gray-700 mb-8">
                Subscribe to our media list to receive immediate notifications about our latest announcements, press releases, and key updates directly to your inbox.
              </p>
              <form onSubmit={handleSubscriptionSubmit} className="space-y-6">
                <div>
                  <Input
                    type="email"
                    placeholder="Your Email Address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="w-full p-3 text-lg border-gray-300 focus:border-[#004235] focus:ring-[#004235] rounded-md placeholder-gray-500"
                    aria-label="Email for news alerts"
                  />
                </div>
                <div>
                  <Button
                    type="submit"
                    className="bg-[#004235] hover:bg-[#028475] text-white w-full text-lg py-3"
                    size="lg"
                  >
                    SUBSCRIBE TO NEWS ALERTS
                    <Send className="ml-2 h-5 w-5" />
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </section>

        {/* Media Contact Section */}
        <section className="py-12 md:py-20 border-t border-gray-200">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl font-bold text-[#004235] mb-6">
                Media Contact
              </h2>
              <p className="text-lg text-gray-700 mb-6">
                For all media inquiries, interview requests, or further information, please contact our dedicated communications team:
              </p>
              <div className="space-y-3 mb-8">
                <p className="text-lg text-gray-800">
                  Email: <a href="mailto:<EMAIL>" className="text-[#028475] hover:text-[#004235] hover:underline"><EMAIL></a>
                </p>
                {/* Add phone number if available */}
                {/* <p className="text-lg text-gray-800">Phone: [Your Media Relations Phone Number]</p> */}
              </div>
              <Button
                variant="outline"
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
                size="lg"
                asChild
              >
                {/* Assuming a contact page or a specific form link */}
                <Link href="/contact?inquiry=media">
                  SUBMIT MEDIA INQUIRY
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </section>

      </main>

      <MainFooter />
    </div>
  );
}