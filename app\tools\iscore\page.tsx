"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/tools/iscore/HeroSection";
import UnderstandingIScoreSection from "@/components/tools/iscore/UnderstandingIScoreSection";
import KeyFeaturesSection from "@/components/tools/iscore/KeyFeaturesSection";
import AccessUtilizeSection from "@/components/tools/iscore/AccessUtilizeSection";
import ValuePropositionSection from "@/components/tools/iscore/ValuePropositionSection";
import CTASection from "@/components/tools/iscore/CTASection";

export default function IScorePage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <UnderstandingIScoreSection />
      <KeyFeaturesSection />
      <AccessUtilizeSection />
      <ValuePropositionSection />
      <CTASection />

      <BottomFooter />
    </div>
  );
}