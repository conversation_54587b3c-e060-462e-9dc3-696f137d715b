"use client";

import { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Mail, Send } from "lucide-react";

export default function NewsSubscriptionCtaSection() {
  const [email, setEmail] = useState('');

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Handle news alert subscription logic here
    console.log({ email, subscribedTo: 'News Alerts' });
    alert(`Subscribed with ${email} for StreamLnk News Alerts!`);
    setEmail('');
  };

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center bg-[#F2F2F2] p-8 md:p-12 rounded-xl shadow-xl">
          <Mail className="h-12 w-12 text-[#028475] mb-6 mx-auto" />
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Stay Informed: Get the Latest StreamLnk News Delivered
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Don't miss out on important announcements and company updates. Subscribe to our dedicated news mailing list.
          </p>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Input 
                type="email" 
                placeholder="Your Email Address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required 
                className="w-full p-3 text-lg border-gray-300 focus:border-[#004235] focus:ring-[#004235] rounded-md placeholder-gray-500"
                aria-label="Email for news alerts"
              />
            </div>
            
            <div>
              <Button 
                type="submit"
                className="bg-[#004235] hover:bg-[#028475] text-white w-full text-lg py-3"
                size="lg"
              >
                SUBSCRIBE TO NEWS ALERTS
                <Send className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
}