import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowR<PERSON>, BarChart, Bell, Briefcase, DollarSign, FileText, HelpCircle, Settings, Star, User } from 'lucide-react';
import { MainNav } from '@/components/main-nav';
import { MainFooter } from '@/components/main-footer';

export const metadata: Metadata = {
  title: 'StreamLnk User Portal | Your Personalized StreamLnk Hub',
  description: 'Control, Clarity, and Collaboration. This secure, post-login environment centralizes and optimizes all your industrial trade activities, providing real-time insights, streamlined workflows, and seamless global connectivity.',
};

const UserPortalPage = () => {
  const portalSections = [
    {
      title: 'My Dashboard',
      description: 'Your personalized landing page, offering an at-a-glance overview of critical activities, pending actions, and key performance indicators like orders, shipments, payments, and alerts.',
      icon: <BarChart className="h-8 w-8 text-[#028475]" />,
      link: '/user-portal/dashboard',
    },
    {
      title: 'My Orders / Sales Orders',
      description: 'Manage all active and historical transactions. View detailed orders with specifications, pricing, timelines, integrated shipment tracking, and associated documents. Take actions like updating status, confirming receipt, or initiating disputes.',
      icon: <Briefcase className="h-8 w-8 text-[#028475]" />,
      link: '/user-portal/orders',
    },
    {
      title: 'My Profile & Settings',
      description: 'Manage your personal and company profiles, upload and track compliance documents, customize notification preferences, and administer user roles for your team.',
      icon: <Settings className="h-8 w-8 text-[#028475]" />,
      link: '/user-portal/profile',
    },
    {
      title: 'Billing & Payments',
      description: 'A centralized hub for all financial activities, including managing invoices, tracking payment statuses, monitoring payouts, and accessing financial reports.',
      icon: <DollarSign className="h-8 w-8 text-[#028475]" />,
      link: '/user-portal/billing',
    },
    {
      title: 'My Documents / Compliance Center',
      description: 'Securely store and manage all critical trade and compliance documents. Track document status, expiry dates, and upload new files.',
      icon: <FileText className="h-8 w-8 text-[#028475]" />,
      link: '/user-portal/documents',
    },
    {
      title: 'Notifications & Alerts',
      description: 'Stay instantly informed with customizable alerts for new orders, shipment status changes, document expiries, payment updates, and important system messages.',
      icon: <Bell className="h-8 w-8 text-[#028475]" />,
      link: '/user-portal/notifications',
    },
    {
      title: 'Support & Resources',
      description: 'Directly access help, submit support tickets, and find links to comprehensive FAQs, onboarding guides, and a trade glossary.',
      icon: <HelpCircle className="h-8 w-8 text-[#028475]" />,
      link: '/user-portal/support',
    },
    {
      title: 'Tier & Rewards',
      description: 'Track your progress within the StreamLnk Tier & Rewards program, view your current tier, progress to the next, and earned benefits.',
      icon: <Star className="h-8 w-8 text-[#028475]" />,
      link: '/user-portal/rewards',
    },
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <MainNav />
      {/* Hero Section */}
      <section className="bg-[#F2F2F2] py-16 md:py-24">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-4">
            StreamLnk User Portal
          </h1>
          <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto mb-8">
            Your Personalized StreamLnk Hub: Control, Clarity, and Collaboration
          </p>
          <p className="text-md md:text-lg text-gray-600 max-w-4xl mx-auto">
            This secure, post-login environment centralizes and optimizes all your industrial trade activities, providing real-time insights, streamlined workflows, and seamless global connectivity.
          </p>
        </div>
      </section>

      {/* Key Sections */}
      <section className="py-12 md:py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center text-[#004235] mb-12">
            Key Sections within Your StreamLnk Portal
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {portalSections.map((section) => (
              <Card key={section.title} className="flex flex-col overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader className="pb-4">
                  <div className="flex items-center space-x-4 mb-2">
                    {section.icon}
                    <CardTitle className="text-xl font-semibold text-[#004235]">{section.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="flex-grow">
                  <CardDescription className="text-gray-600 mb-4">
                    {section.description}
                  </CardDescription>
                  <Button variant="link" asChild className="text-[#028475] hover:text-[#004235] p-0 font-semibold">
                    <Link href={section.link}>
                      Go to {section.title} <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Placeholder for future content or CTA */}
      <section className="py-16 bg-[#f3f4f6]">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-2xl font-semibold text-[#004235] mb-4">Ready to Streamline Your Trade?</h2>
          <p className="text-gray-700 mb-8 max-w-xl mx-auto">
            Explore your portal or contact support if you need assistance.
          </p>
          <div className="space-x-4">
            <Button className="bg-[#004235] hover:bg-[#028475] text-white px-8 py-3">
              Explore Dashboard
            </Button>
            <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-8 py-3">
              Contact Support
            </Button>
          </div>
        </div>
      </section>
      <MainFooter />
    </div>
  );
};

export default UserPortalPage;