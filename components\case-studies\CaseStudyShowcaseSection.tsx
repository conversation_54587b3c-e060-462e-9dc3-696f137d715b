"use client";

import { useState, useEffect } from 'react';
import CaseStudyCard, { CaseStudy } from "./CaseStudyCard";
import { Button } from "@/components/ui/button";

// Mock data for case studies - replace with actual data fetching and filtering logic
const allCaseStudies: CaseStudy[] = [
  {
    slug: "polymer-source-sourcing-time",
    imageSrc: "/images/case-studies/case-study-manufacturing.webp", // Placeholder
    imageAlt: "Modern manufacturing facility",
    clientName: "PolymerSource Inc.",
    clientType: "SME Manufacturer",
    title: "SME Manufacturer Cuts Sourcing Time from Weeks to Days",
    challenge: "Limited supplier base and lengthy procurement cycles.",
    results: [
      "70% Reduction in Sourcing Time",
      "10% Average Cost Savings",
      "Access to Global Suppliers",
    ],
    tags: ["Buyer", "Polymers", "Cost Savings", "Efficiency"],
  },
  {
    slug: "globalchem-export-expansion",
    imageSrc: "/images/case-studies/case-study-shipping.webp", // Placeholder
    imageAlt: "Cargo ship and containers",
    clientName: "GlobalChem Suppliers Ltd.",
    clientType: "International Producer",
    title: "Polymer Producer Expands Export Reach & Optimizes Inventory",
    challenge: "Difficulty accessing new international markets and managing surplus stock.",
    results: [
      "Entered 2 New Continents",
      "$1.5M Additional Export Sales",
      "500MT Surplus Sold via Auction",
    ],
    tags: ["Supplier", "Polymers", "Market Expansion", "Inventory Management"],
  },
  {
    slug: "logitrans-freight-efficiency",
    imageSrc: "/images/case-studies/case-study-logistics.webp", // Placeholder
    imageAlt: "Truck on a highway",
    clientName: "LogiTrans Connect",
    clientType: "Regional Freight Carrier",
    title: "Freight Carrier Increases Load Volume & Reduces Empty Miles",
    challenge: "Inconsistent job flow and inefficient routing.",
    results: [
      "18% Increase in Monthly Loads",
      "10% Reduction in Empty Miles",
      "Faster Payment Cycles",
    ],
    tags: ["Freight Partner", "Logistics", "Efficiency"],
  },
  {
    slug: "auto-parts-global-visibility",
    imageSrc: "/images/case-studies/case-study-automotive.webp", // Placeholder
    imageAlt: "Automotive parts assembly line",
    clientName: "AutoParts Global",
    clientType: "Tier 1 Automotive Supplier",
    title: "Automotive Supplier Achieves Full Supply Chain Visibility",
    challenge: "Lack of real-time tracking for critical components from sub-suppliers.",
    results: [
      "95% On-Time Delivery Rate Achieved",
      "Reduced Expedited Freight Costs by 22%",
      "Improved Production Planning Accuracy",
    ],
    tags: ["Buyer", "Automotive", "Improved Visibility", "Risk Mitigation"],
  },
  {
    slug: "chemtrade-customs-clearance",
    imageSrc: "/images/case-studies/case-study-customs.webp", // Placeholder
    imageAlt: "Customs inspection area",
    clientName: "ChemTrade Logistics",
    clientType: "Customs Agent & Forwarder",
    title: "Customs Agent Streamlines Clearance for Chemical Shipments",
    challenge: "Complex documentation and delays in customs processing for hazardous materials.",
    results: [
      "Reduced Customs Delays by 30%",
      "Improved Compliance Score with Authorities",
      "Enhanced Client Satisfaction",
    ],
    tags: ["Customs Agent", "Industrial Chemicals", "Efficiency", "Compliance"],
  },
  {
    slug: "eco-pack-sustainable-packaging",
    imageSrc: "/images/case-studies/case-study-packaging.webp", // Placeholder
    imageAlt: "Sustainable packaging materials",
    clientName: "EcoPack Solutions",
    clientType: "Packaging Provider",
    title: "Packaging Provider Meets ESG Goals with Sustainable Sourcing",
    challenge: "Sourcing certified sustainable packaging materials at competitive prices.",
    results: [
      "Increased Use of Recycled Materials by 40%",
      "Secured 3 New Eco-Conscious Clients",
      "Enhanced Brand Reputation for Sustainability",
    ],
    tags: ["Supplier", "Packaging", "ESG / Sustainability", "Market Expansion"],
  },
];

interface CaseStudyShowcaseSectionProps {
  // Props for filtering will be added here later
  // filters: { role: string; industry: string; benefit: string };
}

const ITEMS_PER_PAGE = 6;

export default function CaseStudyShowcaseSection({ /* filters */ }: CaseStudyShowcaseSectionProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [filteredStudies, setFilteredStudies] = useState<CaseStudy[]>(allCaseStudies);

  // TODO: Implement actual filtering logic based on props
  // useEffect(() => {
  //   let studies = allCaseStudies;
  //   if (filters.role !== 'all') {
  //     studies = studies.filter(study => study.tags.includes(filters.role)); // Simplified, improve tag matching
  //   }
  //   if (filters.industry !== 'all') {
  //     studies = studies.filter(study => study.tags.includes(filters.industry)); // Simplified
  //   }
  //   if (filters.benefit !== 'all') {
  //     studies = studies.filter(study => study.tags.includes(filters.benefit)); // Simplified
  //   }
  //   setFilteredStudies(studies);
  //   setCurrentPage(1); // Reset to first page on filter change
  // }, [filters]);

  const totalPages = Math.ceil(filteredStudies.length / ITEMS_PER_PAGE);
  const paginatedStudies = filteredStudies.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Optionally, scroll to the top of the showcase section
    // document.getElementById('case-study-showcase')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section id="case-study-showcase" className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-[#004235] mb-10 text-center">
          Case Study Showcase
        </h2>

        {paginatedStudies.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {paginatedStudies.map((study) => (
              <CaseStudyCard key={study.slug} study={study} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-xl text-gray-600 mb-4">No case studies match your current filter criteria.</p>
            <p className="text-gray-500">Try adjusting your filters or view all case studies.</p>
            {/* TODO: Add a button to clear filters here if needed */}
          </div>
        )}

        {totalPages > 1 && (
          <div className="flex justify-center items-center space-x-2 mt-12">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
            >
              Previous
            </Button>
            {[...Array(totalPages)].map((_, i) => (
              <Button
                key={i + 1}
                variant={currentPage === i + 1 ? "default" : "outline"}
                size="sm"
                onClick={() => handlePageChange(i + 1)}
                className={
                  currentPage === i + 1
                    ? "bg-[#004235] text-white hover:bg-[#028475]"
                    : "border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
                }
              >
                {i + 1}
              </Button>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
            >
              Next
            </Button>
          </div>
        )}
      </div>
    </section>
  );
}