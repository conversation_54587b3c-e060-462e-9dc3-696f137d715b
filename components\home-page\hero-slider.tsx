"use client"

import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { MainNav } from "@/components/main-nav"
import { ArrowRight, ChevronDown, Pause, Play } from "lucide-react"
import { useState, useEffect } from "react"

const slides = [
  {
    id: 1,
    category: "DIGITAL ENERGY TRADING",
    title: "Transform Your Energy Operations with StreamLnk's Advanced Platform",
    description: "Connect with verified suppliers worldwide and access real-time market data for smarter energy trading decisions",
    primaryButton: "Start Trading",
    secondaryButton: "Explore Platform",
    backgroundImage: "/images/homepage/home page banner image.png",
  },
  {
    id: 2,
    category: "GLOBAL MARKETPLACE",
    title: "Access the World's Largest Network of Energy Suppliers",
    description: "Trade with confidence using our verified supplier network spanning 50+ countries with complete transparency",
    primaryButton: "Join Network",
    secondaryButton: "View Suppliers",
    backgroundImage: "/images/homepage/home page banner image.png",
  },
  {
    id: 3,
    category: "SMART ANALYTICS",
    title: "AI-Powered Market Intelligence for Strategic Energy Trading",
    description: "Leverage advanced analytics and predictive insights to optimize your energy procurement and trading strategies",
    primaryButton: "View Analytics",
    secondaryButton: "Learn More",
    backgroundImage: "/images/homepage/home page banner image.png",
  },
  {
    id: 4,
    category: "SECURE TRANSACTIONS",
    title: "Trade Energy with Complete Security and Regulatory Compliance",
    description: "Benefit from blockchain-secured transactions and automated compliance monitoring across all energy markets",
    primaryButton: "Secure Trading",
    secondaryButton: "View Security",
    backgroundImage: "/images/homepage/home page banner image.png",
  },
]

export function HeroSlider() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)

  useEffect(() => {
    if (!isPlaying) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [isPlaying])

  const handleSlideClick = (index: number) => {
    setCurrentSlide(index)
  }

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying)
  }

  const currentSlideData = slides[currentSlide]
  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <Image
          src={currentSlideData.backgroundImage}
          alt="StreamLnk industrial background"
          fill
          className="object-cover transition-opacity duration-1000"
          priority
        />
        {/* Dark overlay */}
        <div className="absolute inset-0 bg-black/40" />
      </div>

      {/* Content */}
      <div className="relative z-10 flex flex-col min-h-screen">
        {/* Main Navigation */}
        <MainNav />

        {/* Hero Content */}
        <main className="flex-1 flex items-center">
          <div className="px-6 lg:px-12 w-full">
            <div className="max-w-4xl">
              {/* Category Label */}
              <div className="mb-6">
                <span className="text-white/80 text-xs font-medium tracking-widest uppercase transition-all duration-500">
                  {currentSlideData.category}
                </span>
              </div>

              {/* Main Headline */}
              <h1 className="text-white text-4xl md:text-5xl lg:text-6xl font-light leading-tight mb-12 max-w-3xl transition-all duration-500">
                {currentSlideData.title}
              </h1>

              {/* Call to Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Button className="bg-white text-black hover:bg-gray-100 px-6 py-3 text-base font-medium h-auto transition-all duration-300">
                  {currentSlideData.primaryButton}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>

                <Button
                  variant="ctaOutline"
                  className="px-6 py-3 text-base font-medium h-auto transition-all duration-300"
                >
                  {currentSlideData.secondaryButton}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </div>
          </div>
        </main>

        {/* Bottom Content Section */}
        <footer className="w-full">
          <div className="px-6 lg:px-12 pb-8">
            {/* Content Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              {slides.map((slide, index) => (
                <div
                  key={slide.id}
                  className={`space-y-3 cursor-pointer transition-all duration-300 ${
                    index === currentSlide ? "opacity-100" : "opacity-60 hover:opacity-80"
                  }`}
                  onClick={() => handleSlideClick(index)}
                >
                  <div className="relative w-full h-1 bg-white/20 overflow-hidden">
                    <div
                      className={`h-full bg-[#18b793] transition-all duration-300 ${
                        index === currentSlide ? "w-full" : "w-0"
                      }`}
                      style={{
                        animation: index === currentSlide && isPlaying ? "progress 5s linear" : "none",
                      }}
                    />
                  </div>
                  <div className="bg-black/20 backdrop-blur-sm p-4">
                    <p
                      className={`text-sm leading-relaxed transition-all duration-300 ${
                        index === currentSlide ? "text-white" : "text-white/70"
                      }`}
                    >
                      {slide.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Bottom Controls */}
            <div className="flex items-center justify-between">
              <ChevronDown className="w-6 h-6 text-white/60 animate-bounce" />

              <Button
                variant="ghost"
                size="icon"
                className="bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 rounded-full w-12 h-12 transition-all duration-300"
                onClick={togglePlayPause}
              >
                {isPlaying ? <Pause className="w-5 h-5 fill-current" /> : <Play className="w-5 h-5 fill-current ml-0.5" />}
              </Button>
            </div>
          </div>
        </footer>
      </div>

      {/* Progress Animation Styles */}
      <style jsx>{`
        @keyframes progress {
          from {
            width: 0%;
          }
          to {
            width: 100%;
          }
        }
      `}</style>
    </div>
  )
}

