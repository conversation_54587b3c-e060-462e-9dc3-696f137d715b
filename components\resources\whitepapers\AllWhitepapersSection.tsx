"use client";

import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Download, CalendarDays, Tag } from 'lucide-react';

// Placeholder data - in a real application, this would come from a CMS or API
const allWhitepapersData = [
  {
    id: 1,
    coverImage: "/images/whitepapers/cover-ai-polymers.jpg",
    title: "The AI Imperative: Revolutionizing Procurement in the Polymer Industry",
    summary: "This whitepaper explores how artificial intelligence is transforming traditional polymer sourcing, from predictive pricing to automated supplier matching and risk assessment...",
    pdfLink: "/whitepapers/ai-polymer-procurement.pdf",
    publicationDate: "October 26, 2023",
    categories: ["AI & Machine Learning", "Polymers", "Procurement"]
  },
  {
    id: 2,
    coverImage: "/images/whitepapers/cover-resilient-supply-chains.jpg",
    title: "Building Resilient Supply Chains: A Framework for Post-Pandemic Industrial Trade",
    summary: "Learn key strategies for enhancing visibility, diversifying sourcing, and leveraging digital platforms like StreamLnk to build more robust and agile global supply chains...",
    pdfLink: "/whitepapers/resilient-supply-chains.pdf",
    publicationDate: "September 15, 2023",
    categories: ["Supply Chain Digitization", "Risk Management"]
  },
  {
    id: 3,
    coverImage: "/images/whitepapers/cover-data-goldmine.jpg",
    title: "The Data Goldmine: Monetizing Insights from B2B Industrial Marketplaces",
    summary: "An exploration of how platforms like StreamLnk are creating new value through data analytics, benchmarking (StreamIndex™), and predictive intelligence...",
    pdfLink: "/whitepapers/data-goldmine-b2b.pdf",
    publicationDate: "August 02, 2023",
    categories: ["Market Analysis", "B2B E-commerce"]
  },
  {
    id: 4,
    coverImage: "/images/whitepapers/cover-sustainable-sourcing.jpg", // Example without cover
    title: "Sustainable Sourcing in Chemicals: A Practical Guide for ESG Compliance",
    summary: "Navigating the complexities of sustainable chemical sourcing, focusing on ESG reporting, ethical practices, and circular economy principles within the StreamLnk network.",
    pdfLink: "/whitepapers/sustainable-sourcing-chemicals.pdf",
    publicationDate: "July 11, 2023",
    categories: ["Sustainable Sourcing & ESG", "Chemicals", "Compliance"]
  },
  {
    id: 5,
    coverImage: "/images/whitepapers/cover-logistics-optimization.jpg",
    title: "Optimizing Logistics in Bulk Material Transport: Cost Reduction and Efficiency Gains",
    summary: "Strategies for leveraging technology and data to optimize bulk material logistics, reduce transportation costs, and improve delivery times.",
    pdfLink: "/whitepapers/logistics-optimization-bulk.pdf",
    publicationDate: "June 05, 2023",
    categories: ["Logistics & Freight Optimization", "Market Analysis"]
  }
];

interface WhitepaperListItemProps {
  whitepaper: typeof allWhitepapersData[0];
}

function WhitepaperListItem({ whitepaper }: WhitepaperListItemProps) {
  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden flex flex-col md:flex-row border border-gray-200 hover:shadow-xl transition-shadow duration-300">
      {whitepaper.coverImage && (
        <div className="md:w-1/4 relative h-48 md:h-auto flex-shrink-0">
          <Image 
            src={whitepaper.coverImage}
            alt={`Cover image for ${whitepaper.title}`}
            layout="fill"
            objectFit="cover"
            className="bg-gray-200"
          />
        </div>
      )}
      <div className={`p-6 flex flex-col flex-grow ${whitepaper.coverImage ? 'md:w-3/4' : 'w-full'}`}>
        <h3 className="text-xl font-semibold text-[#004235] mb-2 leading-tight">{whitepaper.title}</h3>
        <div className="flex items-center text-xs text-gray-500 mb-3">
          {whitepaper.publicationDate && (
            <div className="flex items-center mr-4">
              <CalendarDays className="h-3.5 w-3.5 mr-1.5 text-[#028475]" />
              <span>{whitepaper.publicationDate}</span>
            </div>
          )}
          {whitepaper.categories && whitepaper.categories.length > 0 && (
            <div className="flex items-center flex-wrap">
              <Tag className="h-3.5 w-3.5 mr-1.5 text-[#028475]" />
              {whitepaper.categories.map((cat, idx) => (
                <span key={idx} className="mr-1.5 bg-gray-100 px-1.5 py-0.5 rounded text-xs">
                  {cat}
                </span>
              ))}
            </div>
          )}
        </div>
        <p className="text-gray-600 text-sm mb-4 flex-grow leading-relaxed">{whitepaper.summary}</p>
        <Button 
          variant="outline"
          className="mt-auto border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white transition-colors w-full md:w-auto md:self-start"
          asChild
        >
          <Link href={whitepaper.pdfLink} target="_blank" rel="noopener noreferrer">
            <Download className="mr-2 h-4 w-4" />
            DOWNLOAD WHITEPAPER
          </Link>
        </Button>
      </div>
    </div>
  );
}

export default function AllWhitepapersSection() {
  // Basic pagination logic (can be expanded)
  const currentPage = 1;
  const itemsPerPage = 5; // Or make this dynamic
  const paginatedWhitepapers = allWhitepapersData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <section id="all-whitepapers" className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-3">
            All Whitepapers
          </h2>
          <div className="w-20 h-1 bg-[#028475] mx-auto"></div>
        </div>

        <div className="grid grid-cols-1 gap-8 mb-12">
          {paginatedWhitepapers.map((paper) => (
            <WhitepaperListItem key={paper.id} whitepaper={paper} />
          ))}
        </div>

        {/* Placeholder for Pagination Controls */}
        {allWhitepapersData.length > itemsPerPage && (
          <div className="flex justify-center items-center space-x-2 mt-12">
            <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white">Previous</Button>
            {/* Add page numbers here */}
            <span className='text-gray-700'>Page {currentPage} of {Math.ceil(allWhitepapersData.length / itemsPerPage)}</span>
            <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white">Next</Button>
          </div>
        )}
      </div>
    </section>
  );
}