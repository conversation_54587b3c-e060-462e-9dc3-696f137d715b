export default function ProcessSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            A Secure Path to Platform Access
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            The KYC/AML verification process is integrated into user onboarding:
          </p>

          <div className="space-y-4 max-w-3xl mx-auto">
            {[
                "User Registration: Applicant completes the online registration form for the relevant StreamLnk portal.",
                "Document Upload: Submits all required identification and business verification documents through our secure portal.",
                "Automated Checks: System performs initial automated verifications and watchlist screenings.",
                "Compliance Team Review: StreamLnk compliance specialists review the application and flagged items.",
                "Approval & Activation: Upon successful verification, the account is approved, and full platform access is granted. Users are notified of their iScore™ baseline.",
                "Ongoing Compliance: Users are required to maintain up-to-date documentation to retain active status."
              ].map((step, index) => (
              <div key={index} className="flex items-start p-4 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-[#028475] text-white flex items-center justify-center mr-3">
                  {index + 1}
                </div>
                <p className="text-gray-800">{step}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}