"use client"

import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import HeroSection from "@/components/industries/life-sciences-healthcare/HeroSection"
import ChallengesSection from "@/components/industries/life-sciences-healthcare/ChallengesSection"
import SolutionsSection from "@/components/industries/life-sciences-healthcare/SolutionsSection"
import CommitmentSection from "@/components/industries/life-sciences-healthcare/CommitmentSection"
import BenefitsSection from "@/components/industries/life-sciences-healthcare/BenefitsSection"
import CTASection from "@/components/industries/life-sciences-healthcare/CTASection"

export default function LifeSciencesHealthcarePage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <ChallengesSection />
      <SolutionsSection />
      <CommitmentSection />
      <BenefitsSection />
      <CTASection />

      <MainFooter />
    </div>
  )
}