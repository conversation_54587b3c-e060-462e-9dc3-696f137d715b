import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/portal-features/reporting/HeroSection";
import ProblemStatementSection from "@/components/portal-features/reporting/ProblemStatementSection";
import ComprehensiveReportingSection from "@/components/portal-features/reporting/ComprehensiveReportingSection";
import CommonFunctionalitiesSection from "@/components/portal-features/reporting/CommonFunctionalitiesSection";
import BenefitsSection from "@/components/portal-features/reporting/BenefitsSection";
import CTASection from "@/components/portal-features/reporting/CTASection";

export default function PortalReportingPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ProblemStatementSection />

      <ComprehensiveReportingSection />

      <CommonFunctionalitiesSection />

      <BenefitsSection />

      <CTASection />

      <BottomFooter />
    </div>
  );
}