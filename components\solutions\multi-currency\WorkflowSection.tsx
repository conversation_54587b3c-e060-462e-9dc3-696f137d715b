export default function WorkflowSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Seamless Flow from Global Quote to Local Settlement
          </h2>
          
          <div className="relative">
            {/* Vertical line */}
            <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-[#028475] hidden md:block"></div>
            
            <div className="space-y-8">
              {[
                {
                  step: "Supplier (E-Stream)",
                  description: "Lists product in EUR."
                },
                {
                  step: "Buyer (MyStreamLnk - USA)",
                  description: "Views product with indicative price in USD (converted at a near-real-time rate)."
                },
                {
                  step: "Quote & Order",
                  description: "Final transaction agreed in EUR (or USD, as negotiated). Invoice generated in the agreed currency."
                },
                {
                  step: "Buyer Payment",
                  description: "Buyer in USA pays the USD equivalent (if invoiced in USD) or initiates a EUR wire transfer to StreamLnk's designated multi-currency account."
                },
                {
                  step: "StreamLnk Treasury",
                  description: "Receives funds, manages FX conversion (if necessary) transparently through partners."
                },
                {
                  step: "Supplier Payout",
                  description: "Supplier in Europe receives payout in EUR to their local bank account, minus StreamLnk platform fees."
                },
                {
                  step: "All Parties",
                  description: "View clear transaction records and FX details (where applicable) in their respective portals."
                }
              ].map((item, index) => (
                <div key={index} className="flex">
                  <div className="md:w-1/3 pr-4 font-semibold text-[#004235] flex items-start">
                    <div className="bg-[#028475] text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 flex-shrink-0 relative z-10">
                      {index + 1}
                    </div>
                    <span>{item.step}</span>
                  </div>
                  <div className="md:w-2/3 text-gray-700">
                    {item.description}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}