"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useSearchParams } from "next/navigation"
import { Eye, EyeOff, AlertCircle, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { supabase, isSupabaseConfigured } from "@/lib/supabase"

export function LoginForm() {
  const searchParams = useSearchParams()
  const portalParam = searchParams.get("portal")
  
  // Format portal name for display
  const getPortalName = () => {
    if (!portalParam) return "Portal"
    
    // Convert kebab-case to readable format
    return portalParam
      .split("-")
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")
  }
  
  const [showPassword, setShowPassword] = useState(false)
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [token, setToken] = useState("")
  const [useToken, setUseToken] = useState(true) // Always require token authentication
  const [rememberMe, setRememberMe] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isConfigured, setIsConfigured] = useState(true)
  const [loginSuccess, setLoginSuccess] = useState(false)
  const [errors, setErrors] = useState<{
    username?: string
    password?: string
    token?: string
    general?: string
  }>({})
  
  useEffect(() => {
    // Check if Supabase is properly configured
    setIsConfigured(isSupabaseConfigured())
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    const newErrors: {
      username?: string
      password?: string
      token?: string
      general?: string
    } = {}

    // Validate fields
    if (!username) {
      newErrors.username = "Please tell us your username."
    }

    if (!password) {
      newErrors.password = "Please enter your password."
    }

    if (!token) {
      newErrors.token = "Please tell us your token code."
    }

    setErrors(newErrors)

    // If there are errors, don't proceed
    if (Object.keys(newErrors).length > 0) {
      return
    }

    // Check if Supabase is properly configured
    if (!isConfigured) {
      setErrors({
        general: "Authentication service is not properly configured. Please contact support."
      })
      return
    }

    setIsLoading(true)
    setErrors({})

    try {
      // Use Supabase Auth to sign in with email and password
      const { data, error } = await supabase.auth.signInWithPassword({
        email: username, // Using username field for email
        password: password,
      })

      if (error) {
        console.error("Login error:", error)
        setErrors({
          general: error.message || "Failed to sign in. Please check your credentials and try again."
        })
        return
      }

      // Always verify token since it's mandatory
      // Here you would implement 2FA verification
      // This is a placeholder for actual 2FA implementation
      console.log("Verifying 2FA token:", token)

      // Handle successful login
      console.log("Login successful", data)
      setLoginSuccess(true)
      
      // If remember me is checked, you can set a longer session
      if (rememberMe) {
        // Set a cookie or local storage item to remember the user
        localStorage.setItem("rememberUser", "true")
      }

      // Redirect to dashboard or specific portal page after successful login
      // For now, we'll just show a success message
      setTimeout(() => {
        // If a specific portal was requested, we could redirect to a portal-specific dashboard
        if (portalParam) {
          window.location.href = `/dashboard?portal=${portalParam}`
        } else {
          window.location.href = "/dashboard"
        }
      }, 1500)
    } catch (err) {
      console.error("Unexpected error during login:", err)
      setErrors({
        general: "An unexpected error occurred. Please try again later."
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-2xl p-8">
      {portalParam && (
        <h1 className="text-2xl font-bold text-[#004235] mb-6 text-center">
          {getPortalName()} Login
        </h1>
      )}
      {!isConfigured && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 flex items-start mb-6">
          <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" />
          <div>
            <h3 className="text-sm font-medium text-yellow-800">Configuration Required</h3>
            <p className="text-sm text-yellow-700 mt-1">
              The authentication service is not properly configured. This is a development environment issue.
            </p>
            <p className="text-sm text-yellow-700 mt-1">
              Please set the NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY environment variables.
            </p>
          </div>
        </div>
      )}
      
      {loginSuccess ? (
        <div className="bg-green-50 border border-green-200 rounded-md p-4 flex items-start">
          <div className="flex-1 text-center">
            <h3 className="text-lg font-medium text-green-800">Login Successful!</h3>
            <p className="text-sm text-green-700 mt-1">Redirecting you to your dashboard...</p>
            <div className="flex justify-center mt-4">
              <Loader2 className="h-8 w-8 text-green-500 animate-spin" />
            </div>
          </div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="username" className="text-gray-600">
            Username
          </Label>
          <div className="relative">
            <Input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className={`w-full ${errors.username ? "border-red-500" : ""}`}
              placeholder="Enter username"
            />
            {errors.username && (
              <div className="flex items-center mt-1 text-red-500 text-sm">
                <AlertCircle className="h-4 w-4 mr-1" />
                <span>{errors.username}</span>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="password" className="text-gray-600">
            Password
          </Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className={`w-full pr-10 ${errors.password ? "border-red-500" : ""}`}
              placeholder="Enter password"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-blue-500 hover:text-blue-700"
            >
              {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>
            {errors.password && (
              <div className="flex items-center mt-1 text-red-500 text-sm">
                <AlertCircle className="h-4 w-4 mr-1" />
                <span>{errors.password}</span>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="token" className="text-gray-600">
            Token
          </Label>
          <div className="relative">
            <Input
              id="token"
              type="text"
              value={token}
              onChange={(e) => setToken(e.target.value)}
              className={`w-full ${errors.token ? "border-red-500" : ""}`}
              placeholder="Enter token code"
            />
            {errors.token && (
              <div className="flex items-center mt-1 text-red-500 text-sm">
                <AlertCircle className="h-4 w-4 mr-1" />
                <span>{errors.token}</span>
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="remember"
              checked={rememberMe}
              onCheckedChange={(checked) => setRememberMe(checked as boolean)}
            />
            <label htmlFor="remember" className="text-sm text-gray-600 cursor-pointer">
              Remember me
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="token-checkbox"
              checked={true}
              disabled={true}
            />
            <label htmlFor="token-checkbox" className="text-sm text-gray-600">
              Token authentication required
            </label>
          </div>
        </div>

        {errors.general && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3 flex items-start mt-2">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
            <p className="text-sm text-red-700">{errors.general}</p>
          </div>
        )}

        <Button 
          type="submit" 
          className="w-full bg-gradient-to-r from-[#004235] to-[#07BC94] hover:opacity-90"
          disabled={isLoading || !isConfigured}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Signing in...
            </>
          ) : (
            "Sign in"
          )}
        </Button>

        <div className="space-y-2">
          <Link href="/forgot-credentials" className="block text-center text-sm text-[#07BC94] hover:underline">
            Forgot username/password?
          </Link>
          <Link href="/signup" className="block text-center text-sm text-[#07BC94] hover:underline">
            Not Enrolled? Sign Up Now.
          </Link>
        </div>
      </form>
      )}
    </div>
  )
}

