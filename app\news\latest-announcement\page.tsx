"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import NewsSubscriptionCtaSection from "@/components/news/NewsSubscriptionCtaSection";
import PressCenterLinkSection from "@/components/news/PressCenterLinkSection";
import Image from "next/image";
import Link from "next/link";
import { ChevronRight, CalendarDays, UserCircle } from "lucide-react";

export default function LatestAnnouncementPage() {
  const pageTitle = "StreamLnk Celebrates Surpassing 10,000 Verified Businesses in Its Global Ecosystem | StreamLnk News";
  const metaDescription = "StreamLnk announces a major milestone, connecting over 10,000 verified industrial businesses worldwide and accelerating the digital transformation of global supply chains.";

  return (
    <>
      {/* SEO Meta Tags - Next.js handles this via Head component or metadata export in app router */}
      {/* For App Router, we'd typically export a metadata object, but for simplicity in a single file component: */}
      <head>
        <title>{pageTitle}</title>
        <meta name="description" content={metaDescription} />
        {/* Add other meta tags like Open Graph, Twitter Cards if needed */}
      </head>

      <div className="flex flex-col bg-white min-h-screen">
        <MainNav />

        <main className="flex-grow">
          <article className="py-12 md:py-16">
            <div className="container mx-auto px-4">
              {/* Breadcrumbs */}
              <div className="mb-8 text-sm text-gray-600 flex items-center flex-wrap">
                <Link href="/" className="hover:text-[#028475]">Home</Link>
                <ChevronRight className="h-4 w-4 mx-1" />
                <Link href="/press-center" className="hover:text-[#028475]">Press Center & Media</Link>
                <ChevronRight className="h-4 w-4 mx-1" />
                <Link href="/news" className="hover:text-[#028475]">News</Link>
                <ChevronRight className="h-4 w-4 mx-1" />
                <span className="text-gray-500">StreamLnk Celebrates Surpassing 10,000 Verified Businesses</span>
              </div>

              {/* Article Header */}
              <header className="mb-8 md:mb-12">
                <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#004235] mb-4 leading-tight">
                  StreamLnk Ecosystem Expands, Connecting Over 10,000 Verified Industrial Businesses Worldwide
                </h1>
                <p className="text-lg md:text-xl text-gray-700 mb-6">
                  Platform achieves significant user milestone, underscoring growing demand for integrated, AI-powered solutions in global supply chain management.
                </p>
                <div className="flex flex-wrap items-center text-sm text-gray-600 space-x-4">
                  <div className="flex items-center">
                    <CalendarDays className="h-4 w-4 mr-1.5 text-[#028475]" />
                    <span>[October 26, 2023]</span>
                  </div>
                  <div className="flex items-center">
                    <UserCircle className="h-4 w-4 mr-1.5 text-[#028475]" />
                    <span>By StreamLnk Communications Team</span>
                  </div>
                </div>
              </header>

              {/* Featured Image Placeholder */}
              <div className="mb-8 md:mb-12 aspect-video w-full max-w-4xl mx-auto bg-gray-200 rounded-lg overflow-hidden shadow-lg">
                <Image 
                  src="/images/placeholder-celebration-10k.jpg" // Replace with actual image path
                  alt="Celebratory graphic showing 10,000+ businesses connected on a global map"
                  width={1200}
                  height={675}
                  className="w-full h-full object-cover"
                  priority
                />
                {/* Fallback text if image doesn't load, or for screen readers */}
                {/* <div className="w-full h-full flex items-center justify-center text-gray-500">
                  Featured Image: Celebratory graphic (10,000+ businesses)
                </div> */}
              </div>

              {/* Article Content */}
              <div className="prose prose-lg max-w-3xl mx-auto text-gray-800 prose-headings:text-[#004235] prose-a:text-[#028475] hover:prose-a:text-[#004235]">
                <p>
                  [London, UK] – StreamLnk Inc., a leader in digitizing global industrial supply chains, today announced a significant milestone: its ecosystem now connects over 10,000 verified businesses across the globe. This achievement marks a pivotal moment in the company's mission to transform industrial trade by making it more efficient, transparent, and trustworthy. The rapid adoption of the StreamLnk platform reflects the increasing appetite among manufacturers, suppliers, logistics providers, and agents for integrated digital solutions that streamline complex international operations.
                </p>

                <h2 className="text-2xl md:text-3xl font-semibold mt-10 mb-4">Driving Digital Adoption Across Industrial Sectors</h2>
                <p>
                  Since its inception, StreamLnk has focused on breaking down the traditional silos in the industrial supply chain. The platform’s growth to over 10,000 verified entities highlights its success in providing a unified environment where businesses can confidently source, sell, finance, and transport materials worldwide. This expansion encompasses diverse sectors, including polymers, industrial chemicals, energy, automotive, packaging, and manufacturing, demonstrating the platform’s broad applicability and value proposition.
                </p>
                <p>
                  StreamLnk's comprehensive approach, which includes its MyStreamLnk buyer portal, E-Stream supplier portal, and integrated logistics solutions like StreamFreight and StreamGlobe+, along with the powerful data insights of StreamResources+ and iScore™ ratings, has resonated with businesses seeking a single source of truth for their global trade activities.
                </p>

                <h2 className="text-2xl md:text-3xl font-semibold mt-10 mb-4">Quotes</h2>
                <blockquote className="border-l-4 border-[#028475] pl-6 py-2 my-6 italic text-gray-700">
                  <p>"Surpassing 10,000 verified businesses is a monumental achievement and a testament to the immense value our integrated ecosystem delivers," said Firas Shallab, Founder & CEO of StreamLnk. "It validates our vision that the future of industrial trade is digital, connected, and intelligent. We are incredibly grateful to our growing community of users and partners who are embracing this transformation with us."</p>
                </blockquote>
                <blockquote className="border-l-4 border-[#028475] pl-6 py-2 my-6 italic text-gray-700">
                  <p>"Our rapid growth is a direct result of solving real pain points for businesses facing fragmented processes, opaque pricing, and complex logistics," added Ahmad Moussa, CTO of StreamLnk. "The demand for efficiency, transparency, and trust in cross-border industrial transactions is stronger than ever, and our AI-powered platform is uniquely positioned to meet that need."</p>
                </blockquote>

                <h2 className="text-2xl md:text-3xl font-semibold mt-10 mb-4">Impact & Future Outlook</h2>
                <p>
                  This milestone signifies not just user growth, but a deepening of the network effect within the StreamLnk ecosystem. With more buyers, suppliers, and service providers interacting on a single platform, the data intelligence becomes richer, the opportunities for optimized matching increase, and the overall efficiency of transactions is further enhanced. StreamLnk remains committed to continuous innovation, with plans for further geographic expansion, new feature development, and deeper AI integration to serve its growing global user base.
                </p>

                <h2 className="text-2xl md:text-3xl font-semibold mt-10 mb-4">About StreamLnk Inc.</h2>
                <p>
                  StreamLnk Inc. is building the world’s first AI-powered, fully integrated B2B ecosystem for the global energy and industrial supply chain. The platform unifies sourcing, logistics, finance, and compliance through specialized portals, leveraging advanced AI and data analytics to provide unparalleled efficiency, transparency, and trust for businesses operating in complex international markets.
                </p>

                <hr className="my-8" />

                <p className="text-center font-semibold text-lg">###</p>

                <div className="mt-8 p-6 bg-[#f3f4f6] rounded-lg">
                  <h3 className="text-xl font-semibold text-[#004235] mb-3">Media Contact:</h3>
                  <p>StreamLnk Communications Team</p>
                  <p>Email: <a href="mailto:<EMAIL>" className="text-[#028475] hover:underline"><EMAIL></a></p>
                  <p><Link href="/press-center" className="text-[#028475] hover:underline">[Link to StreamLnk Press Center]</Link></p>
                </div>
              </div>
            </div>
          </article>

          <NewsSubscriptionCtaSection />
          <PressCenterLinkSection />
        </main>

        <BottomFooter />
      </div>
    </>
  );
}

// Note: For a production app, ensure the placeholder image path '/images/placeholder-celebration-10k.jpg' exists
// or replace it with a valid image URL or a more robust placeholder solution.
// Also, consider using Next.js's metadata API for better SEO management in the app router.