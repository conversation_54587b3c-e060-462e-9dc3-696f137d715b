"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Eye, FileText, ListFilter, Search, TrendingUp, Bell, DownloadCloud, ShieldCheck, BarChartHorizontal, PieChart } from "lucide-react";
import Link from "next/link";

export default function KeyFeaturesSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Exploring the Depths of Partner Reliability
          </h2>
          <p className="text-xl text-gray-700 mb-12 text-center">
            Key Features & Data Visualizations within the iScore™ Tool
          </p>

          {/* iScore™ Search & Directory */}
          <div className="mb-12 p-6 bg-white rounded-lg shadow-md">
            <div className="flex items-center mb-4">
              <Search className="text-[#028475] h-10 w-10 mr-4 flex-shrink-0" />
              <h3 className="text-2xl font-semibold text-[#004235]">iScore™ Search & Directory</h3>
            </div>
            <ul className="list-disc list-inside text-gray-700 space-y-2 mb-4 pl-4">
              <li>Search by company name, StreamLnk ID, service type, or region.</li>
              <li>Results display a summary card: Company Name, Overall iScore™ (e.g., "88/100 - Silver"), Key Strengths (e.g., "Excellent On-Time Delivery").</li>
            </ul>
            <Button 
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
              size="sm"
            >
              {/* Link to be updated if a specific search page exists */}
              View Full iScore™ Profile <Eye className="ml-2 h-4 w-4" />
            </Button>
          </div>

          {/* iScore™ Profile Page */}
          <div className="mb-12 p-6 bg-white rounded-lg shadow-md">
            <div className="flex items-center mb-4">
              <PieChart className="text-[#028475] h-10 w-10 mr-4 flex-shrink-0" /> {/* Changed icon */}
              <h3 className="text-2xl font-semibold text-[#004235]">iScore™ Profile Page</h3>
            </div>
            <ul className="list-disc list-inside text-gray-700 space-y-2 pl-4">
              <li>Overall iScore™: Prominently displayed with a visual rating (e.g., badge, color code).</li>
              <li>Categorical Score Breakdown: Spider chart or bar graphs showing scores for Operational, Compliance, Financial, Communication.</li>
              <li>Key Performance Metrics: Table displaying specific metrics (e.g., "% On-Time Shipments," "Document Compliance Rate").</li>
              <li>Score Trend Graph: Line chart showing the Overall iScore™ over the past 6-12 months.</li>
              <li>Strengths & Areas for Improvement: AI-generated qualitative summaries.</li>
            </ul>
          </div>

          {/* Comprehensive iScore™ Reports */}
          <div className="mb-12 p-6 bg-white rounded-lg shadow-md">
            <div className="flex items-center mb-4">
              <FileText className="text-[#028475] h-10 w-10 mr-4 flex-shrink-0" />
              <h3 className="text-2xl font-semibold text-[#004235]">Comprehensive iScore™ Reports (Premium PDF Download)</h3>
            </div>
            <ul className="list-disc list-inside text-gray-700 space-y-2 mb-4 pl-4">
              <li>Includes all profile page information plus:</li>
              <ul className="list-disc list-inside text-gray-600 space-y-1 mt-1 pl-6">
                <li>Detailed explanation of scoring methodology.</li>
                <li>Historical data for key metrics.</li>
                <li>Anonymized peer benchmarking.</li>
                <li>Summary of any (non-confidential) external data points considered.</li>
              </ul>
            </ul>
            {/* Button for sample report can be added here if needed */}
          </div>

          {/* Watchlist & Alerts */}
          <div className="p-6 bg-white rounded-lg shadow-md">
            <div className="flex items-center mb-4">
              <Bell className="text-[#028475] h-10 w-10 mr-4 flex-shrink-0" />
              <h3 className="text-2xl font-semibold text-[#004235]">Watchlist & Alerts (Premium Feature)</h3>
            </div>
            <ul className="list-disc list-inside text-gray-700 space-y-2 mb-4 pl-4">
              <li>Receive notifications if a watched entity's iScore™ changes significantly.</li>
            </ul>
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white"
              size="sm"
            >
              Add to Watchlist <Eye className="ml-2 h-4 w-4" />
            </Button>
          </div>

        </div>
      </div>
    </section>
  );
}