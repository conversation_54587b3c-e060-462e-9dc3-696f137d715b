import { CareersFooter } from "@/components/careers-footer";
import { CareersMainNav } from "@/components/careers/CareersMainNav";
import { CareersHeroSection } from "@/components/careers/CareersHeroSection";
import { CareersFilterSidebar } from "@/components/careers/CareersFilterSidebar";
import { JobListings } from "@/components/careers/JobListings";




// Main Page Component
export default function CareersPage() {
  return (
    // Removed min-h-screen to allow content to define height if footer is gone
    <div className="flex flex-col bg-white">
      <CareersMainNav />
      <CareersHeroSection />

      {/* Main Content Area - Job Search */}
      {/* Added padding-bottom to compensate for removed footer space */}
      <div className="container mx-auto flex flex-col gap-8 px-4 py-12 md:flex-row lg:gap-12 pb-16 md:pb-24">
        <CareersFilterSidebar />
        <JobListings />
      </div>

      {/* Footer component */}
      <CareersFooter />
    </div>
  );
}