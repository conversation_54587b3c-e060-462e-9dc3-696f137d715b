"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, BarChartHorizontalBig, Clock, Info } from 'lucide-react';

const challenges = [
  {
    icon: <EyeOff className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Lack of Real-Time Pricing",
    description: "No access to objective, real-time pricing benchmarks for specific material grades and regions."
  },
  {
    icon: <BarChartHorizontalBig className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Difficulty Assessing Logistics",
    description: "Trouble in assessing true logistics lane efficiency or comparative carrier reliability."
  },
  {
    icon: <Info className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Inability to Benchmark Costs",
    description: "Cannot accurately benchmark procurement costs or sales pricing against the broader market."
  },
  {
    icon: <AlertTriangle className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Limited Foresight",
    description: "Limited insight into emerging price trends, supply/demand imbalances, or potential supply chain risks."
  },
  {
    icon: <Clock className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Over-Reliance on Outdated Data",
    description: "Dependence on historical data, anecdotal information, or costly, static third-party reports."
  }
];

export default function ChallengeSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            The Challenge: Making Decisions in Opaque Industrial Markets
          </h2>
          <p className="text-xl text-gray-700">
            Operating Without a Clear View of the Market?
          </p>
          <p className="text-lg text-gray-600 mt-2">
            Success in the industrial materials sector hinges on timely and accurate market intelligence. However, businesses often struggle due to:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {challenges.map((challenge, index) => (
            <div key={index} className="bg-[#F2F2F2] p-6 rounded-lg shadow-md flex flex-col items-center text-center">
              {challenge.icon}
              <h3 className="font-semibold text-[#004235] text-lg mb-2">{challenge.title}</h3>
              <p className="text-gray-600 text-sm">{challenge.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}