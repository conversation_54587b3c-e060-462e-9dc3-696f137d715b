import CurrencyCard from "@/components/finance-payments/treasury-dashboard/currency-card"

export default function MultiCurrencySupportSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Multi-Currency Support</h2>
          <p className="text-gray-600 max-w-3xl">
            Comprehensive support for global currencies to facilitate international trade.
          </p>
        </div>

        <div className="grid gap-4 grid-cols-2 md:grid-cols-4 lg:grid-cols-7">
          <CurrencyCard code="USD" name="US Dollar" />
          <CurrencyCard code="EUR" name="Euro" />
          <CurrencyCard code="GBP" name="British Pound" />
          <CurrencyCard code="AED" name="UAE Dirham" />
          <CurrencyCard code="JPY" name="Japanese Yen" />
          <CurrencyCard code="CNY" name="Chinese Yuan" />
          <CurrencyCard code="MXN" name="Mexican Peso" />
        </div>

        <div className="mt-8 text-center">
          <p className="text-gray-600">
            Supports over 25 global currencies with cross-border visibility across regional accounts and subsidiaries.
          </p>
        </div>
      </div>
    </section>
  )
}