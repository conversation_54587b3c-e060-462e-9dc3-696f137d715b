"use client"

import type React from "react"

import { useState } from "react"
import { Upload, FileText, X, CheckCircle2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function UploadForm() {
  const [file, setFile] = useState<File | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [isUploaded, setIsUploaded] = useState(false)

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0]
      setFile(droppedFile)
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0])
    }
  }

  const handleRemoveFile = () => {
    setFile(null)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Simulate upload
    setTimeout(() => {
      setIsUploaded(true)
    }, 1500)
  }

  return (
    <Card className="border-[#f3f4f6]">
      <CardContent className="pt-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="order-id">Order or Invoice ID</Label>
            <Input
              id="order-id"
              placeholder="Enter your order or invoice ID"
              className="border-[#f3f4f6] focus-visible:ring-[#028475]"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="payment-type">Payment Type</Label>
            <Select>
              <SelectTrigger className="border-[#f3f4f6] focus:ring-[#028475]">
                <SelectValue placeholder="Select payment type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="wire">Wire Transfer</SelectItem>
                <SelectItem value="ach">ACH</SelectItem>
                <SelectItem value="bank">Bank Transfer</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Proof of Payment Document</Label>
            <div
              className={`border-2 border-dashed rounded-lg p-6 transition-colors ${
                isDragging ? "border-[#028475] bg-[#028475]/5" : "border-[#f3f4f6]"
              } ${file ? "bg-[#f3f4f6]" : ""}`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              {!file ? (
                <div className="flex flex-col items-center justify-center space-y-4 text-center">
                  <div className="p-3 rounded-full bg-[#004235]/10">
                    <Upload className="h-8 w-8 text-[#028475]" />
                  </div>
                  <div>
                    <p className="text-lg font-medium text-[#004235]">Drag and drop your file here</p>
                    <p className="text-sm text-gray-500 mt-1">or click to browse files</p>
                    <p className="text-xs text-gray-500 mt-2">Supported formats: PDF, PNG, JPG (Max: 10MB)</p>
                  </div>
                  <Input
                    id="file-upload"
                    type="file"
                    className="hidden"
                    accept=".pdf,.png,.jpg,.jpeg"
                    onChange={handleFileChange}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    className="border-[#028475] text-[#028475]"
                    onClick={() => document.getElementById("file-upload")?.click()}
                  >
                    Browse Files
                  </Button>
                </div>
              ) : (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-full bg-[#004235]/10">
                      <FileText className="h-6 w-6 text-[#028475]" />
                    </div>
                    <div>
                      <p className="font-medium text-[#004235]">{file.name}</p>
                      <p className="text-sm text-gray-500">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="text-gray-500 hover:text-red-500"
                    onClick={handleRemoveFile}
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Additional Notes (Optional)</Label>
            <textarea
              id="notes"
              placeholder="Add any additional information about this payment"
              className="w-full min-h-[100px] rounded-md border border-[#f3f4f6] p-3 focus:outline-none focus:ring-2 focus:ring-[#028475]"
            />
          </div>

          {isUploaded ? (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center space-x-3">
              <CheckCircle2 className="h-6 w-6 text-green-500" />
              <div>
                <p className="font-medium text-green-800">Upload Successful!</p>
                <p className="text-sm text-green-600">Your proof of payment has been uploaded successfully.</p>
              </div>
            </div>
          ) : (
            <Button type="submit" className="w-full bg-[#004235] hover:bg-[#004235]/90">
              Upload Proof of Payment
            </Button>
          )}
        </form>
      </CardContent>
    </Card>
  )
}
