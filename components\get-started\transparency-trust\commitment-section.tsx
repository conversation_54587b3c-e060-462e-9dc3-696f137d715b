import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>heck, Lock, FileText, Truck, BookOpen, Brain } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

export default function CommitmentSection() {
  const pillars = [
    {
      icon: <ShieldCheck className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Rigorous Participant Verification (KYC/AML)",
      description: "Every supplier, buyer, agent, and service provider on StreamLnk undergoes a comprehensive verification process to confirm their identity, legitimacy, and basic compliance standing.",
    },
    {
      icon: <BarChartBig className="h-10 w-10 text-[#028475] mb-4" />,
      title: "iScore™ Performance & Reliability Ratings",
      description: "Our proprietary, data-driven iScore™ system provides objective ratings for all platform participants based on their operational performance, compliance history, and feedback, offering you clear indicators of reliability.",
    },
    {
      icon: <SearchCheck className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Transparent Pricing & Cost Breakdowns (StreamIndex™)",
      description: "Access to real-time market price benchmarks (StreamIndex™) for key commodities. Quotes generated through StreamLnk provide clear breakdowns of material costs, logistics, customs, duties, and platform fees, ensuring no hidden charges.",
    },
    {
      icon: <Lock className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Secure Transaction Environment",
      description: "Integrated secure payment gateways, Escrow services, and milestone-based payment options protect both buyers and suppliers. All sensitive data is encrypted and handled according to stringent global data privacy standards.",
    },
    {
      icon: <FileText className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Centralized & Verified Documentation",
      description: "Access to verified supplier certifications and compliance documents. All transaction-related documents (POs, Invoices, B/Ls, CoAs) are stored securely and accessible to relevant parties in their portal's Document Vault. Automated tracking of document expiry dates ensures ongoing compliance.",
    },
    {
      icon: <Truck className="h-10 w-10 text-[#028475] mb-4" />,
      title: "End-to-End Shipment Visibility",
      description: "Real-time tracking of shipments across all logistics legs provides transparency into the entire fulfillment process.",
    },
    {
      icon: <BookOpen className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Clear Platform Policies & Terms",
      description: "Our Terms of Service, Privacy Policy, and operational guidelines are easily accessible and clearly define the rights and responsibilities of all users.",
    },
    {
      icon: <Brain className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Data-Driven Insights for All",
      description: "StreamResources+ provides access (tiered) to market intelligence, helping all participants make more informed, transparent decisions.",
    },
  ];

  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-xl md:text-2xl font-semibold text-[#028475] mb-2">
            Our Foundational Principles: Openness, Verification, Security, and Clarity
          </h2>
          <h3 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            StreamLnk's Commitment: How We Build & Maintain Transparency & Trust
          </h3>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            Transparency and trust are not add-ons at StreamLnk; they are woven into the fabric of our platform and operations. Here’s how we deliver on this commitment:
          </p>
        </div>

        <h4 className="text-2xl font-semibold text-[#004235] mb-8 text-center">Key Pillars of Transparency & Trust:</h4>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {pillars.map((pillar, index) => (
            <Card key={index} className="bg-white shadow-lg hover:shadow-xl transition-shadow duration-300 flex flex-col">
              <CardHeader className="items-center text-center">
                {pillar.icon}
                <CardTitle className="text-xl font-semibold text-[#004235]">{pillar.title}</CardTitle>
              </CardHeader>
              <CardContent className="text-center flex-grow">
                <p className="text-gray-600 text-sm">{pillar.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}