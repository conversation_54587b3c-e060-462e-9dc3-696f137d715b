"use client";

import { ShoppingCart, Zap, Users, BarChartHorizontalBig } from 'lucide-react';

export default function ProcurementSection() {
  return (
    <section className="py-12 md:py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">
            Digitizing Procurement – The Foundation of Efficiency
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center leading-relaxed">
            Manual procurement processes are often plagued by time wastage, suboptimal costs, and limited market access due to fragmented supplier bases and opaque pricing structures. This foundational stage of the supply chain is ripe for digital transformation.
          </p>

          <div className="bg-[#f3f4f6] p-8 rounded-xl shadow-lg">
            <h3 className="text-2xl font-semibold text-[#004235] mb-3">
              StreamLnk Solution: Revolutionizing Procurement
            </h3>
            <p className="text-gray-700 mb-6 leading-relaxed">
              MyStreamLnk and E-Stream digitize the entire procurement lifecycle, offering access to vast, verified global supplier networks. Our AI-assisted quoting provides instant, data-driven cost estimates, dramatically cutting RFQ turnaround times from weeks to mere hours. StreamIndex™ price benchmarks ensure market transparency, while all communication and order history are centralized for easy access and auditability.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="flex items-start">
                <ShoppingCart className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Faster Sourcing</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Access a global network for rapid supplier discovery.</p>
                </div>
              </div>
              <div className="flex items-start">
                <Zap className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Competitive Global Pricing</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Leverage AI and benchmarks for optimal costs.</p>
                </div>
              </div>
              <div className="flex items-start">
                <BarChartHorizontalBig className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Reduced Administrative Burden</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Automate tasks and centralize communication.</p>
                </div>
              </div>
              <div className="flex items-start">
                <Users className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Improved Supplier Relationships</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Foster transparency and efficient collaboration.</p>
                </div>
              </div>
            </div>
            <p className="text-md text-gray-800 font-medium leading-relaxed">
              This digital transformation results in significantly faster sourcing cycles, access to competitive global pricing, a reduced administrative burden, and stronger, more transparent supplier relationships.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}