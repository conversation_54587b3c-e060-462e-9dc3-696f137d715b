import { AlertTriangle } from 'lucide-react'

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Why Risk & Compliance Cannot Be an Afterthought
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Engaging in global industrial trade exposes businesses to a multitude of risks and complex compliance obligations:
          </p>
            
          <div className="space-y-4 max-w-3xl mx-auto">
            {[
              "Counterparty Due Diligence: Difficulty verifying the legitimacy and compliance of international suppliers, buyers, and service providers.",
              "Regulatory Complexity: Navigating diverse and constantly evolving import/export laws, sanctions lists, product safety standards, and environmental regulations across multiple jurisdictions.",
              "Document Management Chaos: Handling and validating a vast array of documents (licenses, certifications, MSDS, COO, B/L, customs declarations) is prone to errors and delays.",
              "Financial & Payment Security: Risk of fraud, payment defaults, and insecure cross-border transactions.",
              "Operational Integrity: Ensuring quality control, ethical sourcing, and adherence to agreed-upon contract terms.",
              "Data Privacy & Security: Protecting sensitive commercial and personal data in a digital environment."
            ].map((challenge, index) => (
              <div key={index} className="flex items-start p-4 bg-gray-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
                <p className="text-gray-800">{challenge}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}