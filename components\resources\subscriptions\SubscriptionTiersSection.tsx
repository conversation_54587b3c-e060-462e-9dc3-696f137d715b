"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Check, ArrowRight, Info, MessageSquare } from "lucide-react";
import Link from "next/link";

const tiers = [
  {
    name: "Market Watcher",
    id: "market-watcher",
    href: "/signup?plan=market-watcher", // Placeholder link
    priceMonthly: "$99 - $299",
    description: "Ideal For: SMEs, individual traders, those needing a high-level market overview.",
    features: [
      "Access to high-level StreamIndex™ summaries (regional price trends, general logistics efficiency).",
      "Monthly curated market commentary reports (PDF).",
      "Basic iScore™ badge visibility for platform participants.",
      "Limited historical data access (e.g., last 3-6 months).",
      "Standard dashboard with pre-set widgets.",
    ],
    cta: "SELECT MARKET WATCHER",
    learnMoreLink: "/resources/subscriptions/market-watcher-details" // Placeholder
  },
  {
    name: "Professional Analyst",
    id: "professional-analyst",
    href: "/signup?plan=professional-analyst", // Placeholder link
    priceMonthly: "$499 - $999",
    description: "Ideal For: Procurement teams, sales strategists, market analysts, mid-sized companies.",
    features: [
      "All of Market Watcher, plus:",
      "Detailed, filterable StreamIndex™ data (drill-down by product grade, Incoterm, specific trade lane).",
      "Interactive charting tools with extended historical data (e.g., 12-24 months).",
      "Ability to compare multiple StreamIndex™ components.",
      "Access to view detailed iScore™ reports (online view & basic PDF download).",
      "Regional demand heatmaps and supply chain risk alerts.",
      "Downloadable aggregated data summaries (CSV).",
      "Bi-weekly specialized insight reports.",
      "Limited DaaS API access (e.g., for key StreamIndex™ data points).",
    ],
    cta: "SELECT PROFESSIONAL ANALYST",
    learnMoreLink: "/resources/subscriptions/professional-analyst-details" // Placeholder
  },
  {
    name: "Enterprise Intelligence Suite",
    id: "enterprise-suite",
    href: "/contact-sales?solution=enterprise-suite", // Placeholder link
    priceMonthly: "Contact Sales",
    description: "Ideal For: Large corporations, financial institutions, government agencies, consultancies.",
    features: [
      "All of Professional Analyst, plus:",
      "Full granularity and raw(er) aggregated data access for StreamIndex™ and iScore™.",
      "Extensive historical data (3+ years).",
      "Full DaaS API access for deep integration into enterprise BI, ERP, or trading systems.",
      "Advanced custom report generation tools within the portal.",
      "Predictive analytics modules (e.g., \"what-if\" scenario planning).",
      "Dedicated Account Manager and Data Analyst support from StreamLnk.",
      "Option for white-labeled dashboards and custom data solutions.",
    ],
    cta: "CONTACT ENTERPRISE SALES",
    learnMoreLink: "/resources/subscriptions/enterprise-suite-details" // Placeholder
  },
];

export default function SubscriptionTiersSection() {
  return (
    <section id="subscription-tiers" className="py-16 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">
            Intelligence Solutions Tailored to Your Business Needs
          </h2>
          <p className="text-xl text-[#028475] font-semibold">
            StreamResources+ Subscription Tiers – Find Your Fit
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8 items-stretch">
          {tiers.map((tier) => (
            <div
              key={tier.id}
              className="flex flex-col rounded-lg shadow-xl bg-white border border-gray-200 overflow-hidden transform hover:scale-105 transition-transform duration-300"
            >
              <div className="p-8 flex-grow">
                <h3 className="text-2xl font-semibold text-[#004235] mb-2">{tier.name}</h3>
                <p className="text-sm text-gray-500 mb-4 h-16">{tier.description}</p>
                <p className="text-4xl font-bold text-[#004235] mb-1">{tier.priceMonthly}</p>
                <p className="text-xs text-gray-500 mb-6">{tier.priceMonthly !== "Contact Sales" ? "per month" : "Custom Pricing"}</p>
                
                <ul role="list" className="space-y-3 text-sm text-gray-600 mb-8 flex-grow">
                  {tier.features.map((feature) => (
                    <li key={feature} className="flex items-start">
                      <Check className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="p-6 bg-gray-50 mt-auto">
                <Button className="w-full bg-[#004235] hover:bg-[#028475] text-white mb-2" size="lg" asChild>
                  <Link href={tier.href}>
                    {tier.cta}
                    {tier.id === 'enterprise-suite' ? <MessageSquare className="ml-2 h-5 w-5" /> : <ArrowRight className="ml-2 h-5 w-5" />}
                  </Link>
                </Button>
                <Button variant="outline" className="w-full border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white" size="lg" asChild>
                  <Link href={tier.learnMoreLink}>
                    LEARN MORE
                    <Info className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}