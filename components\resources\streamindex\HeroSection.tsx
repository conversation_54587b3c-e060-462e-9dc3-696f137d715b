"use client";

import Link from 'next/link';
import Image from 'next/image';
import { But<PERSON> } from '@/components/ui/button';
import { ChevronRight, BarChart3 } from 'lucide-react'; // Added BarChart3 for a more relevant icon

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24"> {/* Changed background to light, adjusted padding */}
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6"> {/* Changed text color */}
              StreamIndex™: Your Real-Time Compass in Industrial Materials
            </h1>
            <p className="text-xl text-gray-700 mb-8"> {/* Changed text color and size */}
              Navigate volatile markets with unprecedented clarity. StreamIndex™, powered by StreamLnk's StreamResources+ data engine, delivers proprietary, AI-driven benchmarks on pricing, logistics, and risk across the global industrial supply chain.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
                size="lg"
                asChild
              >
                <Link href="/contact?subject=StreamIndex+Demo"> {/* Updated subject */}
                  REQUEST DEMO
                  <ChevronRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button
                variant="outline"
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
                size="lg"
                asChild
              >
                <Link href="/solutions/streamresources-plus#streamindex"> {/* Placeholder link */}
                  LEARN MORE
                  <BarChart3 className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/resources/streamindex-hero.webp" // Placeholder - suggest user to replace
              alt="StreamIndex by StreamLnk Hero Image"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: Consider adding a more relevant image or illustration for StreamIndex */}
          </div>
        </div>
      </div>
    </section>
  );
}