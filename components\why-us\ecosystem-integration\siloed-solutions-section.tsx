import { XCircle } from "lucide-react";

export default function SiloedSolutionsSection() {
  const problems = [
    "Separate platforms for product sourcing, freight booking, customs brokerage, and payment processing.",
    "Manual data re-entry between systems, leading to errors and inefficiencies.",
    "Lack of a single source of truth, making end-to-end visibility nearly impossible.",
    "Difficult coordination and communication between different service providers and stakeholders.",
    "Increased operational friction, delays at handoff points, and higher administrative costs.",
    "Missed opportunities for optimization that a holistic view could provide.",
  ];

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Siloed Solutions, Fragmented Results: The Old Way of Trading
          </h2>
          <p className="text-xl text-gray-700">
            The Problem with Disconnected Systems in B2B Trade
          </p>
        </div>
        <div className="max-w-2xl mx-auto">
          <p className="text-lg text-gray-700 mb-8">
            Traditionally, managing industrial trade meant juggling multiple, disconnected tools and providers:
          </p>
          <ul className="space-y-4">
            {problems.map((item, index) => (
              <li key={index} className="flex items-start">
                <XCircle className="h-6 w-6 text-red-500 mr-3 mt-1 flex-shrink-0" />
                <span className="text-gray-700">{item}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  );
}