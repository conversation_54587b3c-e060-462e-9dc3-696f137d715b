"use client";

import { Layers, ShieldCheck, <PERSON>tings, BarChart3, Zap, Package, FileText } from "lucide-react";

const challenges = [
  {
    title: "Diverse Material & Component Sourcing",
    description: "Sourcing a wide variety of specialized raw materials, custom components, and MRO (Maintenance, Repair, Operations) supplies from diverse global suppliers.",
    icon: <Layers className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
  },
  {
    title: "Quality & Specification Adherence",
    description: "Ensuring consistent quality and adherence to tight engineering specifications for all sourced inputs.",
    icon: <ShieldCheck className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
  },
  {
    title: "Complex BOM & Production Coordination",
    description: "Managing complex bills of materials (BOMs) and coordinating multi-stage production processes.",
    icon: <Settings className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
  },
  {
    title: "Price Volatility",
    description: "Price volatility for key metals, plastics, and electronic components.",
    icon: <BarChart3 className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
  },
  {
    title: "Long Lead Times",
    description: "Long lead times for specialized or custom-fabricated parts impacting production schedules.",
    icon: <Zap className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
  },
  {
    title: "Optimal Inventory Management",
    description: "Maintaining optimal inventory levels for thousands of SKUs without tying up excessive capital.",
    icon: <Package className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
  },
  {
    title: "Compliance & Traceability",
    description: "Compliance with industry-specific quality standards (e.g., AS9100, ISO 9001) and material traceability.",
    icon: <FileText className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
  }
];

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Critical Supply Chain Challenges in Engineering & Manufacturing
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Is Your Production Hindered by Sourcing and Logistics Inefficiencies?
          </p>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Engineering and manufacturing enterprises rely on intricate supply chains and precise material inputs, often facing:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {challenges.map((item, index) => (
              <div key={index} className="bg-[#F2F2F2] p-6 rounded-lg">
                <div className="flex items-start mb-4">
                  {item.icon}
                  <div>
                    <h3 className="font-semibold text-[#004235] text-lg">{item.title}</h3>
                    <p className="text-gray-600">{item.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}