import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Transform Data into Decisions: Actionable Intelligence for Industrial Trade
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              In today's volatile global market, data is your most valuable asset. StreamLnk's StreamResources+ provides unparalleled analytics and insights derived from thousands of real-time industrial transactions, empowering you to optimize strategy and outperform the competition.
            </p>
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                Request a Demo
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/solutions/data-analytics/hero-placeholder.svg" // Placeholder image path
              alt="Data Analytics Solutions"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  )
}