import { CheckCircle } from "lucide-react";

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Navigating the Complexities of the Polymer Market?
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            The global polymers and plastics industry, while vast and essential, faces distinct operational and market challenges:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Price Volatility</h3>
                  <p className="text-gray-600">Frequent fluctuations in raw material (e.g., crude oil, natural gas) and resin prices impacting margins.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Complex Supply Chains</h3>
                  <p className="text-gray-600">Multiple grades, additives, and processing stages involve numerous stakeholders.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Fragmented Supplier Base</h3>
                  <p className="text-gray-600">Difficulty finding reliable suppliers for specific grades or recycled/bio-based polymers.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Logistics Nuances</h3>
                  <p className="text-gray-600">Managing bulk vs. bagged shipments, specialized handling for certain polymers, and international freight complexities.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Quality & Compliance</h3>
                  <p className="text-gray-600">Ensuring adherence to technical specifications, certifications (e.g., FDA for food-grade), and regulations like REACH.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Inventory Management</h3>
                  <p className="text-gray-600">Balancing stock levels for diverse grades against fluctuating demand and lead times.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg col-span-1 md:col-span-2">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Sustainability Pressures</h3>
                  <p className="text-gray-600">Increasing demand for recycled content, bio-polymers, and transparent ESG reporting.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}