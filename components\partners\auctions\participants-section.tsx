import { Users, Building2, User<PERSON>heck } from "lucide-react"

export default function ParticipantsSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">Who Can Participate and Benefit?</h2>
          <p className="text-lg text-gray-700">
            StreamLnk Auctions are designed for key players in the industrial supply chain.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <div className="rounded-full bg-[#028475]/10 w-16 h-16 flex items-center justify-center mb-4 mx-auto">
              <Building2 className="h-8 w-8 text-[#028475]" />
            </div>
            <h3 className="text-xl font-semibold text-[#004235] mb-3 text-center">Suppliers (via E-Stream)</h3>
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>
                  Offer slow-moving inventory, off-grade materials, or end-of-year surplus at competitive, market-driven
                  prices.
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>Ideal for flash sales, financial optimization strategies, or clearing warehouse space.</span>
              </li>
            </ul>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm">
            <div className="rounded-full bg-[#028475]/10 w-16 h-16 flex items-center justify-center mb-4 mx-auto">
              <Users className="h-8 w-8 text-[#028475]" />
            </div>
            <h3 className="text-xl font-semibold text-[#004235] mb-3 text-center">
              Customers/Buyers (via MyStreamLnk)
            </h3>
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>Bid competitively on time-limited deals for a wide range of industrial products.</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>
                  Gain access to materials with clear visibility into supply type, origin, region, and transaction
                  terms.
                </span>
              </li>
            </ul>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm">
            <div className="rounded-full bg-[#028475]/10 w-16 h-16 flex items-center justify-center mb-4 mx-auto">
              <UserCheck className="h-8 w-8 text-[#028475]" />
            </div>
            <h3 className="text-xl font-semibold text-[#004235] mb-3 text-center">Agents (via MyStreamLnk+)</h3>
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>Invite and guide their client portfolios to participate in relevant auction campaigns.</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#028475] mr-2">•</span>
                <span>Potentially increase commission earnings through facilitated auction transactions.</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-10 max-w-3xl mx-auto text-center">
          <p className="text-gray-700 italic">
            Auctions can be strategically timed (flash-based, strategically-timed) or seasonally forecasted by our AI to
            maximize impact and participation.
          </p>
        </div>
      </div>
    </section>
  )
}
