"use client";

import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"; // Using Leaf as a generic sustainability icon
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function SustainableConstructionSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <Leaf className="h-16 w-16 text-[#028475] mx-auto mb-6" />
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Building a Sustainable Future: Sourcing Green Materials with StreamLnk
          </h2>
          <p className="text-xl text-[#028475] mb-8">
            Focus on Green Building & Sustainable Construction
          </p>
          <p className="text-lg text-gray-700 mb-10">
            StreamLnk supports the growing demand for sustainable construction by:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-left">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <h3 className="font-semibold text-[#004235] text-lg mb-2">Highlighting Green Suppliers</h3>
              <p className="text-gray-600">Highlighting suppliers of recycled content materials, low-carbon cement, sustainably sourced lumber, and other green building products.</p>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <h3 className="font-semibold text-[#004235] text-lg mb-2">Filtering by Certifications</h3>
              <p className="text-gray-600">Enabling buyers to filter materials based on environmental certifications (LEED, BREEAM compatibility data, EPDs).</p>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <h3 className="font-semibold text-[#004235] text-lg mb-2">Tracking Credentials</h3>
              <p className="text-gray-600">Providing tools to track the origin and sustainability credentials of sourced materials for green building reporting.</p>
            </div>
          </div>
          <Button
            variant="outline"
            className="mt-10 border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-6"
            size="lg"
            asChild
          >
            <Link href="/solutions/sustainable-materials"> {/* Assuming this link exists or will be created */}
              Discover Green Solutions
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}