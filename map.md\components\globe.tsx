"use client"

import { useRef, useMemo, useCallback } from "react"
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from "@react-three/fiber"
import { OrbitControls, Sphere, Stars } from "@react-three/drei"
import * as THREE from "three"

// Country positions (approximate lat/lng converted to 3D coordinates)
const countryPositions = {
  US: { lat: 39.8283, lng: -98.5795 },
  CA: { lat: 56.1304, lng: -106.3468 },
  GB: { lat: 55.3781, lng: -3.436 },
  DE: { lat: 51.1657, lng: 10.4515 },
  FR: { lat: 46.2276, lng: 2.2137 },
  JP: { lat: 36.2048, lng: 138.2529 },
  AU: { lat: -25.2744, lng: 133.7751 },
  BR: { lat: -14.235, lng: -51.9253 },
  IN: { lat: 20.5937, lng: 78.9629 },
  CN: { lat: 35.8617, lng: 104.1954 },
  RU: { lat: 61.524, lng: 105.3188 },
  ZA: { lat: -30.5595, lng: 22.9375 },
}

function latLngToVector3(lat: number, lng: number, radius = 2.02) {
  const phi = (90 - lat) * (Math.PI / 180)
  const theta = (lng + 180) * (Math.PI / 180)

  return [
    -radius * Math.sin(phi) * Math.cos(theta),
    radius * Math.cos(phi),
    radius * Math.sin(phi) * Math.sin(theta),
  ] as [number, number, number]
}

function CountryMarker({
  countryCode,
  position,
  isSelected,
  onSelect,
}: {
  countryCode: string
  position: [number, number, number]
  isSelected: boolean
  onSelect: (countryCode: string, screenPosition: { x: number; y: number }) => void
}) {
  const meshRef = useRef<THREE.Mesh>(null)
  const { camera, gl } = useThree()

  const handleClick = useCallback(
    (event: THREE.Event) => {
      event.stopPropagation()

      // Convert 3D position to screen coordinates
      const vector = new THREE.Vector3(...position)
      vector.project(camera)

      const x = (vector.x * 0.5 + 0.5) * gl.domElement.clientWidth
      const y = (vector.y * -0.5 + 0.5) * gl.domElement.clientHeight

      onSelect(countryCode, { x, y })
    },
    [countryCode, position, camera, gl, onSelect],
  )

  useFrame((state) => {
    if (meshRef.current) {
      const scale = isSelected ? 1.8 : 1.2
      meshRef.current.scale.setScalar(scale)

      // Add a subtle pulsing animation for selected markers
      if (isSelected) {
        const pulse = Math.sin(state.clock.elapsedTime * 3) * 0.1 + 1
        meshRef.current.scale.setScalar(scale * pulse)
      }
    }
  })

  return (
    <mesh ref={meshRef} position={position} onClick={handleClick}>
      <sphereGeometry args={[0.04, 12, 12]} />
      <meshBasicMaterial color={isSelected ? "#fbbf24" : "#ff6b6b"} transparent opacity={0.9} />
      {/* Outer glow ring */}
      <mesh>
        <ringGeometry args={[0.06, 0.08, 16]} />
        <meshBasicMaterial
          color={isSelected ? "#fbbf24" : "#ff6b6b"}
          transparent
          opacity={0.4}
          side={THREE.DoubleSide}
        />
      </mesh>
    </mesh>
  )
}

function EarthSphere({ onGlobeClick }: { onGlobeClick: () => void }) {
  const earthRef = useRef<THREE.Mesh>(null)

  // Create a procedural Earth-like texture
  const earthTexture = useMemo(() => {
    if (typeof document === "undefined") return null

    const canvas = document.createElement("canvas")
    canvas.width = 1024
    canvas.height = 512
    const context = canvas.getContext("2d")

    if (!context) return null

    // Create gradient for ocean (blue base)
    const oceanGradient = context.createLinearGradient(0, 0, 0, canvas.height)
    oceanGradient.addColorStop(0, "#1e40af") // Deep blue at poles
    oceanGradient.addColorStop(0.5, "#2563eb") // Medium blue at equator
    oceanGradient.addColorStop(1, "#1e40af") // Deep blue at poles

    context.fillStyle = oceanGradient
    context.fillRect(0, 0, canvas.width, canvas.height)

    // Add land masses (simplified continents)
    context.fillStyle = "#22c55e" // Green for land

    // North America
    context.fillRect(150, 120, 200, 150)
    context.fillRect(100, 100, 150, 100)

    // South America
    context.fillRect(200, 280, 120, 200)

    // Europe
    context.fillRect(450, 100, 100, 80)

    // Africa
    context.fillRect(480, 180, 120, 200)

    // Asia
    context.fillRect(550, 80, 300, 180)

    // Australia
    context.fillRect(750, 350, 150, 80)

    // Add some texture variation
    context.fillStyle = "#16a34a" // Darker green
    for (let i = 0; i < 50; i++) {
      const x = Math.random() * canvas.width
      const y = Math.random() * canvas.height
      const size = Math.random() * 20 + 5
      context.fillRect(x, y, size, size)
    }

    // Add ice caps
    context.fillStyle = "#f8fafc" // White for ice
    context.fillRect(0, 0, canvas.width, 30) // North pole
    context.fillRect(0, canvas.height - 30, canvas.width, 30) // South pole

    const texture = new THREE.CanvasTexture(canvas)
    texture.wrapS = THREE.RepeatWrapping
    texture.wrapT = THREE.ClampToEdgeWrapping
    return texture
  }, [])

  // Create a more realistic Earth material
  const earthMaterial = useMemo(() => {
    if (!earthTexture) {
      return new THREE.MeshPhongMaterial({
        color: "#2563eb",
        transparent: false,
        shininess: 0.1,
        specular: new THREE.Color(0x111111),
      })
    }

    return new THREE.MeshPhongMaterial({
      map: earthTexture,
      transparent: false,
      shininess: 0.1,
      specular: new THREE.Color(0x111111),
    })
  }, [earthTexture])

  const handleClick = useCallback(
    (event: THREE.Event) => {
      event.stopPropagation()
      onGlobeClick()
    },
    [onGlobeClick],
  )

  useFrame((state) => {
    // Rotate the Earth slowly
    if (earthRef.current) {
      earthRef.current.rotation.y += 0.002
    }
  })

  return (
    <group>
      {/* Main Earth sphere */}
      <Sphere ref={earthRef} args={[2, 64, 64]} onClick={handleClick} material={earthMaterial} />

      {/* Atmosphere glow */}
      <Sphere args={[2.05, 64, 64]}>
        <meshBasicMaterial color="#4fc3f7" transparent opacity={0.1} side={THREE.BackSide} />
      </Sphere>

      {/* Outer atmosphere */}
      <Sphere args={[2.1, 32, 32]}>
        <meshBasicMaterial color="#81d4fa" transparent opacity={0.05} side={THREE.BackSide} />
      </Sphere>
    </group>
  )
}

function GlobeScene({
  onRegionSelect,
  onGlobeClick,
  selectedRegion,
}: {
  onRegionSelect: (countryCode: string, position: { x: number; y: number }) => void
  onGlobeClick: () => void
  selectedRegion: string | null
}) {
  return (
    <>
      {/* Lighting setup for realistic Earth appearance */}
      <ambientLight intensity={0.3} color="#ffffff" />
      <directionalLight position={[5, 3, 5]} intensity={1.2} color="#ffffff" />
      <pointLight position={[-5, -3, -5]} intensity={0.3} color="#4fc3f7" />

      {/* Stars background */}
      <Stars radius={300} depth={60} count={3000} factor={7} saturation={0} fade speed={0.5} />

      <EarthSphere onGlobeClick={onGlobeClick} />

      {Object.entries(countryPositions).map(([countryCode, coords]) => {
        const position = latLngToVector3(coords.lat, coords.lng)
        return (
          <CountryMarker
            key={countryCode}
            countryCode={countryCode}
            position={position}
            isSelected={selectedRegion === countryCode}
            onSelect={onRegionSelect}
          />
        )
      })}

      <OrbitControls
        enableZoom={true}
        enablePan={false}
        minDistance={3}
        maxDistance={10}
        autoRotate
        autoRotateSpeed={0.3}
        dampingFactor={0.05}
        enableDamping
      />
    </>
  )
}

export function Globe({
  onRegionSelect,
  onGlobeClick,
  selectedRegion,
}: {
  onRegionSelect: (countryCode: string, position: { x: number; y: number }) => void
  onGlobeClick: () => void
  selectedRegion: string | null
}) {
  return (
    <div className="w-full h-screen relative" style={{ marginBottom: "-30vh" }}>
      <Canvas
        camera={{ position: [0, 0, 5], fov: 45 }}
        className="w-full h-full"
        gl={{ antialias: true, alpha: true }}
        onCreated={({ gl }) => {
          gl.setClearColor(0x000000, 0)
        }}
      >
        <GlobeScene onRegionSelect={onRegionSelect} onGlobeClick={onGlobeClick} selectedRegion={selectedRegion} />
      </Canvas>
    </div>
  )
}
