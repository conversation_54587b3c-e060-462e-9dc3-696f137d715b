"use client"

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/industries/energy/HeroSection";
import ChallengesSection from "@/components/industries/energy/ChallengesSection";
import SolutionsSection from "@/components/industries/energy/SolutionsSection";
import EnergyTransitionSection from "@/components/industries/energy/EnergyTransitionSection";
import BenefitsSection from "@/components/industries/energy/BenefitsSection";
import CTASection from "@/components/industries/energy/CTASection";

export default function EnergyTradingPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <ChallengesSection />
      <SolutionsSection />
      <EnergyTransitionSection />
      <BenefitsSection />
      <CTASection />

      <BottomFooter />
    </div>
  )
}