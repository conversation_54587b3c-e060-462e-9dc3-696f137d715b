"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Presentation, <PERSON> } from "lucide-react"; // Retain existing icons for now, will remove unused ones in the JSX
import Link from "next/link";

export default function CtaSection() {
  return (
    <section className="py-16 bg-white md:py-24">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
          Stay Updated on Innovations.
        </h2>
        <p className="text-xl text-gray-700 mb-8 max-w-2xl mx-auto">
          Join the Technological Forefront of Industrial Trade. Follow "Delivered by StreamLnk" and explore our resources to keep abreast of the latest technological advancements and how they can benefit your business.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
            size="lg"
            asChild
          >
            <Link href="/request-demo?source=tech-trends-cta">
              REQUEST DEMO
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
          <Button 
            variant="outline" 
            className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
            size="lg"
            asChild
          >
            <Link href="/blog"> {/* Assuming '/blog' or similar for 'Delivered' */}
              LATEST ARTICLES
            </Link>
          </Button>
        </div>
        <div className="mt-6">
          <Button 
            variant="link" 
            className="text-[#028475] hover:text-[#004235]"
            asChild
          >
            <Link href="/solutions/ai-data"> {/* Assuming a consolidated AI & Data solutions page */}
              EXPLORE AI & DATA
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}