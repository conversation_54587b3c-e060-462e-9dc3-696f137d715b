import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ExternalLink } from "lucide-react";

export default function MobileWebAccessSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            In the Meantime: Access StreamLnk via Mobile Web
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Responsive Design for Access Today: While our dedicated apps are under development, all StreamLnk portals
            (MyStreamLnk, E-Stream, etc.) are built with a mobile-responsive design, ensuring you can access key
            features and manage your account effectively using the web browser on your smartphone or tablet.
          </p>
          <Button
            size="lg"
            className="bg-[#028475] hover:bg-[#004235] text-white transition-colors px-8 py-3 text-lg"
            asChild
          >
            <Link href="/login"> {/* Assuming /login is the correct portal login page */}
              Login to Your Portal <ExternalLink className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}