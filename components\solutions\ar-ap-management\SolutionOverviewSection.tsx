"use client"

import { FileText, Bell, AlertOctagon, Upload, Activity, BarChart3, Workflow, CreditCard } from "lucide-react"

export default function SolutionOverviewSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Your Financial Command Center: Automated, Transparent, and Efficient
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            StreamLnk embeds AR and AP management functionalities directly within its portals (MyStreamLnk for buyers' AP, E-Stream for suppliers' AR, and our internal Finance Hub for overall control), automating key processes and providing real-time visibility:
          </p>

          <div className="space-y-8">
            {/* Feature 1 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <FileText className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Automated Invoicing</h3>
                  <p className="text-gray-700">
                    System-generated proforma and final invoices based on confirmed orders and delivery milestones, ensuring accuracy and consistency.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 2 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <Activity className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Centralized Invoice Dashboards</h3>
                  <p className="text-gray-700 mb-2">
                    <span className="font-semibold">Buyers (MyStreamLnk):</span> View all incoming invoices, due dates, payment statuses, and payment history.
                  </p>
                  <p className="text-gray-700">
                    <span className="font-semibold">Suppliers (E-Stream):</span> Track all outgoing invoices (to StreamLnk or directly visible to buyers if structure allows), payment statuses, and expected payout dates.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 3 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <Bell className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Automated Payment Reminders (7/3/1 Rule)</h3>
                  <p className="text-gray-700">
                    System automatically sends polite payment reminders to buyers via email and in-portal notifications 7 days, 3 days, and 1 day before the invoice due date.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 4 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <AlertOctagon className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Overdue Invoice Enforcement & Account Lockout</h3>
                  <p className="text-gray-700">
                    If an invoice becomes overdue beyond agreed terms, the system automatically flags the customer account, alerts StreamLnk management, blocks the customer from placing new orders, and puts any pending shipments for that customer on hold until the overdue balance is settled or a resolution plan is in place.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 5 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <Upload className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Digital Proof of Payment (POP) Upload & Verification</h3>
                  <p className="text-gray-700">
                    Buyers can upload POPs directly to the platform. AI-assisted matching (invoice ID, payer info) can speed up verification by StreamLnk Treasury.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 6 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">AR Aging Reports & Analytics</h3>
                  <p className="text-gray-700">
                    Primarily for StreamLnk internal finance and potentially for premium supplier/agent tiers. Visualize outstanding receivables, identify at-risk accounts, and track DSO.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 7 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <CreditCard className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Integration with Payment Systems</h3>
                  <p className="text-gray-700">
                    Seamless connection with BNPL, Escrow, and global wire transfer mechanisms.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}