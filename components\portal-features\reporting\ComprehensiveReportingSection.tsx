import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

export default function ComprehensiveReportingSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">Data at Your Fingertips: Comprehensive Reporting Across the StreamLnk Ecosystem</h2>
          <p className="text-lg text-gray-700">
            StreamLnk embeds powerful reporting functionalities within each user portal, complemented by the advanced analytics capabilities of StreamResources+ for premium subscribers.
          </p>
        </div>

        <div className="max-w-5xl mx-auto">
          <h3 className="text-2xl font-bold text-[#004235] mb-6 border-b-2 border-[#028475] pb-2 inline-block">Key Reporting Features by Portal Type:</h3>

          {/* MyStreamLnk */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="9" cy="21" r="1"/><circle cx="20" cy="21" r="1"/><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/></svg>
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">MyStreamLnk (Customer Portal):</h4>
            </div>
            <ul className="list-disc pl-8 space-y-2 text-gray-700">
              <li>Order History Reports: Filterable by date, supplier, product, status. Downloadable.</li>
              <li>Spend Analysis Reports: Breakdown of spend by category, supplier, region.</li>
              <li>Supplier Performance Summaries: Aggregated data on on-time delivery, quality ratings (from iScore™).</li>
              <li>Logistics Performance Reports: Track average delivery times, freight costs per lane.</li>
              <li>ESG Sourcing Reports: Summary of sustainable purchases and CO2e impact.</li>
              <li>Payment & Invoice History Reports.</li>
            </ul>
          </div>

          {/* E-Stream */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"/><path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"/></svg>
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">E-Stream (Supplier Portal):</h4>
            </div>
            <ul className="list-disc pl-8 space-y-2 text-gray-700">
              <li>Sales Performance Reports: Revenue, volume, profit margins by product, region, customer type.</li>
              <li>Quote Conversion Rate Analysis.</li>
              <li>Inventory Velocity & Aging Reports.</li>
              <li>Customer Feedback Summaries (Anonymized).</li>
              <li>Logistics & Fulfillment KPI Reports: On-time readiness, shipment accuracy.</li>
              <li>Payout & Fee Statements.</li>
            </ul>
          </div>

          {/* MyStreamLnk+ */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M20 12V8H6a2 2 0 0 1-2-2c0-1.1.9-2 2-2h12v4"/><path d="M4 6v12c0 1.1.9 2 2 2h14v-4"/><path d="M18 12a2 2 0 0 0 0 4h4v-4Z"/></svg>
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">MyStreamLnk+ (Agent Portal):</h4>
            </div>
            <ul className="list-disc pl-8 space-y-2 text-gray-700">
              <li>Commission Reports: Detailed breakdown of earned, pending, and paid commissions by customer/order.</li>
              <li>Portfolio Performance Reports: Sales volume, new customer acquisition, quote conversion rates across their client base.</li>
              <li>Customer Activity Reports.</li>
            </ul>
          </div>

          {/* Logistics Portals */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="1" y="3" width="15" height="13"/><polygon points="16 8 20 8 23 11 23 16 16 16 16 8"/><circle cx="5.5" cy="18.5" r="2.5"/><circle cx="18.5" cy="18.5" r="2.5"/></svg>
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">Logistics Portals (StreamFreight, StreamGlobe+, StreamPak):</h4>
            </div>
            <ul className="list-disc pl-8 space-y-2 text-gray-700">
              <li>Operational KPI Reports: On-time performance, job acceptance rates, document compliance rates, average completion times.</li>
              <li>Financial Reports: Payout statements, invoice histories.</li>
              <li>Compliance Audit Reports: Summary of document validity and adherence to SLAs.</li>
            </ul>
          </div>

          {/* StreamResources+ */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 3v18h18"/><path d="m19 9-5 5-4-4-3 3"/></svg>
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">StreamResources+ (Premium Data Portal):</h4>
            </div>
            <ul className="list-disc pl-8 space-y-2 text-gray-700">
              <li>Access to StreamIndex™ historical data reports.</li>
              <li>Detailed iScore™ analytical reports for any platform participant.</li>
              <li>Custom Report Builder: Create and schedule bespoke reports based on a vast array of data points.</li>
              <li>Market Intelligence Reports: In-depth analyses of industry trends, regional dynamics, and commodity forecasts.</li>
              <li>Data Export Capabilities (CSV, PDF, API for DaaS tiers).</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}