"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/resources/esg-reporting/HeroSection";
import ImperativeSection from "@/components/resources/esg-reporting/ImperativeSection";
import HowStreamLnkEmpowersSection from "@/components/resources/esg-reporting/HowStreamLnkEmpowersSection";
import BenefitsSection from "@/components/resources/esg-reporting/BenefitsSection";
import PartneringCtaSection from "@/components/resources/esg-reporting/PartneringCtaSection";

export default function ESGReportingResourcePage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <ImperativeSection />
      <HowStreamLnkEmpowersSection />
      <BenefitsSection />
      <PartneringCtaSection />

      <BottomFooter />
    </div>
  );
}