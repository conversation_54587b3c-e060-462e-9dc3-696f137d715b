"use client";

import { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { FilterX, Search } from 'lucide-react';

// Define types for filter options for better type safety
interface FilterOption {
  value: string;
  label: string;
}

const roleOptions: FilterOption[] = [
  { value: "all", label: "All Roles" },
  { value: "buyer", label: "Buyers / Manufacturers" },
  { value: "supplier", label: "Suppliers / Producers" },
  { value: "agent", label: "Agents / Distributors" },
  { value: "freight-carrier", label: "Freight Carriers (Land/Rail)" },
  { value: "sea-freight", label: "Sea Freight Partners" },
  { value: "customs-agent", label: "Customs Agents" },
  { value: "warehouse-provider", label: "Packaging / Warehouse Providers" },
];

const industryOptions: FilterOption[] = [
  { value: "all", label: "All Industries" },
  { value: "polymers-plastics", label: "Polymers & Plastics" },
  { value: "industrial-chemicals", label: "Industrial Chemicals" },
  { value: "energy", label: "Energy" },
  { value: "automotive", label: "Automotive" },
  { value: "construction-materials", label: "Construction Materials" },
  // Add other key industries as applicable
];

const benefitOptions: FilterOption[] = [
  { value: "all", label: "All Benefits" },
  { value: "cost-reduction", label: "Cost Reduction" },
  { value: "time-savings", label: "Time Savings / Efficiency" },
  { value: "market-expansion", label: "Market Expansion" },
  { value: "logistics-optimization", label: "Logistics Optimization" },
  { value: "risk-mitigation", label: "Risk Mitigation" },
  { value: "improved-visibility", label: "Improved Visibility" },
  { value: "esg-sustainability", label: "ESG / Sustainability" },
];

interface FilterSectionProps {
  onFilterChange?: (filters: { role: string; industry: string; benefit: string }) => void;
}

export default function FilterSection({ onFilterChange }: FilterSectionProps) {
  const [selectedRole, setSelectedRole] = useState<string>("all");
  const [selectedIndustry, setSelectedIndustry] = useState<string>("all");
  const [selectedBenefit, setSelectedBenefit] = useState<string>("all");

  const handleApplyFilters = () => {
    if (onFilterChange) {
      onFilterChange({
        role: selectedRole,
        industry: selectedIndustry,
        benefit: selectedBenefit,
      });
    }
    // In a real app, you'd likely update a URL query param or trigger a data fetch
    console.log("Applying filters:", { selectedRole, selectedIndustry, selectedBenefit });
  };

  const handleClearFilters = () => {
    setSelectedRole("all");
    setSelectedIndustry("all");
    setSelectedBenefit("all");
    if (onFilterChange) {
      onFilterChange({
        role: "all",
        industry: "all",
        benefit: "all",
      });
    }
    console.log("Filters cleared");
  };

  // Optional: Apply filters automatically when a selection changes
  // useEffect(() => {
  //   handleApplyFilters();
  // }, [selectedRole, selectedIndustry, selectedBenefit]);

  return (
    <section className="py-12 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <h2 className="text-2xl font-semibold text-[#004235] mb-3 text-center">
            Find Stories Relevant to Your Business
          </h2>
          <p className="text-lg text-gray-700 mb-8 text-center">
            Filter & Browse Case Studies
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 items-end">
            <div>
              <Label htmlFor="role-filter" className="text-sm font-medium text-gray-700 mb-1 block">Filter by Role / User Type</Label>
              <Select value={selectedRole} onValueChange={setSelectedRole}>
                <SelectTrigger id="role-filter" className="w-full bg-white border-gray-300">
                  <SelectValue placeholder="Select Role" />
                </SelectTrigger>
                <SelectContent>
                  {roleOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="industry-filter" className="text-sm font-medium text-gray-700 mb-1 block">Filter by Industry</Label>
              <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
                <SelectTrigger id="industry-filter" className="w-full bg-white border-gray-300">
                  <SelectValue placeholder="Select Industry" />
                </SelectTrigger>
                <SelectContent>
                  {industryOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="benefit-filter" className="text-sm font-medium text-gray-700 mb-1 block">Filter by Challenge Solved / Benefit</Label>
              <Select value={selectedBenefit} onValueChange={setSelectedBenefit}>
                <SelectTrigger id="benefit-filter" className="w-full bg-white border-gray-300">
                  <SelectValue placeholder="Select Benefit" />
                </SelectTrigger>
                <SelectContent>
                  {benefitOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={handleApplyFilters}
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
              size="lg"
            >
              APPLY FILTERS
              <Search className="ml-2 h-5 w-5" />
            </Button>
            <Button
              onClick={handleClearFilters}
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
              size="lg"
            >
              CLEAR FILTERS
              <FilterX className="ml-2 h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}