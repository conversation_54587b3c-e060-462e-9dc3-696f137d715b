import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowR<PERSON> } from "lucide-react";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl text-center mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
            The StreamLnk Difference: Seamlessly Integrated, End-to-End.
          </h1>
          <p className="text-xl md:text-2xl text-[#028475] mb-8">
            Don't settle for piecemeal solutions. StreamLnk is the only platform that unifies every critical function of the industrial materials supply chain into one intelligent, interconnected ecosystem. Experience the power of true integration.
          </p>
          <Button 
            size="lg" 
            className="bg-[#004235] hover:bg-[#028475] text-white px-8 py-3 text-lg transition-colors"
            asChild
          >
            <Link href="/request-demo"> 
              SEE ECOSYSTEM IN ACTION
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}