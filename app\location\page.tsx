"use client";

import Link from "next/link";
import { Fragment } from 'react'; // Import Fragment for wrapping multiple elements
import { Button } from "@/components/ui/button";
import { CountrySelector } from "@/components/country-selector";
import { MainNav } from "@/components/main-nav";
// Removed MainFooter import as it wasn't used
import { BottomFooter } from "@/components/bottom-footer";

// Define the country data structure
interface Country {
  name: string;
  href: string;
}

// Group countries by first letter
interface CountryGroup {
  letter: string;
  countries: Country[];
}

// Define all countries alphabetically (using the provided list)
const countries: Country[] = [
  // A
  { name: "Afghanistan", href: "/" },
  { name: "Albania", href: "/" },
  { name: "Algeria", href: "/" },
  { name: "American Samoa", href: "/" },
  { name: "Andorra", href: "/" },
  { name: "Angola", href: "/" },
  { name: "<PERSON><PERSON><PERSON>", href: "/" },
  { name: "Antigua", href: "/" },
  { name: "Argentina", href: "/" },
  { name: "Armenia", href: "/" },
  { name: "Aruba", href: "/" },
  { name: "Australia", href: "/" },
  { name: "Austria", href: "/" },
  { name: "Azerbaijan", href: "/" },
  // B
  { name: "Bahamas", href: "/" },
  { name: "Bahrain", href: "/" },
  { name: "Bangladesh", href: "/" },
  { name: "Barbados", href: "/" },
  { name: "Belgium", href: "/" },
  { name: "Belize", href: "/" },
  { name: "Benin", href: "/" },
  { name: "Bermuda", href: "/" },
  { name: "Bhutan", href: "/" },
  { name: "Bolivia", href: "/" },
  { name: "Bonaire", href: "/" },
  { name: "Bosnia", href: "/" },
  { name: "Botswana", href: "/" },
  { name: "Brazil", href: "/" },
  { name: "Brunei", href: "/" },
  { name: "Bulgaria", href: "/" },
  { name: "Burkina", href: "/" },
  { name: "Faso", href: "/" },
  { name: "Burundi", href: "/" },
  // C
  { name: "Cambodia", href: "/" },
  { name: "Cameroon", href: "/" },
  { name: "Canada", href: "/" },
  { name: "Canary Islands", href: "/" },
  { name: "Cape Verde", href: "/" },
  { name: "Caribbean", href: "/" },
  { name: "Cayman Islands", href: "/" },
  { name: "Central African Republic", href: "/" },
  { name: "Chad", href: "/" },
  { name: "Channel Islands", href: "/" },
  { name: "Colombia", href: "/" },
  { name: "Islands Costa Rica", href: "/" },
  { name: "Croatia", href: "/" },
  { name: "Cuba", href: "/" },
  { name: "Cyprus", href: "/" },
  { name: "Czech Republic", href: "/" },
  // D
  { name: "Denmark", href: "/" },
  { name: "Djibouti", href: "/" },
  { name: "Dominica", href: "/" },
  { name: "Dominican Republic", href: "/" },
  // ... add more countries to match the image exactly if needed
];

// Group countries by first letter
const groupCountriesByLetter = (countries: Country[]): CountryGroup[] => {
  const groups: Record<string, Country[]> = {};

  countries.forEach(country => {
    // Ensure name exists and is not empty before getting charAt(0)
    if (country.name && country.name.length > 0) {
        const firstLetter = country.name.charAt(0).toUpperCase();
        if (!groups[firstLetter]) {
          groups[firstLetter] = [];
        }
        groups[firstLetter].push(country);
    } else {
        // Optional: log countries that are skipped
        // console.warn("Skipping country with empty or missing name:", country);
    }
  });

  return Object.entries(groups)
    .map(([letter, countries]) => ({ letter, countries }))
    .sort((a, b) => a.letter.localeCompare(b.letter));
};

export default function LocationPage() {
  const countryGroups = groupCountriesByLetter(countries);

  return (
    <div className="flex min-h-screen flex-col">
      {/* Assume these components are already styled correctly */}
      <CountrySelector />
      <MainNav />

      <main className="flex-1">
        <div className="container mx-auto py-12 px-4">
          {/* Title styling matching the image */}
          <h1 className="text-4xl font-bold text-center text-[#1a4731] mb-12">Please Select A Location</h1> {/* Used a specific dark green color from the image */}

          <div className="max-w-5xl mx-auto">
            {countryGroups.map((group) => (
              <div key={group.letter} className="mb-8 bg-gray-50 p-6 rounded-lg shadow-sm"> {/* Background, padding, rounding, shadow */}
                <div className="flex items-start">
                  {/* Letter styling matching the image */}
                  <div className="w-12 mr-6 flex-shrink-0">
                    <span className="text-4xl font-bold text-[#1a4731]">{group.letter}</span> {/* Used specific dark green color */}
                  </div>

                  {/* Flex container for countries and separators, matching image flow */}
                  <div className="flex-1 flex flex-wrap items-center gap-x-2 gap-y-1">
                    {group.countries.map((country, index) => (
                      // Use Fragment to group the link and the separator without adding an extra DOM node
                      <Fragment key={country.name}>
                        <Link
                          href={country.href}
                          // Styling for country names matching the image
                          className="text-gray-700 hover:text-[#1a4731] transition-colors duration-150 whitespace-nowrap" // Used gray-700 as base, hover primary green
                        >
                          {country.name}
                        </Link>
                        {/* Conditionally render separator, not after the last item */}
                        {index < group.countries.length - 1 && (
                          <span className="text-gray-400">|</span>
                        )}
                      </Fragment>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Return button styling and centering */}
          <div className="mt-12 text-center">
            <Button variant="outline" asChild>
              <Link href="/">Return to Homepage</Link>
            </Button>
          </div>
        </div>
      </main>

      {/* Assume this component is already styled correctly */}
      <BottomFooter />
    </div>
  );
}