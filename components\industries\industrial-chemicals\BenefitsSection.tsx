import { <PERSON><PERSON><PERSON> } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Streamline Operations, Mitigate Risk, Access Global Markets
          </h2>
          <h3 className="text-xl font-semibold text-[#028475] mb-8">
            Benefits for the Industrial Chemical Industry
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8 text-left">
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h4 className="text-lg font-semibold text-[#004235] mb-3">Simplified Global Sourcing</h4>
              <p className="text-gray-700">Find and vet specialized chemical suppliers worldwide.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h4 className="text-lg font-semibold text-[#004235] mb-3">Enhanced Regulatory Compliance</h4>
              <p className="text-gray-700">Manage documentation and stay updated on requirements more easily.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h4 className="text-lg font-semibold text-[#004235] mb-3">Improved Safety & Handling</h4>
              <p className="text-gray-700">Connect with logistics partners experienced in chemical transport.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h4 className="text-lg font-semibold text-[#004235] mb-3">Greater Price Transparency</h4>
              <p className="text-gray-700">Make informed decisions with StreamIndex™ chemical price benchmarks.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h4 className="text-lg font-semibold text-[#004235] mb-3">Reduced Operational Risk</h4>
              <p className="text-gray-700">Utilize iScore™ and compliance tools to select reliable partners.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h4 className="text-lg font-semibold text-[#004235] mb-3">Efficient Supply Chain Management</h4>
              <p className="text-gray-700">Optimize logistics and reduce lead times for critical chemical inputs.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}