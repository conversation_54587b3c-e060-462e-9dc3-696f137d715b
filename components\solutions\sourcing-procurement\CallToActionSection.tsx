"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export default function CallToActionSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#172d2d] mb-6">
            Ready to Elevate Your Industrial Procurement?
          </h2>
          <p className="text-lg text-[#172d2d] mb-10">
            Join leading manufacturers who are leveraging StreamLnk to build more resilient, efficient, and cost-effective supply chains.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Button
              className="bg-white hover:bg-[#172d2d] hover:text-white text-[#172d2d] border border-[#172d2d] px-6"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                Request Demo
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="outline"
              className="bg-white hover:bg-[#172d2d] hover:text-white text-[#172d2d] border border-[#172d2d] px-6"
              size="lg"
              asChild
            >
              <Link href="/contact-sales">
                Contact Sales
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}