"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowR<PERSON> } from "lucide-react"; // Changed from ArrowDown
import Image from "next/image";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Unlock Your Potential: Discover Partnership Opportunities
            </h1>
            <p className="text-xl text-gray-700 mb-10 leading-relaxed">
              StreamLnk is building a collaborative global ecosystem for industrial trade. We offer a range of partnership programs designed to create mutual value and drive innovation across the supply chain. Find the opportunity that's right for you.
            </p>
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white px-8 py-3 text-lg"
              size="lg"
              asChild
            >
              <Link href="#partnership-programs">
                EXPLORE PROGRAMS
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="relative w-full h-[300px] md:h-[450px] rounded-lg overflow-hidden shadow-xl mt-12 lg:mt-0">
            <Image
              src="/images/placeholder-partner-opportunities.webp" // TODO: User to replace with a relevant image for partnership opportunities
              alt="StreamLnk Partnership Opportunities"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: User to replace with a relevant image for partnership opportunities */}
          </div>
        </div>
      </div>
    </section>
  );
}