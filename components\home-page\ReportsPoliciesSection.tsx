import { ChevronLeft, ChevronRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export function ReportsPoliciesSection() {
  return (
    <div className="bg-white mb-16 md:mb-24">
      {/* Header Section */}
      <section className="px-6 py-24 md:py-32">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-start">
            <div className="max-w-3xl">
              <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">Energy Trading Solutions for Every Sector</h1>
              <p className="text-gray-600 text-lg leading-relaxed">
                From oil & gas to renewable energy, StreamLnk provides specialized trading solutions tailored to your industry's unique requirements and regulatory standards.
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="icon" className="rounded-full border-[#18b793] text-[#18b793] hover:bg-[#18b793] hover:text-white">
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon" className="rounded-full border-[#18b793] text-[#18b793] hover:bg-[#18b793] hover:text-white">
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Service Cards Section */}
      <section className="px-6 pb-24 md:pb-32">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 md:gap-12">
            {/* Oil & Gas Card */}
            <Card className="bg-gradient-to-br from-[#004235]/5 to-[#18b793]/5 border border-[#18b793]/20 p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="mb-6">
                  <div className="w-12 h-12 bg-[#004235] rounded-lg flex items-center justify-center">
                    <div className="grid grid-cols-2 gap-1">
                      {Array.from({ length: 4 }).map((_, i) => (
                        <div key={i} className="w-2 h-2 bg-white rounded-full"></div>
                      ))}
                    </div>
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-[#004235] mb-4">Oil & Gas</h3>
                <p className="text-gray-600 text-sm mb-6">
                  Comprehensive trading solutions for crude oil, natural gas, and refined petroleum products with real-time pricing and logistics coordination.
                </p>
                <button className="text-[#18b793] text-sm font-medium flex items-center gap-2 hover:text-[#004235] transition-colors">
                  <ChevronRight className="h-4 w-4" />
                  Explore oil & gas trading
                </button>
              </CardContent>
            </Card>

            {/* Renewable Energy Card */}
            <Card className="bg-gradient-to-br from-[#18b793]/5 to-[#004235]/5 border border-[#18b793]/20 p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="mb-6">
                  <div className="w-12 h-12 bg-[#18b793] rounded-lg flex items-center justify-center">
                    <div className="grid grid-cols-3 gap-1">
                      {Array.from({ length: 6 }).map((_, i) => (
                        <div key={i} className="w-1.5 h-1.5 bg-white rounded-full"></div>
                      ))}
                    </div>
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-[#004235] mb-4">Renewable Energy</h3>
                <p className="text-gray-600 text-sm mb-6">
                  Trade solar, wind, and other renewable energy certificates with transparent pricing and sustainability tracking.
                </p>
                <button className="text-[#18b793] text-sm font-medium flex items-center gap-2 hover:text-[#004235] transition-colors">
                  <ChevronRight className="h-4 w-4" />
                  Explore renewable trading
                </button>
              </CardContent>
            </Card>

            {/* Petrochemicals Card */}
            <Card className="bg-gradient-to-br from-[#004235]/5 to-[#18b793]/5 border border-[#18b793]/20 p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-[#004235] to-[#18b793] rounded-lg flex items-center justify-center">
                    <div className="grid grid-cols-4 gap-0.5">
                      {Array.from({ length: 12 }).map((_, i) => (
                        <div key={i} className="w-1 h-1 bg-white rounded-full"></div>
                      ))}
                    </div>
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-[#004235] mb-4">Petrochemicals</h3>
                <p className="text-gray-600 text-sm mb-6">
                  Specialized trading platform for chemical feedstocks, polymers, and specialty chemicals with quality assurance and compliance tracking.
                </p>
                <button className="text-[#18b793] text-sm font-medium flex items-center gap-2 hover:text-[#004235] transition-colors">
                  <ChevronRight className="h-4 w-4" />
                  Explore petrochemical trading
                </button>
              </CardContent>
            </Card>

            {/* Power & Utilities Card */}
            <Card className="bg-gradient-to-br from-[#18b793]/5 to-[#004235]/5 border border-[#18b793]/20 p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="mb-6">
                  <div className="w-12 h-12 bg-[#004235] rounded-lg flex items-center justify-center">
                    <div className="grid grid-cols-2 gap-1">
                      {Array.from({ length: 8 }).map((_, i) => (
                        <div key={i} className="w-1.5 h-1.5 bg-white rounded-full"></div>
                      ))}
                    </div>
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-[#004235] mb-4">Power & Utilities</h3>
                <p className="text-gray-600 text-sm mb-6">
                  Electricity trading solutions with grid integration, demand forecasting, and regulatory compliance for utility companies.
                </p>
                <button className="text-[#18b793] text-sm font-medium flex items-center gap-2 hover:text-[#004235] transition-colors">
                  <ChevronRight className="h-4 w-4" />
                  Explore power trading
                </button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}