import type { Metadata } from "next";
import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";
import HeroSection from "@/components/why-us/ecosystem-integration/hero-section";
import SiloedSolutionsSection from "@/components/why-us/ecosystem-integration/siloed-solutions-section";
import HowIntegrationWorksSection from "@/components/why-us/ecosystem-integration/how-integration-works-section";
import IntegrationValueSection from "@/components/why-us/ecosystem-integration/integration-value-section";
import CtaSection from "@/components/why-us/ecosystem-integration/cta-section";

export const metadata: Metadata = {
  title: "Why StreamLnk? The Power of True Ecosystem Integration | StreamLnk",
  description:
    "Discover how StreamLnk's unified ecosystem seamlessly integrates every critical function of the industrial materials supply chain, delivering unmatched efficiency and end-to-end visibility.",
};

export default function EcosystemIntegrationPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <MainNav />
      <main>
        <HeroSection />
        <SiloedSolutionsSection />
        <HowIntegrationWorksSection />
        <IntegrationValueSection />
        <CtaSection />
      </main>
      <MainFooter />
    </div>
  );
}