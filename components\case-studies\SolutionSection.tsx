"use client";

interface SolutionSectionProps {
  portals: string[];
  keyFeaturesUsed: string[];
  descriptionParagraphs: string[];
}

export default function SolutionSection({
  portals,
  keyFeaturesUsed,
  descriptionParagraphs,
}: SolutionSectionProps) {
  return (
    <section className="mb-12">
      <h2 className="text-3xl font-bold text-[#004235] mb-6">
        The StreamLnk Solution
      </h2>
      <p className="text-lg text-gray-700 mb-4 leading-relaxed">
        StreamLnk implemented its {portals.join(' and ')} utilizing key features such as {keyFeaturesUsed.join(', ')}.
      </p>
      {descriptionParagraphs.map((paragraph, index) => (
        <p key={index} className="text-lg text-gray-700 mb-4 leading-relaxed">
          {paragraph}
        </p>
      ))}
    </section>
  );
}