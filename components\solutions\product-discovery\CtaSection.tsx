"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"

export function CtaSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Ready to Find Your Next Material?
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Your Next Industrial Solution is Just a Search Away.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                REQUEST A DEMO OF PRODUCT DISCOVERY
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            
            <Button 
              variant="outline" 
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
              size="lg"
              asChild
            >
              <Link href="/mystreamlnk-signup">
                SIGN UP FOR MYSTREAMLNK
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}