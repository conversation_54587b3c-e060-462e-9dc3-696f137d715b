import React from 'react';
import { TimelineStep, TimelineStepProps } from './timeline-step';

interface StandardizedTimelineProps {
  steps: TimelineStepProps[];
  title: string;
  description: string;
  bgColor?: string;
}

export function StandardizedTimeline({ steps, title, description, bgColor = 'bg-[#F2F2F2]' }: StandardizedTimelineProps) {
  return (
    <section className={`py-16 md:py-24 ${bgColor}`}>
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">{title}</h2>
          <p className="text-gray-600 max-w-3xl">{description}</p>
        </div>

        {/* For steps with icons, use the Timeline component */}
        {steps.some(step => step.icon) ? (
          <div className="max-w-4xl mx-auto">
            <Timeline steps={steps} />
          </div>
        ) : (
          /* For numbered steps, use a grid layout */
          <div className={`grid gap-6 md:grid-cols-${Math.min(steps.length, 5)}`}>
            {steps.map((step, index) => (
              <TimelineStep 
                key={index}
                number={step.number || index + 1}
                title={step.title}
                description={step.description}
                icon={step.icon}
              />
            ))}
          </div>
        )}
      </div>
    </section>
  );
}

// Internal Timeline component for icon-based steps
interface TimelineProps {
  steps: TimelineStepProps[];
}

function Timeline({ steps }: TimelineProps) {
  return (
    <div className="relative">
      {/* Process Steps Line - visible on medium screens and up */}
      <div className="hidden md:block absolute left-1/2 top-0 bottom-0 w-1 bg-[#028475] transform -translate-x-1/2"></div>

      <div className="space-y-12 relative">
        {steps.map((step, index) => (
          <div key={index} className="flex flex-col items-center md:flex-row md:relative md:items-stretch">
            {/* Text Content Box */}
            <div
              className={`w-full max-w-xl md:max-w-none md:w-1/2 ${
                index % 2 === 0
                  ? "md:pr-[56px] md:text-right"
                  : "md:pl-[56px] md:ml-auto"
              } order-2 md:order-none`}
            >
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-xl font-semibold text-[#004235] mb-2">{step.title}</h3>
                <p className="text-gray-700">{step.description}</p>
              </div>
            </div>

            {/* Icon */}
            <div
              className={`order-1 md:order-none my-4 md:my-0 md:absolute md:left-1/2 md:top-1/2 md:transform md:-translate-x-1/2 md:-translate-y-1/2 z-10`}
            >
              <div className="bg-[#004235] rounded-full p-3 shadow-lg">
                {step.icon}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}