"use client";

interface ChallengeSectionProps {
  challengeParagraphs: string[];
}

export default function ChallengeSection({ challengeParagraphs }: ChallengeSectionProps) {
  return (
    <section className="mb-12">
      <h2 className="text-3xl font-bold text-[#004235] mb-6">
        The Challenge
      </h2>
      {challengeParagraphs.map((paragraph, index) => (
        <p key={index} className="text-lg text-gray-700 mb-4 leading-relaxed">
          {paragraph}
        </p>
      ))}
    </section>
  );
}