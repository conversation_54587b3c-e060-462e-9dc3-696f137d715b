// app/industries/industrial-chemicals/page.tsx
import Link from "next/link"
import { MainNav } from "@/components/main-nav"
import { BottomFooter } from "@/components/bottom-footer"
import { Button } from "@/components/ui/button"
import { ArrowRight, Beaker, CheckCircle, FileCheck, Factory, HardHat, Leaf, ShieldCheck, Truck } from "lucide-react"
import HeroSection from "@/components/industries/industrial-chemicals/HeroSection"
import ChallengesSection from "@/components/industries/industrial-chemicals/ChallengesSection"
import SolutionsSection from "@/components/industries/industrial-chemicals/SolutionsSection"
import SafetyComplianceSection from "@/components/industries/industrial-chemicals/SafetyComplianceSection"
import BenefitsSection from "@/components/industries/industrial-chemicals/BenefitsSection"
import CTASection from "@/components/industries/industrial-chemicals/CTASection"

export default function IndustrialChemicalsPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <ChallengesSection />
      <SolutionsSection />
      <SafetyComplianceSection />
      <BenefitsSection />
      <CTASection />

      <BottomFooter />
    </div>
  );
}