import { FileJson, Code, Server, Shield, Users } from "lucide-react"

export function DeveloperToolsSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <div className="mb-12 text-center">
            <h2 className="text-3xl font-bold text-[#004235] mb-4">Developer Access & API Tools</h2>
            <div className="w-20 h-1 bg-[#028475] mx-auto mb-6"></div>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              StreamLnk is committed to providing a robust and accessible development environment
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200 flex flex-col">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235]/10 p-3 rounded-full mr-4">
                  <FileJson className="h-6 w-6 text-[#004235]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Comprehensive API Docs</h3>
              </div>
              <p className="text-gray-700 flex-grow">
                Detailed RESTful API documentation including a sandbox environment for testing and development.
              </p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200 flex flex-col">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235]/10 p-3 rounded-full mr-4">
                  <Code className="h-6 w-6 text-[#004235]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Rate-Limited Endpoints</h3>
              </div>
              <p className="text-gray-700 flex-grow">
                For public integrations, ensuring platform stability and consistent performance for all partners.
              </p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200 flex flex-col">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235]/10 p-3 rounded-full mr-4">
                  <Server className="h-6 w-6 text-[#004235]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Tiered Access Levels</h3>
              </div>
              <p className="text-gray-700 flex-grow">
                Differentiating between strategic partner data exchange and public data access for appropriate resource
                allocation.
              </p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200 flex flex-col">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235]/10 p-3 rounded-full mr-4">
                  <Shield className="h-6 w-6 text-[#004235]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">SLA-Driven Performance</h3>
              </div>
              <p className="text-gray-700 flex-grow">
                Ensuring high availability and reliability, especially for critical payment and freight modules.
              </p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200 flex flex-col md:col-span-2 lg:col-span-1">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235]/10 p-3 rounded-full mr-4">
                  <Users className="h-6 w-6 text-[#004235]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Dedicated Partner Success</h3>
              </div>
              <p className="text-gray-700 flex-grow">
                Support from an integration consultant to guide you through the process and ensure successful
                implementation.
              </p>
            </div>
          </div>

          <div className="mt-12 p-6 bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <Code className="h-6 w-6 text-[#028475] mr-3" />
              <h3 className="text-xl font-bold text-[#004235]">Sample API Endpoint</h3>
            </div>
            <div className="bg-gray-900 text-gray-100 p-4 rounded-md font-mono text-sm overflow-x-auto">
              <pre>
                {`GET /api/v1/market-data/polymer-prices
Authorization: Bearer {your_api_key}

Response:
{
  "data": [
    {
      "product_id": "PET-CLEAR-A",
      "name": "PET Clear Grade A",
      "current_price": 1250.75,
      "currency": "USD",
      "unit": "MT",
      "region": "APAC",
      "last_updated": "2023-04-15T08:30:00Z",
      "trend": "+2.3%"
    },
    ...
  ],
  "meta": {
    "total_count": 128,
    "page": 1,
    "limit": 20
  }
}`}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
