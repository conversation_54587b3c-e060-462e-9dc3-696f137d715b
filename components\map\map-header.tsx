import { Globe, Wifi, WifiOff } from "lucide-react"
import { Card } from "@/components/ui/card"
import type { Partner, Route, LiveAsset } from "@/types/map-types"

interface MapHeaderProps {
  selectedPartner: Partner | null
  selectedRoute: Route | null
  selectedAsset: LiveAsset | null
  isRealTimeEnabled: boolean
}

export function MapHeader({ selectedPartner, selectedRoute, selectedAsset, isRealTimeEnabled }: MapHeaderProps) {
  return (
    <header className="relative z-10 p-6">
      <Card className="glassmorphism p-4 border-slate-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Globe className="w-8 h-8 text-[#52AAA3]" />
            <div>
              <h1 className="text-2xl font-bold text-slate-900">StreamLnk Global Map</h1>
              <p className="text-slate-600 text-sm">StreamLnk Global Map</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            {/* Real-time status indicator */}
            <div className="flex items-center gap-2">
              {isRealTimeEnabled ? (
                <Wifi className="w-4 h-4 text-green-600" />
              ) : (
                <WifiOff className="w-4 h-4 text-red-600" />
              )}
              <span className="text-slate-700 text-sm">{isRealTimeEnabled ? "Live" : "Offline"}</span>
            </div>
            {selectedAsset && (
              <div className="text-right">
                <p className="text-slate-600 text-sm">Selected Asset</p>
                <p className="text-[#52AAA3] font-semibold">{selectedAsset.cargo}</p>
              </div>
            )}
            {selectedRoute && (
              <div className="text-right">
                <p className="text-slate-600 text-sm">Selected Route</p>
                <p className="text-[#52AAA3] font-semibold">
                  {selectedRoute.from} → {selectedRoute.to}
                </p>
              </div>
            )}
            {selectedPartner && (
              <div className="text-right">
                <p className="text-slate-600 text-sm">Selected Partner</p>
                <p className="text-[#52AAA3] font-semibold">{selectedPartner.name}</p>
              </div>
            )}
          </div>
        </div>
      </Card>
    </header>
  )
}
