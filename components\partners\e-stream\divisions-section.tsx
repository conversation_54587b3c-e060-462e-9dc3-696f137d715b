"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { TabsContent, Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Truck, Globe, Package, Users, Database, Briefcase, BarChart3, type LucideIcon } from "lucide-react"

interface DivisionTabData {
  id: string
  title: string
  icon: LucideIcon
}

const divisionTabsData: DivisionTabData[] = [
  {
    id: "customers",
    title: "For Customers",
    icon: Users,
  },
  {
    id: "logistics",
    title: "For Logistics Partners",
    icon: Truck,
  },
  {
    id: "suppliers",
    title: "For Suppliers",
    icon: Package,
  },
]

export function DivisionsSection() {
  const [activeTab, setActiveTab] = useState<string>(divisionTabsData[0].id)
  const [underlineStyle, setUnderlineStyle] = useState({ left: 0, width: 0 })
  const tabsListRef = useRef<HTMLDivElement>(null)
  const tabTriggerRefs = useRef<(HTMLButtonElement | null)[]>([])

  const updateUnderline = useCallback(() => {
    if (tabsListRef.current) {
      const activeTabIndex = divisionTabsData.findIndex((tab) => tab.id === activeTab)
      const activeTabRef = tabTriggerRefs.current[activeTabIndex]

      if (activeTabRef) {
        const tabsListRect = tabsListRef.current.getBoundingClientRect()
        const activeTabRect = activeTabRef.getBoundingClientRect()
        setUnderlineStyle({
          left: activeTabRect.left - tabsListRect.left,
          width: activeTabRect.width,
        })
      }
    }
  }, [activeTab])

  useEffect(() => {
    updateUnderline()
    window.addEventListener("resize", updateUnderline)
    return () => window.removeEventListener("resize", updateUnderline)
  }, [activeTab, updateUnderline])

  // Ensure refs are collected
  useEffect(() => {
    tabTriggerRefs.current = tabTriggerRefs.current.slice(0, divisionTabsData.length)
  }, [])

  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <Badge variant="outline" className="mb-4 border-[#028475] text-[#028475]">
            Our Ecosystem
          </Badge>
          <h2 className="text-3xl font-bold text-[#004235] mb-4">Explore Other StreamLnk Divisions</h2>
          <p className="text-lg text-gray-700 max-w-2xl mx-auto">
            Discover the complete StreamLnk ecosystem with specialized platforms for every part of the global supply
            chain
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList ref={tabsListRef} className="relative flex w-full mb-8 bg-transparent p-0 h-auto rounded-none">
            {divisionTabsData.map((tab, index) => (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                ref={(el) => {
                  tabTriggerRefs.current[index] = el
                }}
                className="relative flex-1 items-center justify-center space-x-2 px-6 py-6 text-[#004235] bg-[#f3f4f6] hover:bg-gray-200 data-[state=active]:bg-white data-[state=active]:shadow-none focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#004235] focus-visible:ring-offset-2 font-medium data-[state=active]:font-semibold rounded-none"
              >
                <tab.icon
                  className={`h-5 w-5 flex-shrink-0 ${activeTab === tab.id ? "text-[#004235]" : "text-[#028475]"}`}
                />
                <span>{tab.title}</span>
              </TabsTrigger>
            ))}
            <div
              className="absolute bottom-0 h-[4px] bg-[#004235] transition-all duration-300 ease-in-out"
              style={underlineStyle}
            />
          </TabsList>

          <TabsContent value="customers">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-start space-x-4">
                  <div className="bg-[#F2F2F2] p-3 rounded-lg">
                    <Users className="h-8 w-8 text-[#028475]" />
                  </div>
                  <div className="space-y-1">
                    <CardTitle className="text-xl text-[#004235]">MySTREAMLNK</CardTitle>
                    <CardDescription>For Customers</CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">
                    Access MySTREAMLNK to browse our product catalog, request intelligent quotes, place orders, gain
                    real-time AI-powered visibility into your shipments, and manage your account documents.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-start space-x-4">
                  <div className="bg-[#F2F2F2] p-3 rounded-lg">
                    <Briefcase className="h-8 w-8 text-[#028475]" />
                  </div>
                  <div className="space-y-1">
                    <CardTitle className="text-xl text-[#004235]">MySTREAMLNK+</CardTitle>
                    <CardDescription>For Independent Agents & Distributors</CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">
                    MySTREAMLNK+ is your dedicated portal to manage customer portfolios, generate quotes using real-time
                    data, place orders, track commissions, and grow your business with StreamLnk.
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="logistics">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-start space-x-4">
                  <div className="bg-[#F2F2F2] p-3 rounded-lg">
                    <Truck className="h-8 w-8 text-[#028475]" />
                  </div>
                  <div className="space-y-1">
                    <CardTitle className="text-xl text-[#004235]">STREAMFREIGHT</CardTitle>
                    <CardDescription>For Truckers and Freight Forwarders</CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">
                    Access STREAMFREIGHT to find and bid on available shipments, manage your compliance documents,
                    upload invoices, and track your earnings.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-start space-x-4">
                  <div className="bg-[#F2F2F2] p-3 rounded-lg">
                    <Globe className="h-8 w-8 text-[#028475]" />
                  </div>
                  <div className="space-y-1">
                    <CardTitle className="text-xl text-[#004235]">STREAMGLOBE</CardTitle>
                    <CardDescription>For Global Sea Freight Carriers</CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">
                    STREAMGLOBE is your integration point with StreamLnk. Manage booking requests, sync vessel schedules
                    via API, exchange shipping documents, and provide real-time tracking updates.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-start space-x-4">
                  <div className="bg-[#F2F2F2] p-3 rounded-lg">
                    <Package className="h-8 w-8 text-[#028475]" />
                  </div>
                  <div className="space-y-1">
                    <CardTitle className="text-xl text-[#004235]">STREAMPAK</CardTitle>
                    <CardDescription>For Packaging & 3PL Warehouse Partners</CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">
                    STREAMPAK centralizes your finishing operations. Manage packaging and storage job assignments,
                    update inventory status, and submit invoices for services rendered.
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="suppliers">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-start space-x-4">
                  <div className="bg-[#F2F2F2] p-3 rounded-lg">
                    <Package className="h-8 w-8 text-[#028475]" />
                  </div>
                  <div className="space-y-1">
                    <CardTitle className="text-xl text-[#004235]">E-STREAM</CardTitle>
                    <CardDescription>For Energy & Industrial Material Suppliers</CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">
                    Use E-STREAM to register, list your products with AI-driven readiness checks, manage compliance
                    documentation, respond to quotes, and coordinate logistics.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-start space-x-4">
                  <div className="bg-[#F2F2F2] p-3 rounded-lg">
                    <Database className="h-8 w-8 text-[#028475]" />
                  </div>
                  <div className="space-y-1">
                    <CardTitle className="text-xl text-[#004235]">STREAMGLOBE+</CardTitle>
                    <CardDescription>For Customs Clearance Agents</CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">
                    Log in to STREAMGLOBE+ to manage assigned shipments, securely access documentation, submit
                    declarations, update clearance statuses, and handle invoicing.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-start space-x-4">
                  <div className="bg-[#F2F2F2] p-3 rounded-lg">
                    <BarChart3 className="h-8 w-8 text-[#028475]" />
                  </div>
                  <div className="space-y-1">
                    <CardTitle className="text-xl text-[#004235]">STREAMRESOURCES+</CardTitle>
                    <CardDescription>For Resource Providers</CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">
                    STREAMRESOURCES+ helps you manage your resources, track utilization, and coordinate with partners in
                    the StreamLnk ecosystem.
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        <div className="text-center mt-10">
          <Link href="/portals" className="text-[#028475] font-medium hover:underline">
            View All StreamLnk Portals →
          </Link>
        </div>
      </div>
    </section>
  )
}
