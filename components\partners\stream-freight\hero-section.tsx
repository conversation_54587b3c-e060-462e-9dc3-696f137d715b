import { But<PERSON> } from "@/components/ui/button"
import { ArrowR<PERSON> } from "lucide-react"
import Link from "next/link"

export function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl">
          <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
            Powering Global Supply Chains: Your Gateway to Verified Freight & Rail Jobs
          </h1>
          <p className="text-xl md:text-2xl text-[#028475] mb-8">
            Connect your trucking fleet, rail services, or dispatch operations to StreamLnk's dynamic ecosystem. Bid on
            jobs, manage logistics, and track your earnings—all in one integrated portal.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white transition-colors" asChild>
              <Link href="/signup?portal=stream-freight">
                Become a StreamFreight Partner <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white transition-colors"
              asChild
            >
              <Link href="#learn-more">Learn More</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
