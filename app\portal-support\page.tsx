"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { MainNav } from "@/components/main-nav"
import { BottomFooter } from "@/components/bottom-footer"
import { Search, ChevronRight, Mail, Phone, MessageSquare, FileText } from "lucide-react"

export default function PortalSupportPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      {/* Hero Section */}
      <section className="bg-[#F2F2F2] py-16 md:py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">StreamLnk Support Hub</h1>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto mb-8">
            We're Here to Help You Succeed. Find answers, troubleshoot issues, and get the most out of your StreamLnk portal experience. 
            Our comprehensive support resources are designed for all users across our ecosystem.
          </p>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto relative">
            <div className="flex">
              <Input 
                type="text" 
                placeholder="How can we help you today? Search FAQs, Guides, and Resources..." 
                className="flex-grow rounded-r-none border-gray-300 focus:ring-0 focus:ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 h-12"
              />
              <Button 
                type="submit" 
                className="bg-[#004235] hover:bg-[#028475] text-white px-6 rounded-l-none h-12"
              >
                <Search className="h-5 w-5 mr-2" />
                Search
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Portal Selection Section */}
      <section className="py-12 md:py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-3xl font-bold text-[#004235] mb-6 text-center">
            Get Support Tailored to Your StreamLnk Portal
          </h2>
          <p className="text-center text-gray-700 mb-10 max-w-3xl mx-auto">
            Select the portal you need assistance with to access relevant FAQs, user guides, and contact options:
          </p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 max-w-5xl mx-auto">
            {[
              { name: "MyStreamLnk", description: "Customers" },
              { name: "MyStreamLnk+", description: "Agents/Distributors" },
              { name: "E-Stream", description: "Suppliers" },
              { name: "StreamFreight", description: "Land Freight Carriers" },
              { name: "StreamGlobe+", description: "Customs Agents" },
              { name: "StreamPak", description: "Packaging/Warehousing" },
              { name: "StreamResources+", description: "Data Subscribers" },
              { name: "General Platform", description: "Issues" },
            ].map((portal, index) => (
              <div key={index} className="bg-[#f3f4f6] hover:bg-[#F2F2F2] transition-colors p-6 rounded-lg border border-gray-200 text-center">
                <h3 className="text-lg font-semibold text-[#004235] mb-2">{portal.name}</h3>
                <p className="text-sm text-gray-600 mb-4">{portal.description}</p>
                <Button 
                  variant="outline" 
                  className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full"
                  asChild
                >
                  <Link href="#">
                    Select <ChevronRight className="ml-1 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQs Section */}
      <section className="py-12 md:py-16 bg-[#F2F2F2]">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-3xl font-bold text-[#004235] mb-10 text-center">
            Frequently Asked Questions & Troubleshooting
          </h2>
          
          <div className="max-w-5xl mx-auto">
            <h3 className="text-xl font-semibold text-[#028475] mb-6">Popular Support Topics & FAQs</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* FAQ Categories */}
              {[
                {
                  category: "Account & Login",
                  questions: [
                    "How do I reset my password?",
                    "Why is my account suspended?",
                    "How do I update my company profile?"
                  ]
                },
                {
                  category: "Sourcing & Quoting (for MyStreamLnk Users)",
                  questions: [
                    "How do I submit an RFQ?",
                    "Understanding your landed cost quote.",
                    "How do I compare suppliers?"
                  ]
                },
                {
                  category: "Listing Products & Inventory (for E-Stream Users)",
                  questions: [
                    "How do I add a new product?",
                    "Understanding StreamIndex™ pricing guidance.",
                    "How do I manage my inventory levels?"
                  ]
                },
                {
                  category: "Shipments & Tracking",
                  questions: [
                    "Where can I find my shipment status?",
                    "What do the different shipment milestones mean?",
                    "How do I report a shipment issue or delay?"
                  ]
                },
                {
                  category: "Billing & Payments",
                  questions: [
                    "How do I view my invoices/payouts?",
                    "Understanding StreamLnk fees and commissions.",
                    "What are the accepted payment methods?"
                  ]
                },
                {
                  category: "Document Management & Compliance",
                  questions: [
                    "What documents do I need to upload for verification?",
                    "How do I update an expiring certificate?",
                    "Where can I find my transaction documents (B/L, COA, etc.)?"
                  ]
                },
              ].map((faqCategory, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-sm">
                  <h4 className="text-lg font-semibold text-[#004235] mb-4">Category: {faqCategory.category}</h4>
                  <ul className="space-y-3">
                    {faqCategory.questions.map((question, qIndex) => (
                      <li key={qIndex} className="text-gray-700 hover:text-[#028475]">
                        <Link href="#" className="flex items-start">
                          <ChevronRight className="h-5 w-5 mr-2 text-[#028475] flex-shrink-0 mt-0.5" />
                          <span>{question}</span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
            
            <div className="text-center mt-8">
              <Button 
                variant="outline" 
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
                asChild
              >
                <Link href="#">
                  View All FAQs
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Self-Service Resources Section */}
      <section className="py-12 md:py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-3xl font-bold text-[#004235] mb-4 text-center">
            Learn at Your Own Pace
          </h2>
          <p className="text-center text-gray-700 mb-10 max-w-3xl mx-auto">
            Guides, Tutorials, and Documentation
          </p>
          
          <div className="max-w-4xl mx-auto">
            <h3 className="text-xl font-semibold text-[#028475] mb-6">Self-Service Resources</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {[
                {
                  title: "User Manuals & Portal Guides",
                  icon: <FileText className="h-8 w-8 text-[#004235]" />
                },
                {
                  title: "Video Tutorials",
                  icon: <FileText className="h-8 w-8 text-[#004235]" />
                },
                {
                  title: "Compliance Checklists & Guides",
                  icon: <FileText className="h-8 w-8 text-[#004235]" />
                },
                {
                  title: "Glossary of Trade & Platform Terms",
                  icon: <FileText className="h-8 w-8 text-[#004235]" />
                },
                {
                  title: "Developer Portal & API Documentation",
                  icon: <FileText className="h-8 w-8 text-[#004235]" />
                },
              ].map((resource, index) => (
                <div key={index} className="flex items-start p-4 border border-gray-200 rounded-lg hover:bg-[#f3f4f6] transition-colors">
                  <div className="mr-4 mt-1">
                    {resource.icon}
                  </div>
                  <div>
                    <h4 className="font-medium text-[#004235]">{resource.title}</h4>
                    <Link href="#" className="text-sm text-[#028475] hover:underline flex items-center mt-2">
                      Access resources <ChevronRight className="h-4 w-4 ml-1" />
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Support Section */}
      <section className="py-12 md:py-16 bg-[#F2F2F2]">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-3xl font-bold text-[#004235] mb-4 text-center">
            Still Need Assistance? We're Ready to Help.
          </h2>
          <p className="text-center text-gray-700 mb-10 max-w-3xl mx-auto">
            Contact Our Support Teams
          </p>
          
          <div className="max-w-5xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Support Ticket */}
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold text-[#004235] mb-4">Option 1: Submit a Support Ticket</h3>
                <p className="text-gray-700 mb-6">
                  Preferred for Detailed Issues. Our dedicated support teams aim to respond within 24 business hours.
                </p>
                <Button 
                  className="bg-[#004235] hover:bg-[#028475] text-white px-6 w-full md:w-auto"
                  asChild
                >
                  <Link href="#">
                    Create a New Support Ticket
                  </Link>
                </Button>
              </div>
              
              {/* Live Chat */}
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold text-[#004235] mb-4">Option 2: Live Chat</h3>
                <p className="text-gray-700 mb-6">
                  For Quick Queries - Available Monday to Friday, 9AM to 6PM (Your local time).
                </p>
                <Button 
                  className="bg-[#028475] hover:bg-[#004235] text-white px-6 flex items-center w-full md:w-auto"
                  asChild
                >
                  <Link href="#">
                    <MessageSquare className="h-5 w-5 mr-2" />
                    Start Live Chat
                  </Link>
                </Button>
              </div>
              
              {/* Email Support */}
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold text-[#004235] mb-4">Option 3: Email Support</h3>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-center">
                    <Mail className="h-5 w-5 mr-3 text-[#028475]" />
                    <span>General Support: <a href="mailto:<EMAIL>" className="text-[#028475] hover:underline"><EMAIL></a></span>
                  </li>
                  <li className="flex items-center">
                    <Mail className="h-5 w-5 mr-3 text-[#028475]" />
                    <span>Technical/API Support: <a href="mailto:<EMAIL>" className="text-[#028475] hover:underline"><EMAIL></a></span>
                  </li>
                  <li className="flex items-center">
                    <Mail className="h-5 w-5 mr-3 text-[#028475]" />
                    <span>Billing Inquiries: <a href="mailto:<EMAIL>" className="text-[#028475] hover:underline"><EMAIL></a></span>
                  </li>
                </ul>
              </div>
              
              {/* Phone Support */}
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold text-[#004235] mb-4">Option 4: Phone Support</h3>
                <p className="text-gray-700 mb-4">
                  Available for premium tier customers. Operating hours: Monday to Friday, 9AM to 6PM (Regional time).
                </p>
                <ul className="space-y-2 text-gray-700">
                  <li className="flex items-center">
                    <Phone className="h-5 w-5 mr-3 text-[#028475]" />
                    <span>North America: +****************</span>
                  </li>
                  <li className="flex items-center">
                    <Phone className="h-5 w-5 mr-3 text-[#028475]" />
                    <span>Europe: +44 (0) 20 7946 0958</span>
                  </li>
                  <li className="flex items-center">
                    <Phone className="h-5 w-5 mr-3 text-[#028475]" />
                    <span>Asia-Pacific: +65 6123 4567</span>
                  </li>
                </ul>
              </div>
            </div>
            
            <div className="mt-8 p-4 bg-white rounded-lg border border-gray-200 text-center">
              <p className="text-gray-700">
                <strong>Service Level Commitments:</strong> StreamLnk is committed to providing timely and effective support. 
                Please refer to our <Link href="#" className="text-[#028475] hover:underline">Service Level Agreement</Link> for more details on response times.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Feedback Section */}
      <section className="py-12 bg-white border-t border-gray-200">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-2xl font-bold text-[#004235] mb-4">Help Us Improve StreamLnk</h2>
          <p className="text-gray-700 mb-6 max-w-3xl mx-auto">
            Your feedback is invaluable in helping us enhance the StreamLnk platform. If you have suggestions, 
            feature requests, or encounter any usability issues, please let us know.
          </p>
          <Button 
            variant="outline" 
            className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
            asChild
          >
            <Link href="#">
              Submit Platform Feedback / Suggestion
            </Link>
          </Button>
        </div>
      </section>

      <BottomFooter />
    </div>
  )
}