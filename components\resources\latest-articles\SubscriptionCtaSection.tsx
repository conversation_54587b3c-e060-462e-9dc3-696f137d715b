"use client";

import { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Mail, Send } from "lucide-react";

export default function SubscriptionCtaSection() {
  const [email, setEmail] = useState('');
  const [selectedIndustry, setSelectedIndustry] = useState('');
  const [showIndustrySelect, setShowIndustrySelect] = useState(false);

  // Keeping industries relevant to a general articles subscription, can be adjusted
  const industries = [
    { value: "latest-updates", label: "Latest Platform Updates" },
    { value: "industry-news", label: "General Industry News" },
    { value: "product-deep-dives", label: "Product Deep Dives" },
    { value: "case-studies", label: "Case Studies & Success Stories" },
    { value: "all-articles", label: "All Articles & Updates" },
  ];

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Handle newsletter subscription logic here
    console.log({ 
      email, 
      interestedInTopic: showIndustrySelect, 
      topic: selectedIndustry 
    });
    alert(`Subscribed with ${email} for ${showIndustrySelect && selectedIndustry ? selectedIndustry : 'general articles'}!`);
    setEmail('');
    setSelectedIndustry('');
    setShowIndustrySelect(false);
  };

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center bg-[#F2F2F2] p-8 md:p-12 rounded-xl shadow-xl">
          <Mail className="h-12 w-12 text-[#028475] mb-6 mx-auto" />
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Get "Delivered by StreamLnk" Straight to Your Inbox
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Subscribe for a curated selection of our latest articles, industry news, upcoming webinars, and exclusive content. 
          </p>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Input 
                type="email" 
                placeholder="Your Email Address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required 
                className="w-full p-3 text-lg border-gray-300 focus:border-[#004235] focus:ring-[#004235] rounded-md placeholder-gray-500"
                aria-label="Email for newsletter"
              />
            </div>
            
            <div className="flex items-center justify-center space-x-2">
              <Checkbox 
                id="topicInterest"
                checked={showIndustrySelect}
                onCheckedChange={() => setShowIndustrySelect(!showIndustrySelect)}
                aria-labelledby="topicInterestLabel"
              />
              <label 
                htmlFor="topicInterest" 
                id="topicInterestLabel"
                className="text-sm font-medium text-gray-700 cursor-pointer"
              >
                I'm interested in specific topics/updates
              </label>
            </div>

            {showIndustrySelect && (
              <div>
                <Select onValueChange={setSelectedIndustry} value={selectedIndustry}>
                  <SelectTrigger className="w-full p-3 text-lg border-gray-300 focus:border-[#004235] focus:ring-[#004235] rounded-md text-left text-gray-700">
                    <SelectValue placeholder="Select Topic/Update Type" />
                  </SelectTrigger>
                  <SelectContent>
                    {industries.map(industry => (
                      <SelectItem key={industry.value} value={industry.value}>
                        {industry.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div>
              <Button 
                type="submit"
                className="bg-[#004235] hover:bg-[#028475] text-white w-full text-lg py-3"
                size="lg"
              >
                SUBSCRIBE NOW
                <Send className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </form>
          
          {/* Social media links can be added here if desired, styled differently or similar to footer */}
          {/* <p className="text-sm text-gray-600 mt-8 mb-4">
            Follow us on social media for daily insights and updates:
          </p>
          <div className="flex justify-center space-x-4">
            <Link href="https://linkedin.com/company/streamlnk" target="_blank" rel="noopener noreferrer" aria-label="StreamLnk on LinkedIn">
              <Button variant="outline" size="icon" className="text-[#004235] border-[#004235] hover:bg-[#004235] hover:text-white">
                <Linkedin className="h-5 w-5" />
              </Button>
            </Link>
            <Link href="https://twitter.com/streamlnk" target="_blank" rel="noopener noreferrer" aria-label="StreamLnk on Twitter">
              <Button variant="outline" size="icon" className="text-[#004235] border-[#004235] hover:bg-[#004235] hover:text-white">
                <Twitter className="h-5 w-5" />
              </Button>
            </Link>
          </div> */}
        </div>
      </div>
    </section>
  );
}