"use client";

import { CheckCircle } from 'lucide-react';

export default function ImperativeSection() {
  const imperatives = [
    "Reduce their carbon footprint and environmental impact.",
    "Ensure ethical sourcing and fair labor practices throughout their supply chains.",
    "Promote diversity, equity, and inclusion within their operations and partnerships.",
    "Comply with evolving ESG regulations and disclosure requirements (e.g., CSRD, TCFD, Scope 3 emissions).",
    "Meet the demands of investors, customers, and employees for greater corporate responsibility.",
    "Build resilient supply chains that account for climate-related and social risks."
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">
            The ESG Imperative in Modern Industrial Trade
          </h2>
          <p className="text-xl text-center text-[#028475] font-semibold mb-8">
            Why ESG is Transforming Global Supply Chains
          </p>
          <p className="text-lg text-gray-700 mb-10">
            Environmental, Social, and Governance (ESG) considerations are no longer peripheral; they are central to business strategy, risk management, and stakeholder expectations in the industrial sector. Companies face increasing pressure to:
          </p>
          
          <div className="bg-[#f3f4f6] p-6 md:p-8 rounded-lg shadow-md">
            <ul className="space-y-4">
              {imperatives.map((item, index) => (
                <li key={index} className="flex items-start">
                  <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                  <span className="text-gray-700 text-md">{item}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}