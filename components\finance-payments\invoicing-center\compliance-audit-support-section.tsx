import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import ComplianceFeature from "@/components/finance-payments/invoicing-center/compliance-feature"

export default function ComplianceAuditSupportSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Compliance & Audit Support</h2>
          <p className="text-gray-600 max-w-3xl">
            Enterprise-grade compliance features ensure your financial data meets regulatory requirements.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <ComplianceFeature
            icon={<FileCheck className="h-8 w-8 text-[#028475]" />}
            title="Document Traceability"
            description="Built-in document traceability and change logs for complete audit trails"
          />
          <ComplianceFeature
            icon={<FileText className="h-8 w-8 text-[#028475]" />}
            title="Exportable Reports"
            description="Exportable reports for tax audits, financial reviews, or internal reporting"
          />
          <ComplianceFeature
            icon={<Shield className="h-8 w-8 text-[#028475]" />}
            title="Secure Infrastructure"
            description="GDPR- and SOC2-compliant infrastructure for data security and privacy"
          />
        </div>
      </div>
    </section>
  )
}