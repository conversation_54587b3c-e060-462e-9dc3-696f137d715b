"use client"

import { CheckCircle, Search, Filter, BarChart3, FileText } from "lucide-react"

export function FeaturesSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Find What You Need, When You Need It – Powered by StreamLnk AI
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk's MyStreamLnk Customer Portal revolutionizes how you discover and source industrial materials. Our platform provides:
          </p>
          
          <div className="space-y-8">
            {/* Expansive Global Catalog */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Search className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Expansive Global Catalog</h3>
                  <p className="text-gray-700 mb-4">
                    Access detailed listings for thousands of polymers, chemicals, energy products, and other industrial materials from our network of verified E-Stream suppliers.
                  </p>
                </div>
              </div>
            </div>
            
            {/* Granular Search & Advanced Filtering */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Filter className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Granular Search & Advanced Filtering</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <h4 className="font-semibold text-[#004235] mb-2">Search by:</h4>
                      <ul className="space-y-1 text-gray-700">
                        <li className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-[#028475] mr-2 flex-shrink-0" />
                          <span>Product Name</span>
                        </li>
                        <li className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-[#028475] mr-2 flex-shrink-0" />
                          <span>CAS Number</span>
                        </li>
                        <li className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-[#028475] mr-2 flex-shrink-0" />
                          <span>HS Code</span>
                        </li>
                        <li className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-[#028475] mr-2 flex-shrink-0" />
                          <span>Supplier Name</span>
                        </li>
                        <li className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-[#028475] mr-2 flex-shrink-0" />
                          <span>Brand</span>
                        </li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold text-[#004235] mb-2">Filter by:</h4>
                      <ul className="space-y-1 text-gray-700">
                        <li className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-[#028475] mr-2 flex-shrink-0" />
                          <span>Product Family & Grade</span>
                        </li>
                        <li className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-[#028475] mr-2 flex-shrink-0" />
                          <span>Technical Specifications</span>
                        </li>
                        <li className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-[#028475] mr-2 flex-shrink-0" />
                          <span>Certifications</span>
                        </li>
                        <li className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-[#028475] mr-2 flex-shrink-0" />
                          <span>Country of Origin</span>
                        </li>
                        <li className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-[#028475] mr-2 flex-shrink-0" />
                          <span>ESG Criteria</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Side-by-Side Product Comparison */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Side-by-Side Product Comparison</h3>
                  <p className="text-gray-700 mb-4">
                    Easily compare detailed specifications, pricing indications, lead times, and supplier profiles for multiple products.
                  </p>
                </div>
              </div>
            </div>
            
            {/* AI-Powered Recommendations */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <FileText className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Real-Time Availability & Verified Supplier Information</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>See indicative stock levels and production lead times directly from suppliers.</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>Access supplier profiles with compliance status, iScore™ summaries, and contact details (once engaged).</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>AI-Powered Recommendations (Future Enhancement): Receive suggestions for alternative materials or suppliers based on your search criteria, past sourcing history, and market trends.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}