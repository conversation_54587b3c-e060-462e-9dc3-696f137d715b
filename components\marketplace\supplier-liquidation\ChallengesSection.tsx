import { Banknote, Calendar, Clock, Percent, ShoppingBag, Warehouse } from "lucide-react";

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">Is Idle Inventory Tying Up Your Capital and Space?</h2>
          <p className="text-lg text-gray-700 mb-8 text-center">
            Industrial material suppliers often face challenges with:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <Warehouse className="h-5 w-5 text-white" />
              </div>
              <p className="text-gray-700">Accumulation of slow-moving or surplus stock taking up valuable warehouse space.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <ShoppingBag className="h-5 w-5 text-white" />
              </div>
              <p className="text-gray-700">Difficulty finding buyers for offgrade, near-expiry, or end-of-line materials.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <Calendar className="h-5 w-5 text-white" />
              </div>
              <p className="text-gray-700">Year-end inventory taxes imposing financial burdens on unsold stock.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <Banknote className="h-5 w-5 text-white" />
              </div>
              <p className="text-gray-700">Limited channels to quickly reach a broad base of opportunistic buyers.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <Percent className="h-5 w-5 text-white" />
              </div>
              <p className="text-gray-700">Price erosion when trying to liquidate through traditional, less transparent methods.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <Clock className="h-5 w-5 text-white" />
              </div>
              <p className="text-gray-700">Time-consuming negotiations for small or non-standard lots.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}