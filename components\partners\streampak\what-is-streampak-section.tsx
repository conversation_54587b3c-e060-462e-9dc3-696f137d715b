import { Check } from "lucide-react"
import Image from "next/image"

export function WhatIsStreamPakSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="w-10 h-1 bg-[#028475]"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">What Is StreamPak?</h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 items-center">
          <div className="lg:col-span-3">
            <p className="text-gray-700 text-lg mb-6">
              StreamPak is the specialized packaging and storage management portal within StreamLnk's comprehensive
              global supply chain ecosystem. It's designed to seamlessly connect qualified third-party service providers
              like you with suppliers and buyers requiring expert handling, storage, and preparation of industrial
              goods.
            </p>
            <p className="text-gray-700 text-lg mb-6">Through StreamPak, you can:</p>
            <ul className="space-y-4 mb-6">
              <li className="flex items-start">
                <Check className="text-[#028475] mr-3 mt-1 h-5 w-5 flex-shrink-0" />
                <span>Receive targeted job assignments for packaging, repackaging, labeling, or warehousing.</span>
              </li>
              <li className="flex items-start">
                <Check className="text-[#028475] mr-3 mt-1 h-5 w-5 flex-shrink-0" />
                <span>Report inventory statuses and product handling updates in real-time.</span>
              </li>
              <li className="flex items-start">
                <Check className="text-[#028475] mr-3 mt-1 h-5 w-5 flex-shrink-0" />
                <span>Manage documentation and communication efficiently through one secure, integrated platform.</span>
              </li>
            </ul>
            <p className="text-gray-700 text-lg">
              Whether you handle bulk commodities, palletized goods, or intricately boxed materials, StreamPak offers
              you a prime position in the modern global supply chain, enhancing your visibility and operational
              efficiency.
            </p>
          </div>
          <div className="lg:col-span-2">
            <div className="rounded-lg overflow-hidden">
              <Image
                src="/images/partner/streampak/StreamPak portal interface.webp"
                alt="StreamPak portal interface"
                width={500}
                height={400}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
