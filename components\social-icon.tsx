import Link from "next/link"
import { Facebook, Instagram, Linkedin, Youtube, MessageCircle, Twitter } from "lucide-react"

interface SocialIconProps {
  platform: "facebook" | "instagram" | "linkedin" | "youtube" | "whatsapp" | "twitter"
  href: string
}

export function SocialIcon({ platform, href }: SocialIconProps) {
  const icons = {
    facebook: Facebook,
    instagram: Instagram,
    linkedin: Linkedin,
    youtube: Youtube,
    whatsapp: MessageCircle,
    twitter: Twitter,
  }

  const Icon = icons[platform]

  return (
    <Link
      href={href}
      className="w-8 h-8 bg-black flex items-center justify-center hover:opacity-80 transition-opacity rounded-sm"
      target="_blank"
      rel="noopener noreferrer"
    >
      <Icon className="w-4 h-4" />
    </Link>
  )
}

