"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/partners/streamglobe-carrier/HeroSection";
import OpportunitySection from "@/components/partners/streamglobe-carrier/OpportunitySection";
import ApiConnectivitySection from "@/components/partners/streamglobe-carrier/ApiConnectivitySection";
import PartnerCriteriaSection from "@/components/partners/streamglobe-carrier/PartnerCriteriaSection";
import OnboardingProcessSection from "@/components/partners/streamglobe-carrier/OnboardingProcessSection";
import CtaSection from "@/components/partners/streamglobe-carrier/CtaSection";

export default function StreamGlobeCarrierPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />
      <main>
        <HeroSection />
        <OpportunitySection />
        <ApiConnectivitySection />
        <PartnerCriteriaSection />
        <OnboardingProcessSection />
        <CtaSection />
      </main>
      <BottomFooter />
    </div>
  );
}