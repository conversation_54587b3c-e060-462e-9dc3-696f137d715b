import { XCircle, GlobeLock, AlertTriangle, SearchSlash, UsersRound, Shuffle } from "lucide-react";

export default function LimitationsSection() {
  const limitations = [
    {
      icon: <SearchSlash className="h-6 w-6 text-red-500 mr-3 mt-1 flex-shrink-0" />,
      text: "Missed opportunities to source materials at more competitive global prices."
    },
    {
      icon: <GlobeLock className="h-6 w-6 text-red-500 mr-3 mt-1 flex-shrink-0" />,
      text: "Difficulty for suppliers to reach new international buyers and expand their market share."
    },
    {
      icon: <AlertTriangle className="h-6 w-6 text-red-500 mr-3 mt-1 flex-shrink-0" />,
      text: "Challenges in finding specialized logistics or compliance partners in unfamiliar territories."
    },
    {
      icon: <UsersRound className="h-6 w-6 text-red-500 mr-3 mt-1 flex-shrink-0" />,
      text: "Over-reliance on a small pool of known contacts, limiting resilience against regional disruptions."
    },
    {
      icon: <Shuffle className="h-6 w-6 text-red-500 mr-3 mt-1 flex-shrink-0" />,
      text: "Lack of a unified platform to discover, vet, and engage with a diverse range of global industry players."
    },
    {
      icon: <XCircle className="h-6 w-6 text-red-500 mr-3 mt-1 flex-shrink-0" />,
      text: "Higher costs and complexities associated with piecing together international supply chains."
    }
  ];

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Is Your Current Network Restricting Your Growth and Options?
          </h2>
          <p className="text-xl text-gray-700">
            The Limitations of Localized or Fragmented Networks
          </p>
        </div>
        <div className="max-w-2xl mx-auto">
          <p className="text-lg text-gray-700 mb-8">
            Relying on limited, regional, or disconnected networks in industrial trade often leads to:
          </p>
          <ul className="space-y-4">
            {limitations.map((item, index) => (
              <li key={index} className="flex items-start">
                {item.icon}
                <span className="text-gray-700">{item.text}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  );
}