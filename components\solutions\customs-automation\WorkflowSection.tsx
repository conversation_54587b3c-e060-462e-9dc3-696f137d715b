import { WorkflowTimeline, WorkflowStep } from "@/components/ui/WorkflowTimeline"

export default function WorkflowSection() {
  const timelineSteps: WorkflowStep[] = [
    {
      number: 1,
      title: "Shipment Initiated",
      description: "International order confirmed on StreamLnk."
    },
    {
      number: 2,
      title: "Agent Assigned",
      description: "StreamLnk AI assigns a vetted customs agent from the StreamGlobe+ network based on route and expertise."
    },
    {
      number: 3,
      title: "Documents Delivered",
      description: "Agent receives all necessary shipping documents digitally via StreamGlobe+. POA process managed."
    },
    {
      number: 4,
      title: "Agent Processes Clearance",
      description: "Agent prepares and submits declarations, updates status in real-time."
    },
    {
      number: 5,
      title: "Real-Time Visibility",
      description: "You monitor progress directly in your MyStreamLnk/E-Stream portal."
    },
    {
      number: 6,
      title: "Clearance Confirmed",
      description: "Notification received, and goods are ready for onward movement."
    },
    {
      number: 7,
      title: "Documents Archived",
      description: "All clearance paperwork is stored in your Document Vault."
    }
  ];

  return (
    <WorkflowTimeline
      title="From Paperwork to Port Release – Faster and More Transparent"
      steps={timelineSteps}
    />
  )
}