import { Building2, Globe, Package, Truck } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"

interface PartnerProps {
  partner: {
    id: string
    name: string
    location: string
    services: string[]
    website: string
  }
}

export function PartnerCard({ partner }: PartnerProps) {
  return (
    <Card className="overflow-hidden border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
          <h3 className="font-medium">{partner.name}</h3>
        </div>

        <div className="space-y-3 mb-4">
          <div className="flex items-start gap-2">
            <Building2 className="h-4 w-4 text-gray-400 mt-0.5" />
            <p className="text-sm text-gray-600">{partner.location}</p>
          </div>

          <div className="flex items-start gap-2">
            <Truck className="h-4 w-4 text-gray-400 mt-0.5" />
            <p className="text-sm text-gray-600">
              {partner.services.includes("land") && "Land Freight"}
              {partner.services.includes("warehouse") && " + Warehousing"}
            </p>
          </div>

          {partner.services.includes("stream") && (
            <div className="flex items-start gap-2">
              <Package className="h-4 w-4 text-gray-400 mt-0.5" />
              <p className="text-sm text-gray-600">
                StreamFreight
                {partner.services.includes("warehouse") && " & StreamPak"}
              </p>
            </div>
          )}

          <div className="flex items-start gap-2">
            <Globe className="h-4 w-4 text-gray-400 mt-0.5" />
            <p className="text-sm text-gray-600">{partner.website}</p>
          </div>
        </div>

        <Button variant="link" className="text-emerald-600 p-0 h-auto">
          Learn More
        </Button>
      </CardContent>
    </Card>
  )
}
