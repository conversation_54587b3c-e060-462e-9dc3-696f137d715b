"use client"

import { useState, useCallback } from "react"
import InteractiveGlobalMap from "@/components/map/page"
import ReusableTabs, { type ReusableTabData } from "@/components/ui/reusable-tabs";

// Removed Select components as ReusableTabs will handle selection
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
// Removed Button as ReusableTabs has its own trigger styling
// import { <PERSON><PERSON> } from "@/components/ui/button"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import { Briefcase, Ship, Truck, Train, PackageCheck, LucideIcon } from 'lucide-react'; // Added icons

// Service types with icons
const serviceTypes: { id: string; label: string; portal: string; icon: LucideIcon }[] = [
  { id: "sea", label: "Sea Freight Carriers", portal: "StreamGlobe – Sea Freight", icon: Ship },
  { id: "customs", label: "Customs Clearance Agents", portal: "StreamGlobe – Customs", icon: Briefcase },
  { id: "land", label: "Land Freight Carriers", portal: "StreamFreight", icon: Truck },
  { id: "rail", label: "Rail Freight Carriers", portal: "StreamFreight", icon: Train },
  { id: "packaging", label: "Packaging & Value-Added Service Providers", portal: "StreamPak", icon: PackageCheck },
];

// Partner data - New structure (remains the same)
interface PartnerCategory {
  portal: string;
  groups: {
    name: string;
    partners: string[];
  }[];
  footer?: string; // Make footer optional
}

const partnerDataByCategory: Record<string, PartnerCategory> = {
  sea: {
    portal: "StreamGlobe – Sea Freight",
    groups: [
      { name: "Global", partners: ["Maersk Line", "MSC", "CMA CGM", "Hapag-Lloyd", "COSCO"] },
      { name: "Regional", partners: ["Seaboard Marine (Caribbean/LATAM)", "Crowley Maritime (U.S./Puerto Rico)", "Grimaldi Lines (Europe–Africa)", "etc."] },
    ],
  },
  customs: {
    portal: "StreamGlobe – Customs",
    groups: [
      { name: "North America", partners: ["BorderLink Logistics", "TradeGate Customs", "CanClear Trade Solutions"] },
      { name: "Europe", partners: ["EuroGate Brokers", "UKClear Freight Services", "Scandic Customs Agency"] },
      { name: "LATAM", partners: ["Aduanal ProLogix", "Despachadores Andes", "BrasilCargo Customs"] },
      { name: "Asia-Pacific", partners: ["AsiaComply Brokers", "LogiClear India", "JPC Brokers"] },
      { name: "MENA & Africa", partners: ["ClearPath MENA", "Maghreb Customs Solutions", "Zambezi Brokers Ltd."] },
    ],
  },
  land: {
    portal: "StreamFreight",
    groups: [
      { name: "North America", partners: ["Road 45", "JB Hunt", "BNSF Logistics", "Schneider", "Knight-Swift"] },
      { name: "Europe", partners: ["Kuehne + Nagel Road", "DB Schenker", "GEFCO", "Dachser", "DFDS Logistics"] },
      { name: "LATAM", partners: ["TASA Logística", "Transportes Castores", "Braspress", "Andes Logistics"] },
      { name: "Asia-Pacific", partners: ["Toll Group", "Sankyu Inc.", "Delhivery", "JNE Express", "CJ Logistics"] },
      { name: "MENA & Africa", partners: ["Aramex", "Agility Logistics", "Imperial Logistics", "Naqel Express"] },
    ],
  },
  rail: {
    portal: "StreamFreight",
    groups: [
      { name: "North America", partners: ["Union Pacific", "BNSF Railway", "CN", "CPKC", "Ferromex"] },
      { name: "Europe", partners: ["DB Cargo", "SNCF Fret", "Rail Cargo Group", "PKP Cargo", "Green Cargo"] },
      { name: "LATAM", partners: ["MRS Logística", "Ferrovalle", "Rumo", "Belgrano Cargas"] },
      { name: "Asia-Pacific", partners: ["Indian Railways – DFC", "China Railway Express", "KTZ Express", "Pacific National"] },
      { name: "MENA & Africa", partners: ["Etihad Rail", "ONCF Logistics", "Transnet Freight Rail"] },
    ],
  },
  packaging: {
    portal: "StreamPak",
    groups: [
      { name: "North America", partners: ["Katoen Natie (polymer bulk)", "Bulkmatic (railcar offloading)", "Foodliner (food/chemical bulk)", "Pakwell Packaging", "Quintex Logistics (AI inventory)"] },
      { name: "Europe", partners: ["Bertschi AG (chemicals, bulk liquids)", "Hoyer Group (tank containers, repackaging)", "EuroChem Terminal (fertilizer repackaging)", "Lagerbox Logistics (warehousing, pallet packing)"] },
      { name: "LATAM", partners: ["Logística TPC Group (port warehousing, bagging)", "GRUPO EULEN Logística (contract warehousing, light assembly)", "Sudamericana de Fletes (bulk packaging)"] },
      { name: "Asia-Pacific", partners: ["Yusen Logistics (auto-packing)", "CJ Logistics (smart warehouses, automation)", "TVS Supply Chain (kitting, export prep)", "Kerry Logistics (co-packing, palletizing)"] },
      { name: "MENA & Africa", partners: ["Agility Logistics Parks (in-house packaging)", "Transcargo International (bulk polymer packaging)", "Imperial Logistics Africa (distribution warehousing)", "Tassnim Logistics (packaging automation)"] },
    ],
    footer: "StreamPak portal integration enables: real-time inventory, SLA monitoring, expiry-aware documentation, and sync with freight/customs workflows."
  },
};

// Content component for each tab
const ServiceTabContent = ({ serviceId }: { serviceId: string }) => {
  const currentServiceData = partnerDataByCategory[serviceId as keyof typeof partnerDataByCategory];
  const serviceInfo = serviceTypes.find(s => s.id === serviceId);

  if (!currentServiceData || !serviceInfo) {
    return <p>Details not available for this service.</p>;
  }

  return (
    <div className="bg-white dark:bg-neutral-800 p-6 rounded-lg shadow-lg">
      <h3 className="text-xl md:text-2xl font-semibold text-[#004235] dark:text-emerald-500 mb-1">
        {serviceInfo.label}
      </h3>
      <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">Portal: {currentServiceData.portal}</p>
      
      {currentServiceData.groups.map((group, groupIndex) => (
        <div key={groupIndex} className="mb-6 last:mb-0">
          <h4 className="text-lg font-medium text-gray-700 dark:text-gray-200 mb-2">{group.name}</h4>
          <ul className="list-disc list-inside pl-4 space-y-1 text-gray-600 dark:text-gray-300">
            {group.partners.map((partner, partnerIndex) => (
              <li key={partnerIndex}>{partner}</li>
            ))}
          </ul>
        </div>
      ))}
      {currentServiceData.footer && (
          <p className="mt-6 pt-4 border-t border-gray-200 dark:border-neutral-700 text-sm text-gray-600 dark:text-gray-400">
              {currentServiceData.footer}
          </p>
      )}
    </div>
  );
};

export default function StrategicPartnerPage() {
  const [selectedService, setSelectedService] = useState("sea"); // Default to first service type

  const tabs: ReusableTabData[] = serviceTypes.map(service => ({
    id: service.id,
    title: service.label,
    icon: service.icon, // Pass the icon component
    contentComponent: () => <ServiceTabContent serviceId={service.id} />,
  }));

  const handleTabChange = useCallback((tabId: string) => {
    setSelectedService(tabId);
  }, []);

  return (
    <>
      <MainNav />
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white dark:from-neutral-900 dark:to-black">
        {/* Hero Text Section */}
        <div className="container mx-auto px-4 pt-12 md:pt-16 text-center relative z-50">
          <h1 className="text-3xl md:text-5xl font-bold text-[#004235] dark:text-emerald-400 mb-4">
            StreamLnk Global Partner Directory
          </h1>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            Access a digitally unified, AI-enabled, and regionally optimized supply chain network. StreamLnk partners with trusted, vetted providers across global logistics. Its integrated portal ecosystem helps businesses move goods faster, smarter, and more transparently with seamless coordination and real-time visibility.
          </p>
        </div>

        {/* Full Width Map Section */}
        <div className="w-full mb-12 md:mb-16">
          <InteractiveGlobalMap />
        </div>

        {/* Remaining Content in Container */}
        <div className="container mx-auto px-4 pb-12 md:pb-16">
          {/* Key Partner Categories Section */}
          <div className="mb-12 md:mb-16">
            <h2 className="text-2xl md:text-3xl font-semibold text-gray-800 dark:text-gray-100 mb-2 text-center">
              KEY PARTNER CATEGORIES & PORTALS
            </h2>
            <div className="h-1 w-24 bg-[#028475] mx-auto mb-8"></div>

            {/* Service Type Tabs using ReusableTabs */}
            <ReusableTabs
              tabsData={tabs}
              defaultTabId={selectedService}
              onTabChange={handleTabChange}
              // Using default ReusableTabs styling, which matches the portal page
              // Customizations can be added here if needed, e.g.:
              // tabsListClassName="bg-gray-100 dark:bg-neutral-800 rounded-xl p-1 shadow"
              // tabTriggerClassName="px-3 py-1.5 text-sm"
              // activeTabBgColor="bg-[#004235]"
              // activeTabTextColor="text-white"
              // inactiveTabBgColor="bg-transparent"
              inactiveTabTextColor="text-[#004235] dark:text-gray-200"
              // hoverTabBgColor="hover:bg-gray-200 dark:hover:bg-neutral-700"
              // underlineColor="bg-[#028475]" // Example of changing underline color
            />
          </div>

          {/* Integration Map Section (remains the same) */}
          <div className="bg-gray-50 dark:bg-neutral-800 p-6 md:p-8 rounded-lg shadow-lg">
            <h2 className="text-2xl md:text-3xl font-semibold text-gray-800 dark:text-gray-100 mb-2 text-center">INTEGRATION MAP</h2>
            <div className="h-1 w-24 bg-[#028475] mx-auto mb-8"></div>
            <p className="text-gray-700 dark:text-gray-300 mb-4 text-center">Partners integrate with StreamLnk portals:</p>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 text-center">
              {[ 
                { name: "StreamFreight", description: "Road, Rail, Last Mile" },
                { name: "StreamPak", description: "Packaging & Storage" },
                { name: "StreamGlobe", description: "Sea Freight & Customs" },
                { name: "E-Stream", description: "Supplier listing" },
                { name: "StreamResources+", description: "Data/API Collaboration" },
              ].map(portal => (
                <div key={portal.name} className="p-4 bg-white dark:bg-neutral-700 rounded-md shadow hover:shadow-md transition-shadow">
                  <h3 className="font-semibold text-[#004235] dark:text-emerald-500 text-lg">{portal.name}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{portal.description}</p>
                </div>
              ))}
            </div>
          </div>

        </div>
      </div>
      <MainFooter />
    </>
  )
}
