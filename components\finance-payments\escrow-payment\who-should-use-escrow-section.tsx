import { Users, Briefcase, Building2 } from "lucide-react"
import UserTypeCard from "@/components/finance-payments/escrow-payment/user-type-card"

export default function WhoShouldUseEscrowSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
            Who Should Use Escrow & Milestone Payments?
          </h2>
          <p className="text-gray-600 max-w-3xl">
            Our escrow and milestone payment solutions are ideal for various business scenarios.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <UserTypeCard
            icon={<Users className="h-10 w-10 text-[#028475]" />}
            title="Buyers"
            description="Placing high-value or multi-stage orders with new suppliers or in new markets"
          />
          <UserTypeCard
            icon={<Briefcase className="h-10 w-10 text-[#028475]" />}
            title="Suppliers"
            description="Working with new or international clients where payment security is essential"
          />
          <UserTypeCard
            icon={<Building2 className="h-10 w-10 text-[#028475]" />}
            title="Freight & Logistics"
            description="Partners billing across multiple shipment phases or handling high-value cargo"
          />
        </div>
      </div>
    </section>
  )
}