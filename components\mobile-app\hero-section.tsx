import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Smartphone } from "lucide-react";
import Link from "next/link";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid md:grid-cols-2 gap-8 items-center">
          <div className="max-w-xl">
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Your Global Trade Command Center, Now in Your Pocket: Introducing the StreamLnk Mobile App
            </h1>
            <p className="text-lg md:text-xl text-[#028475] mb-8">
              Stay connected and in control of your industrial sourcing, sales, and logistics, no matter where you are.
              The StreamLnk mobile app brings the power of our integrated ecosystem to your fingertips. (Coming Soon to
              iOS and Android!)
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                size="lg"
                className="bg-[#004235] hover:bg-[#028475] text-white transition-colors"
                asChild
              >
                <Link href="#notify-app-launch">
                  Sign Up For Notifications <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white transition-colors"
                asChild
              >
                <Link href="#mobile-features">Learn More</Link>
              </Button>
            </div>
          </div>
          <div className="hidden md:flex justify-center">
            {/* Placeholder for a mobile app image/graphic */}
            <Smartphone className="w-64 h-64 text-[#004235] opacity-50" />
          </div>
        </div>
      </div>
    </section>
  );
}