import { Accordion } from "@/components/ui/accordion"
import PortalItem from "@/components/portals/portal-item"
import {
  User,
  Users,
  ShoppingCart,
  FileText,
  BarChart,
  Clock,
  Briefcase,
  TrendingUp,
  Smartphone,
  Laptop,
  Server,
} from "lucide-react"
import { FeatureCard, PlatformBadge, BenefitItem } from "@/components/portals/portal-components"
import Link from "next/link"

export default function CustomerPortals() {
  return (
    <Accordion type="single" collapsible className="w-full space-y-6">
      <PortalItem
        id="mystreamlnk"
        title="mySTREAMLNK"
        subtitle="For Customers"
        icon={<User className="h-8 w-8" />}
        color="#004235"
        imageSrc="/images/portals/my-stream-lnk.webp"
        imageAlt="Customer Portal Interface"
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Portal Overview</h4>
            <p className="text-[#404040] mb-6">
              mySTREAMLNK is our comprehensive customer portal designed to give you complete control over your supply
              chain. From browsing our extensive product catalog to tracking shipments in real-time with AI-powered
              visibility, mySTREAMLNK streamlines your logistics experience and provides you with the tools you need to
              make informed decisions.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Key Features</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <FeatureCard icon={<ShoppingCart />} title="Product Catalog">
                Browse our comprehensive product catalog with detailed supplier and brand information
              </FeatureCard>
              <FeatureCard icon={<FileText />} title="Intelligent Quotes">
                Request and receive intelligent quotes tailored to your specific business needs
              </FeatureCard>
              <FeatureCard icon={<BarChart />} title="Order Management">
                Place and manage orders efficiently with our intuitive interface
              </FeatureCard>
              <FeatureCard icon={<Clock />} title="Real-time Tracking">
                Gain real-time AI-powered visibility into your shipments at every stage
              </FeatureCard>
            </div>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Available Platforms</h4>
            <div className="flex flex-wrap gap-4 mb-6">
              <PlatformBadge icon={<Smartphone />} platform="Mobile App" />
              <PlatformBadge icon={<Laptop />} platform="Web Portal" />
              <PlatformBadge icon={<Server />} platform="API Access" />
            </div>
          </div>

          <div className="bg-gray-50 p-6 rounded-lg">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Who is this for?</h4>
            <p className="text-[#404040] mb-6">
              mySTREAMLNK is designed for businesses of all sizes who need to manage their supply chain efficiently.
              Whether you're a small business or a large enterprise, our customer portal provides the tools you need.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Benefits</h4>
            <ul className="space-y-3 mb-6">
              <BenefitItem>Streamlined ordering process</BenefitItem>
              <BenefitItem>Complete visibility of your supply chain</BenefitItem>
              <BenefitItem>Reduced administrative overhead</BenefitItem>
              <BenefitItem>Improved decision-making with real-time data</BenefitItem>
              <BenefitItem>Secure document management</BenefitItem>
            </ul>

            <Link href="/signup?portal=mystreamlnk" className="block w-full">
              <button className="w-full bg-[#004235] hover:bg-[#004235]/90 text-white py-2 px-4 rounded-md font-medium mt-4">
                Request Access
              </button>
            </Link>
          </div>
        </div>
      </PortalItem>

      <PortalItem
        id="mystreamlnkplus"
        title="mySTREAMLNK+"
        subtitle="For Independent Agents & Assigned Distributors"
        icon={<Users className="h-8 w-8" />}
        color="#004235"
        imageSrc="/images/portals/my-stream-lnk-plus.webp"
        imageAlt="Agent Portal Interface"
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Portal Overview</h4>
            <p className="text-[#404040] mb-6">
              mySTREAMLNK+ is a dedicated portal for independent agents and assigned distributors to manage customer
              portfolios, generate quotes using real-time data, place orders, track commissions, and grow your business
              with StreamLnk. This enhanced version provides all the tools you need to serve your customers effectively.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Key Features</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <FeatureCard icon={<Briefcase />} title="Portfolio Management">
                Efficiently manage customer portfolios with comprehensive tools and insights
              </FeatureCard>
              <FeatureCard icon={<TrendingUp />} title="Real-time Quotes">
                Generate accurate quotes using real-time data and pricing information
              </FeatureCard>
              <FeatureCard icon={<ShoppingCart />} title="Order Placement">
                Place and manage orders on behalf of your customers with ease
              </FeatureCard>
              <FeatureCard icon={<BarChart />} title="Commission Tracking">
                Track your commissions and performance metrics in real-time
              </FeatureCard>
            </div>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Available Platforms</h4>
            <div className="flex flex-wrap gap-4 mb-6">
              <PlatformBadge icon={<Smartphone />} platform="Mobile App" />
              <PlatformBadge icon={<Laptop />} platform="Web Portal" />
              <PlatformBadge icon={<Server />} platform="API Access" />
            </div>
          </div>

          <div className="bg-gray-50 p-6 rounded-lg">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Who is this for?</h4>
            <p className="text-[#404040] mb-6">
              mySTREAMLNK+ is specifically designed for independent agents and assigned distributors who represent
              StreamLnk products and services to their customers. This portal provides the tools needed to effectively
              manage customer relationships and grow your business.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Benefits</h4>
            <ul className="space-y-3 mb-6">
              <BenefitItem>Enhanced customer management tools</BenefitItem>
              <BenefitItem>Accurate, real-time quote generation</BenefitItem>
              <BenefitItem>Transparent commission tracking</BenefitItem>
              <BenefitItem>Comprehensive reporting and analytics</BenefitItem>
              <BenefitItem>Streamlined order processing</BenefitItem>
            </ul>

            <Link href="/signup?portal=mystreamlnk-plus" className="block w-full">
              <button className="w-full  bg-[#004235] hover:bg-[#004235]/90 text-white py-2 px-4 rounded-md font-medium mt-4">
                Request Access
              </button>
            </Link>
          </div>
        </div>
      </PortalItem>
    </Accordion>
  )
}
