import { CheckCircle } from "lucide-react"

export function RequestDemoBenefits() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Discover How StreamLnk Can Transform Your Business
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            A live, personalized demo with one of our platform specialists is the best way to understand the full power and potential of StreamLnk for your specific needs.
          </p>
            
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">See Key Portals in Action</h3>
                  <p className="text-gray-600">Get a guided tour relevant to your role (Buyer, Supplier, Agent, Logistics).</p>
                </div>
              </div>
            </div>
              
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Understand AI-Powered Benefits</h3>
                  <p className="text-gray-600">Witness smart sourcing, optimized logistics, StreamIndex™ pricing, and iScore™ risk assessment.</p>
                </div>
              </div>
            </div>
              
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Explore End-to-End Workflow</h3>
                  <p className="text-gray-600">See seamless procurement, freight, customs, payments, and compliance.</p>
                </div>
              </div>
            </div>
              
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Discuss Your Specific Needs</h3>
                  <p className="text-gray-600">Have a dedicated session to address your unique business challenges.</p>
                </div>
              </div>
            </div>
              
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm md:col-span-2">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Get Your Questions Answered</h3>
                  <p className="text-gray-600">Direct interaction with our team to clarify every aspect.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}