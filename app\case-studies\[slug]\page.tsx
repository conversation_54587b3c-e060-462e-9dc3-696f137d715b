import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroBanner from "@/components/case-studies/individual-study/HeroBanner";
import ClientProfileSection from "@/components/case-studies/individual-study/ClientProfileSection";
import ChallengeSection from "@/components/case-studies/individual-study/ChallengeSection";
import SolutionSection from "@/components/case-studies/individual-study/SolutionSection";
import ResultsBenefitsSection from "@/components/case-studies/individual-study/ResultsBenefitsSection";
import TestimonialSection from "@/components/case-studies/individual-study/TestimonialSection";
import FuturePlansSection from "@/components/case-studies/individual-study/FuturePlansSection";
import RelatedLinksSection from "@/components/case-studies/individual-study/RelatedLinksSection";
import { notFound } from 'next/navigation';
import { getCaseStudyData } from '../data';

export default async function IndividualCaseStudyPage({ params }: { params: { slug: string } }) {
  const caseStudyData = await getCaseStudyData(params.slug);

  if (!caseStudyData) {
    notFound(); // This will render the not-found.tsx page if it exists, or a default Next.js 404 page
  }

  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      {/* Pass fetched data to components */}
      <HeroBanner imageUrl={caseStudyData.heroImageUrl} altText={`${caseStudyData.clientName} Case Study Banner`} />
      <ClientProfileSection clientData={caseStudyData} />
      <ChallengeSection caseStudySlug={params.slug} />
      <SolutionSection caseStudySlug={params.slug} />
      <ResultsBenefitsSection caseStudySlug={params.slug} />
      <TestimonialSection caseStudySlug={params.slug} />
      <FuturePlansSection caseStudySlug={params.slug} />
      <RelatedLinksSection />

      <BottomFooter />
    </div>
  );
}