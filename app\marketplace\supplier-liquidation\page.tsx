// app/marketplace/supplier-liquidation/page.tsx
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import { Banknote, BarChart, Calendar, Clock, Coins, DollarSign, Factory, LineChart, Package, Percent, ShoppingBag, Tag, Truck, Warehouse } from "lucide-react";
import Link from "next/link";

import HeroSection from "@/components/marketplace/supplier-liquidation/HeroSection";
import ChallengesSection from "@/components/marketplace/supplier-liquidation/ChallengesSection";
import StrategicToolSection from "@/components/marketplace/supplier-liquidation/StrategicToolSection";
import HowItWorksSection from "@/components/marketplace/supplier-liquidation/HowItWorksSection";
import BenefitsSection from "@/components/marketplace/supplier-liquidation/BenefitsSection";
import CTASection from "@/components/marketplace/supplier-liquidation/CTASection";

export default function SupplierLiquidationPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <StrategicToolSection />

      <HowItWorksSection />

      <BenefitsSection />

      <CTASection />

      <BottomFooter />
    </div>
  );
}