import { HeroSection } from "@/components/partners/streamglobe/hero-section"
import { WhatIsStreamGlobeSection } from "@/components/partners/streamglobe/what-is-streamglobe-section"
import { WhyJoinSection } from "@/components/partners/streamglobe/why-join-section"
import { KeyFeaturesSection } from "@/components/partners/streamglobe/key-features-section"
import { WhoShouldJoinSection } from "@/components/partners/streamglobe/who-should-join-section"
import { ServiceExpectationsSection } from "@/components/partners/streamglobe/service-expectations-section"
import { CustomerCoordinationSection } from "@/components/partners/streamglobe/customer-coordination-section"
import { PoaManagementSection } from "@/components/partners/streamglobe/poa-management-section"
import { HowToJoinSection } from "@/components/partners/streamglobe/how-to-join-section"
import { CtaSection } from "@/components/partners/streamglobe/cta-section"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"

export const metadata = {
  title: "Digitize Your Clearance Services with StreamGlobe | StreamLnk",
  description:
    "Join StreamGlobe as a verified customs agent and connect to international shipments, access auto-populated documentation, and provide real-time clearance updates.",
}

export default function StreamGlobeAgentPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main>
      <HeroSection />
      <WhatIsStreamGlobeSection />
      <WhyJoinSection />
      <KeyFeaturesSection />
      <WhoShouldJoinSection />
      <ServiceExpectationsSection />
      <CustomerCoordinationSection />
      <PoaManagementSection />
      <HowToJoinSection />
      <CtaSection />
      </main>
      <MainFooter />
    </div>
  )
}
