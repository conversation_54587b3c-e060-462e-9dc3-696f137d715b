"use client"

import { Package, Truck, BarChart3, FileText, ShieldCheck, Warehouse } from "lucide-react"

export default function PlatformOverviewSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            StreamPak: Your Integrated Hub for Product Preparation & Storage
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            StreamLnk's StreamPak portal seamlessly integrates warehousing and packaging services into your supply chain workflow. When our AI identifies a product as "Unfinished" or when strategic storage is required, StreamPak connects the need with a qualified partner:
          </p>
          
          <div className="space-y-8">
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Warehouse className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Network of Vetted Partners</h3>
                  <p className="text-gray-700 mb-4">
                    Access certified packaging companies and warehouse operators specializing in industrial materials, ensuring quality and compliance.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Package className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Automated Job Assignment</h3>
                  <p className="text-gray-700 mb-4">
                    When an order requires packaging (e.g., bulk material needs bagging for final delivery) or interim storage, the StreamPak system can automatically assign the job to a suitable partner based on location, capability, and capacity.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Real-Time Inventory Management</h3>
                  <p className="text-gray-700 mb-4">
                    Partners update inventory status (receipt, storage location, condition, dispatch) directly in StreamPak. Mandatory shortage/damage reporting ({'>'}0.1% discrepancy) ensures accountability. You gain visibility into your stock held at partner facilities via your StreamLnk portal.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <FileText className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Packaging Workflow Management</h3>
                  <p className="text-gray-700 mb-4">
                    Detailed job cards with product specifications, required packaging materials, labeling instructions, and SOPs. Partners update progress in real-time (Job Accepted, Product Received, Packaging Started, QC Check, Packaging Complete). Upload of completion certificates, QC reports, and photographic evidence.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Truck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Seamless Logistics Integration</h3>
                  <p className="text-gray-700 mb-4">
                    StreamPak coordinates with StreamFreight for the transport of goods to and from packaging/warehousing facilities.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <ShieldCheck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Compliance & Document Control</h3>
                  <p className="text-gray-700 mb-4">
                    Strict adherence to insurance renewal and certification updates for all StreamPak partners, with portal access linked to compliance status. Centralized storage of all job-related documentation.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}