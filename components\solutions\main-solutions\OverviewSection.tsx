"use client"

export default function OverviewSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            One Platform, Every Solution, for Your Industrial Supply Chain
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            The industrial materials supply chain is complex, involving numerous stakeholders and processes. Traditional methods rely on fragmented systems, leading to inefficiencies, lack of visibility, and increased risk. StreamLnk changes this by offering a unified digital infrastructure where every critical function is connected and intelligent.
          </p>
        </div>
      </div>
    </section>
  )
}