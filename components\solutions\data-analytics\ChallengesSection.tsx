import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Database } from "lucide-react"

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white" id="challenges">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Operating in the Dark? The Cost of Limited Market Intelligence
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Making strategic decisions in the industrial materials sector is often hampered by:
          </p>

          <div className="grid md:grid-cols-2 gap-6">
            {[{
                icon: <BarChart3 className="h-8 w-8 text-[#028475]" />,
                text: "Lack of access to reliable, real-time market pricing and volatility data."
              },
              {
                icon: <TrendingUp className="h-8 w-8 text-[#028475]" />,
                text: "Limited visibility into true supply and demand dynamics across different regions and product grades."
              },
              {
                icon: <LineChart className="h-8 w-8 text-[#028475]" />,
                text: "Difficulty in benchmarking operational performance (logistics, supplier reliability) against industry standards."
              },
              {
                icon: <PieChart className="h-8 w-8 text-[#028475]" />,
                text: "Inability to accurately forecast market shifts or identify emerging risks and opportunities."
              },
              {
                icon: <Database className="h-8 w-8 text-[#028475]" />,
                text: "Data being fragmented across multiple internal systems and external (often outdated) sources."
              }
            ].map((item, index) => (
              <div key={index} className="flex items-start p-4 bg-gray-50 rounded-lg">
                <div className="mr-4 mt-1">{item.icon}</div>
                <p className="text-gray-800">{item.text}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}