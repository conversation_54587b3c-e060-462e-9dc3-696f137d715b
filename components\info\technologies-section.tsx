import { Lightbulb, Layers, Zap, Globe, ShieldCheck } from "lucide-react";

const technologies = [
  {
    icon: <Lightbulb className="h-8 w-8 text-[#004235]" />,
    title: "Artificial Intelligence & Machine Learning",
    description: "For predictive analytics, smart matching, risk scoring, process automation."
  },
  {
    icon: <Layers className="h-8 w-8 text-[#004235]" />,
    title: "Cloud-Native Architecture",
    description: "Ensuring scalability, global accessibility, and reliability (e.g., built on AWS)."
  },
  {
    icon: <Zap className="h-8 w-8 text-[#004235]" />,
    title: "API-First Design",
    description: "Facilitating seamless integration with partner systems and enterprise client ERPs."
  },
  {
    icon: <Globe className="h-8 w-8 text-[#004235]" />,
    title: "Advanced Data Analytics",
    description: "Processing vast amounts of trade data to generate unique market intelligence."
  },
  {
    icon: <ShieldCheck className="h-8 w-8 text-[#004235]" />,
    title: "Bank-Grade Security Protocols",
    description: "Protecting user data and financial transactions."
  },
];

export default function TechnologiesSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">Key Technologies Powering StreamLnk</h2>
          <p className="text-lg text-gray-700">
            Built with a Future-Forward Tech Stack
          </p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {technologies.map(tech => (
            <div key={tech.title} className="bg-[#F2F2F2] p-8 rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 ease-in-out flex flex-col items-center text-center">
              <div className="rounded-full bg-[#004235]/10 w-20 h-20 flex items-center justify-center mb-6 ring-4 ring-[#028475]/20">
                {tech.icon}
              </div>
              <h4 className="text-xl font-semibold text-[#004235] mb-3">{tech.title}</h4>
              <p className="text-gray-600 text-sm">{tech.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}