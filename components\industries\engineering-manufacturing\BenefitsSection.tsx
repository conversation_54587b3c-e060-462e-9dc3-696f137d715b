"use client";

import { Search, BarChart3, Package, Zap, ShieldCheck, Layers, Microscope } from "lucide-react";

const benefits = [
  {
    title: "Streamlined Procurement",
    description: "Simplify the sourcing of diverse materials and components.",
    icon: <Search className="h-8 w-8 text-[#028475] mb-3" />
  },
  {
    title: "Improved Cost Control",
    description: "Access competitive global pricing and optimize landed costs.",
    icon: <BarChart3 className="h-8 w-8 text-[#028475] mb-3" />
  },
  {
    title: "Enhanced Supply Chain Visibility",
    description: "Gain real-time insights into material availability and shipment statuses.",
    icon: <Package className="h-8 w-8 text-[#028475] mb-3" />
  },
  {
    title: "Increased Operational Efficiency",
    description: "Reduce lead times and minimize disruptions to production schedules.",
    icon: <Zap className="h-8 w-8 text-[#028475] mb-3" />
  },
  {
    title: "Better Quality Assurance",
    description: "Source from vetted suppliers with verified certifications.",
    icon: <ShieldCheck className="h-8 w-8 text-[#028475] mb-3" />
  },
  {
    title: "Optimized Inventory Management",
    description: "Make data-driven decisions on stocking levels based on reliable lead times and demand insights.",
    icon: <Layers className="h-8 w-8 text-[#028475] mb-3" />
  },
  {
    title: "Support for Innovation",
    description: "Easily source new and specialized materials for product development.",
    icon: <Microscope className="h-8 w-8 text-[#028475] mb-3" />
  }
];

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">
          Enhance Productivity, Reduce Costs, Build Resilient Operations
        </h2>
        <p className="text-lg text-gray-700 mb-10 text-center">
          Benefits for the Engineering & Manufacturing Sector
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-md text-center">
              {benefit.icon}
              <h3 className="text-xl font-semibold text-[#004235] mb-2">{benefit.title}</h3>
              <p className="text-gray-600">{benefit.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}