import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import Link from 'next/link';
import { Search, ChevronRight, MessageSquare, FileText, LifeBuoy, UploadCloud, FolderOpen, CheckCircle, AlertTriangle, Share2, FileKey2 } from 'lucide-react';

const documentManagementFaqs = [
  {
    question: "What types of compliance documents do I need to upload during onboarding?",
    answer: "This varies by user type (Supplier, Buyer, Service Provider) and region. Generally, it includes business registration, tax ID, relevant operational licenses, and insurance. Specific requirements are detailed during the registration process for your portal."
  },
  {
    question: "How do I know if my uploaded document has been verified?",
    answer: "The status of your documents (Pending Verification, Verified, Action Required, Expired) is visible in the \"Compliance Center\" or \"My Documents\" section of your portal."
  },
  {
    question: "What happens if one of my critical documents (like insurance) expires?",
    answer: "You will receive multiple reminders. If the document expires and is not renewed, your account access or ability to participate in new transactions may be temporarily suspended until a valid document is uploaded and verified, as per our Terms of Service."
  },
  {
    question: "Can I download all my documents for a specific order?",
    answer: "Yes, from the Order Details page or your Document Vault, you can typically download individual documents or a consolidated package (e.g., ZIP file) for your records."
  },
  {
    question: "Who sees the documents I upload?",
    answer: "Your own company compliance documents are primarily for StreamLnk verification. Transaction-specific documents (like a CoA for a product sold) are shared with the direct counterparty of that transaction. StreamLnk adheres to strict data privacy policies."
  }
];

const DocumentManagementPage = () => {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="bg-[#F2F2F2] py-16 md:py-24">
          <div className="container mx-auto px-4 md:px-6 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-4">
              StreamLnk Document Hub
            </h1>
            <p className="text-lg md:text-xl text-gray-700 mb-6 max-w-3xl mx-auto">
              Securely Manage All Your Trade-Related Files
            </p>
            <p className="text-md text-gray-600 mb-8 max-w-2xl mx-auto">
              Find everything you need to know about uploading, accessing, sharing (securely within the platform), and managing your critical business and transaction documents on StreamLnk.
            </p>
            <div className="max-w-xl mx-auto flex flex-col sm:flex-row gap-4">
              <Input
                type="search"
                placeholder='Search Document Help (e.g., "upload CoA," "find invoice")'
                className="flex-grow text-base py-3 px-4"
              />
              <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white text-base">
                <Search className="mr-2 h-5 w-5" /> Search Help
              </Button>
            </div>
          </div>
        </section>

        {/* Why Centralized Document Management Matters Section */}
        <section className="py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] text-center mb-4">
              Streamlining Your Paperwork, Ensuring Compliance
            </h2>
            <p className="text-lg text-gray-600 text-center mb-10 max-w-2xl mx-auto">
              Why Centralized Document Management Matters
            </p>
            <p className="text-gray-700 mb-8 max-w-3xl mx-auto leading-relaxed">
              Global industrial trade generates a significant amount of documentation. Managing these files effectively is crucial for:
            </p>
            <ul className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 text-gray-700 max-w-4xl mx-auto">
              <li className="flex items-start">
                <CheckCircle className="h-6 w-6 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <span><strong>Compliance:</strong> Meeting regulatory requirements for customs, quality, safety, and finance.</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-6 w-6 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <span><strong>Operational Efficiency:</strong> Quick access to the right documents at the right time prevents delays.</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-6 w-6 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <span><strong>Risk Mitigation:</strong> Proper documentation helps resolve disputes and validates transactions.</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-6 w-6 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <span><strong>Audit Readiness:</strong> Easily retrieve records for internal or external audits.</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-6 w-6 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <span><strong>Transparency:</strong> Shared visibility (for relevant parties) of key transaction documents builds trust.</span>
              </li>
            </ul>
          </div>
        </section>

        {/* Your Portal's Document Vault & Compliance Center Section */}
        <section className="bg-slate-50 py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] text-center mb-4">
              Your Portal's Document Vault & Compliance Center
            </h2>
            <p className="text-lg text-gray-600 text-center mb-12 max-w-2xl mx-auto">
              Key Features for Managing Documents in Your StreamLnk Portal
            </p>
            <p className="text-gray-700 mb-8 max-w-3xl mx-auto leading-relaxed">
              Each StreamLnk portal (MyStreamLnk, E-Stream, MyStreamLnk+, StreamFreight, StreamGlobe+, StreamPak) includes features for managing documents relevant to your role:
            </p>
            <div className="space-y-10">
              <div>
                <h3 className="text-2xl font-semibold text-[#004235] mb-3 flex items-center"><UploadCloud className="h-6 w-6 mr-2 text-[#028475]" />Uploading Documents</h3>
                <p className="text-gray-700 mb-2"><strong>Where:</strong> Typically in your "Profile & Settings" under "Compliance Documents," or directly within specific transaction workflows (e.g., uploading a POD for a shipment in StreamFreight, attaching a CoA to a product listing in E-Stream).</p>
                <p className="text-gray-700 mb-2"><strong>Accepted Formats:</strong> PDF, JPG, PNG, DOCX, XLSX (Please check specific sections for exact supported types and size limits).</p>
                <p className="text-gray-700"><strong>Tips for Successful Uploads:</strong> Ensure clear scans, correct file naming, and that you're uploading to the correct section.</p>
              </div>
              <div>
                <h3 className="text-2xl font-semibold text-[#004235] mb-3 flex items-center"><FolderOpen className="h-6 w-6 mr-2 text-[#028475]" />Accessing & Viewing Documents</h3>
                <p className="text-gray-700 mb-2"><strong>Document Vault (MyStreamLnk/E-Stream):</strong> Central repository for all your transaction-related documents (POs, Invoices, B/Ls, CoAs, Customs Declarations). Filterable by order, date, document type.</p>
                <p className="text-gray-700 mb-2"><strong>Compliance Center (All Portals):</strong> Manage your own company's compliance documents (licenses, insurance, certifications). View status (Verified, Expiring Soon, Expired).</p>
                <p className="text-gray-700"><strong>Shipment/Order Details Pages:</strong> Directly access documents linked to specific transactions.</p>
              </div>
              <div>
                <h3 className="text-2xl font-semibold text-[#004235] mb-3 flex items-center"><AlertTriangle className="h-6 w-6 mr-2 text-[#028475]" />Document Expiry & Renewals</h3>
                <p className="text-gray-700 mb-2">StreamLnk automatically tracks expiry dates for key compliance documents (e.g., insurance, licenses, certifications).</p>
                <p className="text-gray-700 mb-2">You will receive automated email and in-portal reminders (e.g., 30, 14, 7 days before expiry).</p>
                <p className="text-gray-700 font-semibold">Action Required: It is your responsibility to upload renewed documents before the expiry date to maintain active platform access and compliance. Failure to do so may result in temporary account suspension or inability to participate in new transactions.</p>
              </div>
              <div>
                <h3 className="text-2xl font-semibold text-[#004235] mb-3 flex items-center"><Share2 className="h-6 w-6 mr-2 text-[#028475]" />Sharing Documents (Securely within the platform)</h3>
                <p className="text-gray-700 mb-2">Documents uploaded for a specific transaction (e.g., a supplier's CoA for an order) are made accessible to the relevant counterparty (e.g., the buyer for that order) through their portal.</p>
                <p className="text-gray-700">StreamLnk does not facilitate uncontrolled external sharing of sensitive documents.</p>
              </div>
              <div>
                <h3 className="text-2xl font-semibold text-[#004235] mb-3 flex items-center"><FileKey2 className="h-6 w-6 mr-2 text-[#028475]" />Power of Attorney (POA) Management (StreamGlobe+ for Customs Agents)</h3>
                <p className="text-gray-700">Dedicated section for uploading, managing, and tracking the status of POAs required for customs clearance.</p>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] text-center mb-10">
              Frequently Asked Questions – Document Management
            </h2>
            <Accordion type="single" collapsible className="w-full max-w-3xl mx-auto">
              {documentManagementFaqs.map((faq, index) => (
                <AccordionItem key={index} value={`item-${index}`}>
                  <AccordionTrigger className="text-left hover:no-underline text-lg font-medium text-gray-800">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-600 text-base leading-relaxed pt-2 pb-4">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </section>

        {/* Best Practices Section */}
        <section className="bg-[#F2F2F2] py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] text-center mb-4">
              Best Practices for a Smooth Experience
            </h2>
            <p className="text-lg text-gray-600 text-center mb-10 max-w-2xl mx-auto">
              Tips for Efficient Document Management on StreamLnk
            </p>
            <ul className="grid grid-cols-1 md:grid-cols-2 gap-6 text-gray-700 max-w-3xl mx-auto">
              <li className="flex items-start p-4 bg-white rounded-lg shadow">
                <CheckCircle className="h-5 w-5 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <span>Keep digital copies of all your key compliance documents readily available.</span>
              </li>
              <li className="flex items-start p-4 bg-white rounded-lg shadow">
                <CheckCircle className="h-5 w-5 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <span>Pay close attention to expiry dates and upload renewals well in advance.</span>
              </li>
              <li className="flex items-start p-4 bg-white rounded-lg shadow">
                <CheckCircle className="h-5 w-5 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <span>Use clear and consistent file naming conventions.</span>
              </li>
              <li className="flex items-start p-4 bg-white rounded-lg shadow">
                <CheckCircle className="h-5 w-5 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <span>Ensure all scanned documents are legible and complete.</span>
              </li>
              <li className="flex items-start p-4 bg-white rounded-lg shadow md:col-span-2">
                <CheckCircle className="h-5 w-5 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <span>Regularly check your portal's "Compliance Center" for any pending actions.</span>
              </li>
            </ul>
          </div>
        </section>

        {/* Need Help Section */}
        <section className="py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
              Need Help with Your Documents?
            </h2>
            <p className="text-lg text-gray-600 mb-8 max-w-xl mx-auto">
              Our Support Team Can Assist with Document-Related Queries. If you have questions during your onboarding process that aren't covered in these guides, our support teams are ready to help.
            </p>
            <div className="flex flex-col sm:flex-row justify-center items-center gap-4 mb-6">
              <Link href="/support/submit-ticket?category=Document%20Management" passHref>
                <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto">
                  <FileText className="mr-2 h-5 w-5" /> Submit A Support Ticket
                </Button>
              </Link>
              <Link href="/contact-us" passHref>
                <Button size="lg" variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto">
                  <MessageSquare className="mr-2 h-5 w-5" /> Contact Customer Service
                </Button>
              </Link>
            </div>
            <Link href="/resources/guides/onboarding" passHref> {/* Assuming onboarding guides are at this path */}
              <Button variant="link" className="text-[#028475] hover:text-[#004235] text-lg">
                <LifeBuoy className="mr-2 h-5 w-5" /> Explore Our Onboarding Guides <ChevronRight className="ml-1 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </section>

      </main>
      <BottomFooter />
    </div>
  );
};

export default DocumentManagementPage;