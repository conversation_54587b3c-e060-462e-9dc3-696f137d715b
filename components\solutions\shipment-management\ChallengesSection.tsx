import { Layers, Clock, AlertCircle, Users, FileText } from "lucide-react";

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Losing Track? The High Cost of Poor Shipment Management
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            Managing multiple industrial shipments, especially across different carriers and international borders, can quickly become overwhelming. Businesses often struggle with:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <div className="flex items-start">
                <div className="bg-[#004235]/10 p-2 rounded-full flex items-center justify-center mr-4">
                  <Layers className="text-[#028475] h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-3">Fragmented Information</h3>
                  <p className="text-gray-700">
                    Lack of a single source of truth for all shipment statuses.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <div className="flex items-start">
                <div className="bg-[#004235]/10 p-2 rounded-full flex items-center justify-center mr-4">
                  <Clock className="text-[#028475] h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-3">Time Wasted</h3>
                  <p className="text-gray-700">
                    Hours spent chasing updates from various freight forwarders, carriers, and customs brokers.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <div className="flex items-start">
                <div className="bg-[#004235]/10 p-2 rounded-full flex items-center justify-center mr-4">
                  <AlertCircle className="text-[#028475] h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-3">Reactive Problem-Solving</h3>
                  <p className="text-gray-700">
                    Difficulty in proactively identifying and addressing potential delays.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <div className="flex items-start">
                <div className="bg-[#004235]/10 p-2 rounded-full flex items-center justify-center mr-4">
                  <Users className="text-[#028475] h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-3">Stakeholder Communication</h3>
                  <p className="text-gray-700">
                    Inability to provide timely and accurate updates to internal stakeholders or end customers.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <div className="flex items-start">
                <div className="bg-[#004235]/10 p-2 rounded-full flex items-center justify-center mr-4">
                  <FileText className="text-[#028475] h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-3">Document Management</h3>
                  <p className="text-gray-700">
                    Complex document management for each individual shipment.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}