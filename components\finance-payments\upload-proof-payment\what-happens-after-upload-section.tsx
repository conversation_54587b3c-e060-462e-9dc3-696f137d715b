import AfterUploadCard from "@/components/finance-payments/upload-proof-payment/after-upload-card"
import { CheckCircle2, Bell, AlertCircle, Clock } from "lucide-react"

export default function WhatHappensAfterUploadSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">What Happens After Upload</h2>
          <p className="text-gray-600 max-w-3xl">
            Once you've uploaded your proof of payment, several processes are initiated.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <AfterUploadCard
            icon={<CheckCircle2 className="h-8 w-8 text-[#028475]" />}
            title="Dashboard Confirmation"
            description="A payment confirmation will appear in your dashboard"
          />
          <AfterUploadCard
            icon={<Bell className="h-8 w-8 text-[#028475]" />}
            title="Partner Notification"
            description="Suppliers and logistics providers are notified instantly"
          />
          <AfterUploadCard
            icon={<AlertCircle className="h-8 w-8 text-[#028475]" />}
            title="Issue Resolution"
            description="If there are issues, StreamLnk Support will contact you"
          />
          <AfterUploadCard
            icon={<Clock className="h-8 w-8 text-[#028475]" />}
            title="Workflow Resumption"
            description="Shipment and documentation workflows resume immediately once POP is validated"
          />
        </div>
      </div>
    </section>
  )
}