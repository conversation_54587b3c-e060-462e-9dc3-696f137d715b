"use client";

import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";
import HeroSection from "@/components/partners/strategic-alliances/HeroSection";
import PowerOfCollaborationSection from "@/components/partners/strategic-alliances/PowerOfCollaborationSection";
import AllianceTypesSection from "@/components/partners/strategic-alliances/AllianceTypesSection";
import PartnerQualitiesSection from "@/components/partners/strategic-alliances/PartnerQualitiesSection";
import InitiatingDiscussionSection from "@/components/partners/strategic-alliances/InitiatingDiscussionSection";

export default function StrategicAlliancesPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />
      <main>
        <HeroSection />
        <PowerOfCollaborationSection />
        <AllianceTypesSection />
        <PartnerQualitiesSection />
        <InitiatingDiscussionSection />
      </main>
      <MainFooter />
    </div>
  );
}