"use client";

import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight, FileText, Users, Info } from 'lucide-react';

export default function CtaSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            Join the StreamLnk Ecosystem and Connect Your Services to a New Wave of Industrial Cargo.
          </h2>
          <p className="text-xl text-gray-700 mb-10">
            Partner with StreamLnk to Power Global Maritime Trade
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-6">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/contact-us?subject=Carrier+Partnership+Inquiry">
                CONTACT PARTNERSHIP TEAM
                <Users className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button 
              variant="outline" 
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/developer/api-documentation/streamglobe-carrier?request=true">
                REQUEST API DOCUMENTATION
                <FileText className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="mt-6">
            <Button 
              variant="link" 
              className="text-[#028475] hover:text-[#004235]"
              asChild
            >
              <Link href="/company/global-forwarding">
                LEARN ABOUT GLOBAL FORWARDING
                <Info className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}