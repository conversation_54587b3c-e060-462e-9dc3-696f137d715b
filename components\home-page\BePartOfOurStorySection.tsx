import { Calendar, FileText, BookOpen, ChevronRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export function BePartOfOurStorySection() {
  return (
    <section className="py-24 md:py-32 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16 md:mb-20">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Stay Connected with StreamLnk
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Keep up with the latest in energy trading, market insights, and platform updates
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
          {/* Industry Events */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-[#004235]/10 rounded-lg">
                <Calendar className="h-6 w-6 text-[#004235]" />
              </div>
              <h3 className="text-xl font-semibold text-[#004235]">Industry Events</h3>
            </div>
            <p className="text-gray-600 mb-6">
              Join us at energy trade shows, conferences, and events worldwide.
            </p>
            <Button variant="outline" className="w-full border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white">
              <ChevronRight className="h-4 w-4 mr-2" />
              View Events
            </Button>
          </div>

          {/* Market Insights */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-[#18b793]/10 rounded-lg">
                <FileText className="h-6 w-6 text-[#18b793]" />
              </div>
              <h3 className="text-xl font-semibold text-[#004235]">Market Insights</h3>
            </div>
            <p className="text-gray-600 mb-6">
              Access expert analysis, market trends, and strategic insights for energy trading.
            </p>
            <Button variant="outline" className="w-full border-[#18b793] text-[#18b793] hover:bg-[#18b793] hover:text-white">
              <ChevronRight className="h-4 w-4 mr-2" />
              Read Insights
            </Button>
          </div>

          {/* Resource Center */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-[#a4dcb4]/20 rounded-lg">
                <BookOpen className="h-6 w-6 text-[#004235]" />
              </div>
              <h3 className="text-xl font-semibold text-[#004235]">Resource Center</h3>
            </div>
            <p className="text-gray-600 mb-6">
              Download guides, whitepapers, and tools to optimize your energy trading operations.
            </p>
            <Button variant="outline" className="w-full border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white">
              <ChevronRight className="h-4 w-4 mr-2" />
              Explore Resources
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}