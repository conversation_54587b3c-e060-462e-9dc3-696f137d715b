import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              The StreamLnk Difference: Your Gateway to a Verified Global Industrial Network
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Break down geographical barriers and unlock worldwide opportunities. StreamLnk connects you to a vetted, ever-expanding network of participants across the entire industrial materials supply chain, from local experts to international players.
            </p>
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/request-demo?feature=global-network">
                EXPLORE NETWORK
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="relative w-full h-[300px] md:h-[450px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/global-network-hero.webp" // Placeholder - suggest user to replace
              alt="StreamLnk Global Industrial Network"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: User to replace with a relevant image for global network */}
          </div>
        </div>
      </div>
    </section>
  );
}