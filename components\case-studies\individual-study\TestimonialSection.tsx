import { Quote } from 'lucide-react';

// Placeholder for fetching testimonial content based on slug
// In a real app, this data would come from a CMS or database
const getTestimonialContent = async (slug: string) => {
  // Simulate fetching data
  await new Promise(resolve => setTimeout(resolve, 100));
  // Example content structure - adapt as needed
  if (slug === "sample-case-study-1") {
    return {
      quote: "StreamLnk didn't just provide a software solution; they became a strategic partner in our growth. Their platform has been a game-changer for our operational efficiency and visibility.",
      author: "<PERSON>, COO",
      company: "Acme Corp"
    };
  }
  // Fallback or default content
  return {
    quote: "A strong, compelling quote from the client highlighting the value they received from StreamLnk. This should be impactful and persuasive.",
    author: "Client Name, Title",
    company: "Client Company"
  };
};

interface TestimonialSectionProps {
  caseStudySlug: string;
}

const TestimonialSection: React.FC<TestimonialSectionProps> = async ({ caseStudySlug }) => {
  const content = await getTestimonialContent(caseStudySlug);

  return (
    <section className="py-12 md:py-16 lg:py-20 bg-[#004235] text-white">
      <div className="container mx-auto px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center">
          <Quote className="w-12 h-12 text-[#028475] mx-auto mb-6" />
          <blockquote className="text-2xl md:text-3xl lg:text-4xl italic font-medium mb-6 leading-relaxed">
            <p>"{content.quote}"</p>
          </blockquote>
          <cite className="block text-lg md:text-xl font-semibold text-gray-200">
            {content.author}
          </cite>
          <p className="text-md text-gray-300">{content.company}</p>
        </div>
      </div>
    </section>
  );
};

export default TestimonialSection;