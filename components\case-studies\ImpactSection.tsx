"use client";

import { CheckCircle } from "lucide-react";

export default function ImpactSection() {
  const impacts = [
    {
      title: "Reduce Procurement Costs & Time",
      description: "Through competitive sourcing and streamlined workflows.",
    },
    {
      title: "Expand Global Market Reach",
      description: "Connecting suppliers with new international buyers.",
    },
    {
      title: "Optimize Logistics & Fulfillment",
      description: "Achieving faster, more reliable, and cost-effective shipping.",
    },
    {
      title: "Enhance Operational Visibility",
      description: "Gaining real-time insights into every stage of the supply chain.",
    },
    {
      title: "Mitigate Risks & Ensure Compliance",
      description: "Transacting with greater security and confidence.",
    },
    {
      title: "Improve Cash Flow",
      description: "Utilizing flexible payment and trade finance solutions.",
    },
    {
      title: "Achieve Sustainability Goals",
      description: "By enabling smarter sourcing and more efficient logistics.",
    },
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">
            Driving Efficiency, Transparency, and Growth Across the Ecosystem
          </h2>
          <p className="text-xl text-[#028475] font-semibold mb-6">
            The StreamLnk Impact – Measurable Value, Real Results
          </p>
          <p className="text-lg text-gray-700">
            At StreamLnk, we are committed to delivering measurable results for every participant in our digital trade ecosystem. Our platform is engineered to solve critical pain points in industrial sourcing, logistics, finance, and compliance. These case studies highlight how businesses like yours are leveraging StreamLnk to:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {impacts.map((impact, index) => (
            <div key={index} className="bg-[#f3f4f6] p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-center mb-3">
                <CheckCircle className="text-[#028475] h-7 w-7 mr-3 flex-shrink-0" />
                <h3 className="text-xl font-semibold text-[#004235]">
                  {impact.title}
                </h3>
              </div>
              <p className="text-gray-600 text-sm ml-10">
                {impact.description}
              </p>
            </div>
          ))}
        </div>
        <p className="text-center text-lg text-gray-700 mt-12">
          Explore the stories below to see the StreamLnk difference.
        </p>
      </div>
    </section>
  );
}