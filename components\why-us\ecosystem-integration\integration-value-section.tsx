import { Zap, Eye, AlertTriangle, Users, BarChart3, Shield<PERSON>heck, Scaling, CheckCircle } from "lucide-react";

export default function IntegrationValueSection() {
  const benefits = [
    {
      icon: <Zap className="h-7 w-7 text-[#028475]" />,
      title: "Unparalleled Efficiency",
      description: "Eliminate manual data entry, reduce administrative overhead, and accelerate transaction cycles with automated workflows and seamless data transfer."
    },
    {
      icon: <Eye className="h-7 w-7 text-[#028475]" />,
      title: "Complete End-to-End Visibility",
      description: "Gain a single, real-time view of your entire supply chain – from initial RFQ to final delivery and payment – across all partners and geographies."
    },
    {
      icon: <AlertTriangle className="h-7 w-7 text-[#028475]" />,
      title: "Reduced Errors & Miscommunication",
      description: "A unified data source and standardized processes minimize the risk of errors, discrepancies, and miscommunications that plague fragmented systems."
    },
    {
      icon: <Users className="h-7 w-7 text-[#028475]" />,
      title: "Enhanced Collaboration",
      description: "Foster smoother collaboration between buyers, suppliers, and all service providers operating within a shared, transparent digital environment."
    },
    {
      icon: <BarChart3 className="h-7 w-7 text-[#028475]" />,
      title: "Holistic Data Insights",
      description: "Only an integrated ecosystem can generate the comprehensive data needed for truly powerful analytics (StreamIndex™, iScore™), enabling smarter strategic decisions."
    },
    {
      icon: <ShieldCheck className="h-7 w-7 text-[#028475]" />,
      title: "Superior Risk Management",
      description: "Integrated compliance checks, secure payment flows, and real-time performance monitoring across the entire chain significantly reduce operational and financial risks."
    },
    {
      icon: <Scaling className="h-7 w-7 text-[#028475]" />,
      title: "Scalability & Adaptability",
      description: "Our modular, integrated architecture allows StreamLnk to easily scale to new regions, add new verticals, and incorporate new technologies without disrupting the core ecosystem."
    }
  ];

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Why True Integration Delivers Unmatched Value
          </h2>
          <p className="text-lg text-gray-700">
            Tangible Benefits of StreamLnk's Ecosystem Integration
          </p>
        </div>
        <div className="max-w-5xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((item, index) => (
              <div key={index} className="flex flex-col items-center text-center p-6 bg-gray-50 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <div className="rounded-full bg-[#028475]/10 w-16 h-16 flex items-center justify-center mb-4">
                  {item.icon}
                </div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">{item.title}</h3>
                <p className="text-gray-600 text-sm">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}