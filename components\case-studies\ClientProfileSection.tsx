"use client";

interface ClientProfileProps {
  companyName: string;
  industry: string;
  size?: string;
  keyRole: string;
}

export default function ClientProfileSection({
  companyName,
  industry,
  size,
  keyRole,
}: ClientProfileProps) {
  return (
    <section className="mb-12 p-6 border border-gray-200 rounded-lg shadow-sm bg-white">
      <h2 className="text-2xl font-semibold text-[#004235] mb-4 border-b border-gray-300 pb-3">
        Client Profile
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 text-gray-700">
        <div>
          <strong className="block text-sm text-gray-500">Company</strong>
          <p className="text-lg">{companyName}</p>
        </div>
        <div>
          <strong className="block text-sm text-gray-500">Industry</strong>
          <p className="text-lg">{industry}</p>
        </div>
        {size && (
          <div>
            <strong className="block text-sm text-gray-500">Size</strong>
            <p className="text-lg">{size}</p>
          </div>
        )}
        <div>
          <strong className="block text-sm text-gray-500">Key Role</strong>
          <p className="text-lg">{keyRole}</p>
        </div>
      </div>
    </section>
  );
}