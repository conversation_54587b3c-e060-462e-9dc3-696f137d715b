import { ShieldCheck, CheckCircle, BarChart3, Globe, Lock, FileText, Users, Building } from 'lucide-react'

export default function FeaturesSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center justify-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Verification at Scale: How StreamLnk Ensures a Trusted Network
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk embeds KYC/AML verification directly into the onboarding process for every user type – Suppliers (E-Stream), Buyers (MyStreamLnk), Agents (MyStreamLnk+), and all Service Providers (StreamFreight, StreamGlobe+, StreamPak). Our approach combines automated checks with expert review:
          </p>

          <div className="space-y-8">
            {/* Feature 1 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <FileText className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Mandatory Document Submission</h3>
                  <p className="text-gray-700 mb-2">
                    Collection of official business registration documents, tax IDs, and proof of address.
                  </p>
                  <p className="text-gray-700">
                    Identification documents for key individuals (UBOs - Ultimate Beneficial Owners, directors).
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 2 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <CheckCircle className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Automated Identity Verification</h3>
                  <p className="text-gray-700">
                    Integration with leading third-party KYC/AML solution providers (e.g., SumSub, Jumio, Onfido - illustrative) for document authentication and biometric verification where applicable.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 3 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <Globe className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Global Watchlist & Sanctions Screening</h3>
                  <p className="text-gray-700">
                    Automated, ongoing screening of all registered entities and key personnel against international sanctions lists (OFAC, EU, UN, etc.), PEP (Politically Exposed Persons) lists, and adverse media databases.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 4 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <Building className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Business Verification Checks</h3>
                  <p className="text-gray-700">
                    Validation of company existence, operational status, and consistency of provided information.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 5 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Risk-Based Approach</h3>
                  <p className="text-gray-700">
                    Different levels of due diligence may be applied based on the perceived risk profile of the entity, transaction value, or jurisdiction.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 6 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <Users className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">StreamLnk Compliance Team Review</h3>
                  <p className="text-gray-700 mb-2">
                    Our dedicated internal compliance team reviews flagged applications and performs enhanced due diligence where necessary.
                  </p>
                  <p className="text-gray-700">
                    Manages escalations and ensures adherence to StreamLnk's compliance policies.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 7 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <ShieldCheck className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Continuous Monitoring (Future Enhancement)</h3>
                  <p className="text-gray-700">
                    Plans for ongoing monitoring of existing users for changes in risk status.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 8 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <Lock className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Secure Data Handling</h3>
                  <p className="text-gray-700">
                    All sensitive KYC/AML data is encrypted and managed in accordance with strict data privacy regulations (GDPR, CCPA).
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}