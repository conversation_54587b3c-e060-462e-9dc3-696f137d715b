"use client";

import { Search, Factory, Package, Users, Microscope, BarChart3 } from "lucide-react";

const solutions = [
  {
    title: "MyStreamLnk for Component & Material Sourcing",
    description: [
      "Discover and source a vast range of industrial inputs: raw materials (metals, alloys, engineering polymers, composites), standard components, custom-fabricated parts (via RFQ to specialized suppliers), and MRO supplies.",
      "Advanced filtering by technical specification, material grade, supplier certification (ISO 9001, etc.), and iScore™ reliability.",
      "Manage complex RFQs for multi-component orders or project-based procurement.",
    ],
    icon: <Search className="h-8 w-8 text-[#028475]" />
  },
  {
    title: "E-Stream for Industrial Suppliers & Component Manufacturers",
    description: [
      "List detailed catalogs of raw materials, engineered components, MRO items, and custom manufacturing capabilities.",
      "Showcase quality certifications and adherence to industry standards.",
      "Receive targeted RFQs from global engineering and manufacturing buyers.",
    ],
    icon: <Factory className="h-8 w-8 text-[#028475]" />
  },
  {
    title: "Integrated Logistics for Timely Delivery (StreamFreight, StreamGlobe+)",
    description: [
      "Coordinate domestic and international shipping for raw materials, components, and finished goods.",
      "Real-time tracking to support Just-in-Time (JIT) inventory and production schedules.",
      "Specialized handling for sensitive or oversized engineered products.",
    ],
    icon: <Package className="h-8 w-8 text-[#028475]" />
  },
  {
    title: "Supply Chain Visibility & Collaboration Tools",
    description: [
      "Gain better visibility into supplier lead times, production statuses (where shared), and inbound shipments.",
      "Facilitate smoother communication and document exchange with suppliers and logistics partners.",
    ],
    icon: <Users className="h-8 w-8 text-[#028475]" />
  },
  {
    title: "Quality Assurance & Compliance Support",
    description: [
      "Access supplier certifications and product compliance documentation directly on the platform.",
      "Utilize iScore™ to assess supplier quality and reliability history.",
      "(Future) Tools for managing quality inspection reports and material traceability.",
    ],
    icon: <Microscope className="h-8 w-8 text-[#028475]" />
  },
  {
    title: "StreamResources+ for Manufacturing Intelligence",
    description: [
      "Access StreamIndex™ benchmarks for key industrial material pricing.",
      "Analyze trends in component availability and supplier performance.",
      "Gain insights into regional manufacturing capabilities and sourcing opportunities.",
    ],
    icon: <BarChart3 className="h-8 w-8 text-[#028475]" />
  }
];

export default function SolutionsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">
            Your Digital Toolkit for Leaner, Smarter, More Agile Operations
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk's Integrated Solutions for Engineering & Manufacturing
          </p>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk offers a suite of tools and a connected network designed to streamline procurement, logistics, and data management for engineering and manufacturing companies:
          </p>

          <div className="space-y-8">
            {solutions.map((solution, index) => (
              <div key={index} className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
                <div className="flex flex-col md:flex-row md:items-start gap-6">
                  <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                    {solution.icon}
                  </div>
                  <div className="flex-grow">
                    <h3 className="text-2xl font-bold text-[#004235] mb-3">{solution.title}</h3>
                    <ul className="space-y-2 text-gray-700">
                      {solution.description.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-start"><span className="text-[#028475] mr-2">•</span>{item}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}