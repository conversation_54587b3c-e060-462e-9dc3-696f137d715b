"use client";

import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer"; // Assuming MainFooter is standard, adjust if BottomFooter is used elsewhere
import HeroSection from "@/components/industries/engineering-manufacturing/HeroSection";
import ChallengesSection from "@/components/industries/engineering-manufacturing/ChallengesSection";
import SolutionsSection from "@/components/industries/engineering-manufacturing/SolutionsSection";
import UseCasesSection from "@/components/industries/engineering-manufacturing/UseCasesSection";
import BenefitsSection from "@/components/industries/engineering-manufacturing/BenefitsSection";
import CTASection from "@/components/industries/engineering-manufacturing/CTASection";

export default function EngineeringManufacturingPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <ChallengesSection />
      <SolutionsSection />
      <UseCasesSection />
      <BenefitsSection />
      <CTASection />

      <MainFooter />
    </div>
  );
}