import Link from "next/link"
import Image from "next/image"
import { ChevronRight } from "lucide-react"
import { CountrySelector } from "@/components/country-selector"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DivisionCard } from "@/components/division-card"

export default function OurDivisionsPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <CountrySelector />
      <MainNav />

      <main>
        {/* Hero Section */}
        <section className="bg-[#F2F2F2] py-16 md:py-24">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl">
              <div className="flex items-center mb-6">
                <div className="w-12 h-1 bg-[#028475] mr-3"></div>
                <span className="text-[#028475] font-medium">OUR ORGANIZATION</span>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
                Specialized Divisions, <br />
                Integrated Solutions
              </h1>
              <p className="text-lg text-gray-700 mb-8 max-w-2xl">
                StreamLnk operates through five specialized divisions, each focused on delivering excellence in their
                respective domains while working together to provide comprehensive end-to-end solutions.
              </p>
            </div>
          </div>
        </section>

        {/* Divisions Overview */}
        <section className="py-16 md:py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mb-12">
              <h2 className="text-3xl font-bold text-[#004235] mb-6 flex items-center">
                <span className="w-10 h-1 bg-[#028475] mr-3"></span>
                Our Divisional Structure
              </h2>
              <p className="text-lg text-gray-700">
                Our divisional structure allows us to maintain specialized expertise while ensuring seamless integration
                across the entire supply chain. Each division is led by industry experts and supported by cutting-edge
                technology to deliver exceptional service and value to our customers.
              </p>
            </div>

            {/* Division Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              <DivisionCard
                title="Supply Chain"
                description="End-to-end supply chain management solutions including procurement, inventory management, and distribution optimization."
                imageSrc="/images/divisions/supply-chain.webp"
                href="/our-divisions/supply-chain"
                iconSrc="/images/icons/supply-chain-icon.png"
              />
              <DivisionCard
                title="Global Forwarding"
                description="International freight forwarding services across air, ocean, and multimodal transport with customs expertise."
                imageSrc="/images/divisions/global-forwarding.webp"
                href="/our-divisions/global-forwarding"
                iconSrc="/images/icons/global-forwarding-icon.png"
              />
              <DivisionCard
                title="Freight"
                description="Domestic transportation solutions including FTL, LTL, and specialized freight services with real-time tracking."
                imageSrc="/images/divisions/freight.webp"
                href="/our-divisions/freight"
                iconSrc="/images/icons/freight-icon.png"
              />
              <DivisionCard
                title="eCommerce"
                description="Specialized logistics solutions for online retailers, including fulfillment, last-mile delivery, and returns management."
                imageSrc="/images/divisions/ecommerce.webp"
                href="/our-divisions/ecommerce"
                iconSrc="/images/icons/ecommerce-icon.png"
              />
              <DivisionCard
                title="Resources"
                description="Data analytics, consulting services, and industry insights to optimize supply chain performance and efficiency."
                imageSrc="/images/divisions/resources.webp"
                href="/our-divisions/resources"
                iconSrc="/images/icons/resources-icon.png"
              />
            </div>
          </div>
        </section>

        {/* Integration Section */}
        <section className="bg-[#f3f4f6] py-16 md:py-24">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="flex items-center mb-6">
                  <div className="w-12 h-1 bg-[#028475] mr-3"></div>
                  <span className="text-[#028475] font-medium">BETTER TOGETHER</span>
                </div>
                <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
                  Seamless Integration Across Divisions
                </h2>
                <p className="text-lg text-gray-700 mb-6">
                  While each division maintains specialized expertise, StreamLnk's strength lies in our ability to
                  integrate services across divisions to create comprehensive solutions tailored to your unique needs.
                </p>
                <ul className="space-y-4 mb-8">
                  <li className="flex items-start">
                    <ChevronRight className="h-5 w-5 text-[#028475] mt-1 flex-shrink-0" />
                    <span className="text-gray-700">
                      <span className="font-medium">Unified Technology Platform:</span> All divisions operate on our
                      proprietary StreamLnk Connect platform, ensuring data consistency and visibility.
                    </span>
                  </li>
                  <li className="flex items-start">
                    <ChevronRight className="h-5 w-5 text-[#028475] mt-1 flex-shrink-0" />
                    <span className="text-gray-700">
                      <span className="font-medium">Cross-Divisional Teams:</span> Dedicated account managers coordinate
                      services across divisions for a seamless customer experience.
                    </span>
                  </li>
                  <li className="flex items-start">
                    <ChevronRight className="h-5 w-5 text-[#028475] mt-1 flex-shrink-0" />
                    <span className="text-gray-700">
                      <span className="font-medium">Integrated Solutions:</span> Custom solutions drawing expertise from
                      multiple divisions to address complex supply chain challenges.
                    </span>
                  </li>
                </ul>
                <Button className="bg-[#004235] hover:bg-[#004235]/90 text-white" asChild>
                  <Link href="/contact">Discuss Your Requirements</Link>
                </Button>
              </div>
              <div className="relative">
                <Image
                  src="/images/divisions/integration-diagram.webp"
                  alt="StreamLnk Divisional Integration"
                  width={600}
                  height={500}
                  className="rounded-lg shadow-lg"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Leadership Section */}
        <section className="py-16 md:py-24">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-3xl mx-auto mb-12">
              <div className="flex items-center justify-center mb-6">
                <div className="w-12 h-1 bg-[#028475]"></div>
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">Divisional Leadership</h2>
              <p className="text-lg text-gray-700">
                Each StreamLnk division is led by industry veterans with decades of experience, ensuring we deliver
                best-in-class solutions and stay at the forefront of industry innovation.
              </p>
            </div>
            <div className="flex justify-center">
              <Button className="bg-[#004235] hover:bg-[#004235]/90 text-white" asChild>
                <Link href="/about-us#leadership">Meet Our Leadership Team</Link>
              </Button>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-[#F2F2F2] py-16 md:py-24">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
                Ready to Experience the StreamLnk Advantage?
              </h2>
              <p className="text-lg text-gray-700 mb-8">
                Contact us today to learn how our integrated divisional approach can transform your supply chain and
                logistics operations.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Button className="bg-[#004235] hover:bg-[#004235]/90 text-white" size="lg" asChild>
                  <Link href="/contact">Contact Us</Link>
                </Button>
                <Button variant="outline" className="border-[#004235] text-[#004235]" size="lg" asChild>
                  <Link href="/request-demo">Request a Demo</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      <MainFooter />
    </div>
  )
}
