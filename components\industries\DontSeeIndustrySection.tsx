import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"

export default function DontSeeIndustrySection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Don't See Your Industry?
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            StreamLnk's platform is adaptable. Our core functionalities for sourcing, logistics, finance, and compliance apply across many industrial B2B sectors.
          </p>
          <Button 
            className="bg-[#004235] hover:bg-[#028475] text-white px-6"
            size="lg"
            asChild
          >
            <Link href="/request-demo">
              Request Industry Demo
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}