import { Repeat, Smartphone, BarChart3, Briefcase, ShieldCheck, Users } from "lucide-react"

export function BenefitsSection() {
  const benefits = [
    {
      icon: <Repeat className="h-12 w-12 text-white" />,
      title: "Recurring Revenue Stream",
      description: "Earn commissions for every transaction made by the clients you onboard and manage.",
    },
    {
      icon: <Smartphone className="h-12 w-12 text-white" />,
      title: "Digitize & Enhance Relationships",
      description:
        "Utilize live RFQ tools, transparent document management, and real-time order tracking to provide superior service to your clients.",
    },
    {
      icon: <BarChart3 className="h-12 w-12 text-white" />,
      title: "Exclusive Data & Insights",
      description:
        "Access valuable analytics to track your clients' purchasing trends, identify their evolving needs, and understand their spending patterns.",
    },
    {
      icon: <Briefcase className="h-12 w-12 text-white" />,
      title: "Operate with Autonomy",
      description:
        "Work independently as your own boss, with StreamLnk providing the robust infrastructure, technology, and operational support.",
    },
    {
      icon: <ShieldCheck className="h-12 w-12 text-white" />,
      title: "No Upfront Investment or Inventory Risk",
      description:
        "Focus on building relationships and driving sales without the burden of capital expenditure or holding stock.",
    },
    {
      icon: <Users className="h-12 w-12 text-white" />,
      title: "Seamless Client Transition",
      description:
        "Should you choose to leave, your clients are smoothly transitioned to StreamLnk's internal support, ensuring their continued service.",
    },
  ]

  return (
    <section className="py-20 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">Why Become a MyStreamLnk+ Agent?</h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            Partnering with MyStreamLnk+ offers unparalleled benefits for ambitious sales agents and distributors:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300"
            >
              <div className="bg-gradient-to-r from-[#004235] to-[#028475] p-4 flex justify-center">{benefit.icon}</div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-[#004235] mb-3">{benefit.title}</h3>
                <p className="text-gray-700">{benefit.description}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            When you join MyStreamLnk+, you leverage a cutting-edge platform to grow your network and income, while we
            handle the complex operational heavy lifting.
          </p>
        </div>
      </div>
    </section>
  )
}
