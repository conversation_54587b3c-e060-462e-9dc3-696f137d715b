import { BarChart, Coins, DollarSign, Factory, LineChart, ShoppingBag, Tag, Truck } from "lucide-react";

export default function StrategicToolSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">Your Strategic Tool for Smart Inventory Liquidation – Powered by E-Stream & AI</h2>
          <p className="text-lg text-gray-700">
            StreamLnk's integrated Auction Engine, accessible through the E-Stream Supplier Portal, provides a powerful and transparent solution for efficiently liquidating inventory:
          </p>
        </div>

        <div className="max-w-5xl mx-auto">
          {/* Targeted Buyer Reach */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <ShoppingBag className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">Targeted Buyer Reach:</h4>
            </div>
            <p className="text-gray-700 pl-16">
              Auctions are promoted to a global network of verified MyStreamLnk buyers and MyStreamLnk+ agent portfolios actively seeking specific material types or opportunistic deals.
            </p>
          </div>

          {/* Flexible Auction Formats */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <Tag className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">Flexible Auction Formats:</h4>
            </div>
            <ul className="list-disc pl-16 space-y-2 text-gray-700">
              <li><span className="font-semibold">Forward Auctions:</span> Suppliers list lots (product, quantity, location, quality specs), and buyers bid upwards.</li>
              <li><span className="font-semibold">Flash Sales & Time-Limited Offers:</span> Create urgency for quick sales.</li>
            </ul>
          </div>

          {/* AI-Powered Scheduling & Pricing Guidance */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <BarChart className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">AI-Powered Scheduling & Pricing Guidance (StreamIndex™):</h4>
            </div>
            <ul className="list-disc pl-16 space-y-2 text-gray-700">
              <li>Our AI analyzes market demand (from StreamResources+) and seasonal trends to suggest optimal timing for your auctions (e.g., pre-tax season, forecasted demand spikes).</li>
              <li>StreamIndex™ can provide guidance on setting reserve prices or starting bids to maximize returns while ensuring a successful sale.</li>
            </ul>
          </div>

          {/* Regional Exclusivity & Sequential Rollout Logic */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <LineChart className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">Regional Exclusivity & Sequential Rollout Logic:</h4>
            </div>
            <p className="text-gray-700 pl-16">
              Our auction engine rules (as detailed in our platform features) prevent price cannibalization by managing how many similar auctions run concurrently in the same region for the same product type.
            </p>
          </div>

          {/* Anonymity Options */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <Factory className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">Anonymity Options (If Desired):</h4>
            </div>
            <p className="text-gray-700 pl-16">
              Option to list certain lots anonymously to protect brand pricing on primary products.
            </p>
          </div>

          {/* Automated Bid Management & Winner Notification */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <Coins className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">Automated Bid Management & Winner Notification:</h4>
            </div>
            <p className="text-gray-700 pl-16">
              The platform handles bid tracking, identifies winning bids, and notifies all parties.
            </p>
          </div>

          {/* Integrated Logistics & Payment */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <Truck className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">Integrated Logistics & Payment:</h4>
            </div>
            <p className="text-gray-700 pl-16">
              Winning bids seamlessly transition into orders with integrated fulfillment (StreamFreight, StreamGlobe+, StreamPak) and secure payment processing (Escrow, BNPL options for buyers can increase bid attractiveness).
            </p>
          </div>

          {/* Full Transparency */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">Full Transparency:</h4>
            </div>
            <p className="text-gray-700 pl-16">
              Suppliers see all bids (if open auction) and final sales prices, with clear visibility on StreamLnk platform fees.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}