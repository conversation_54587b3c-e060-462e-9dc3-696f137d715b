"use client"

import { Building2, X, CheckCircle, AlertCircle, Phone, Mail, ExternalLink } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { serviceTypes, routes } from "@/data/map-data"
import type { Partner, LiveAsset } from "@/types/map-types"

interface PartnerPanelProps {
  selectedPartner: Partner
  filteredAssets: LiveAsset[]
  onClose: () => void
}

export function PartnerPanel({ selectedPartner, filteredAssets, onClose }: PartnerPanelProps) {
  const getTierBadgeColor = (tier: string) => {
    switch (tier) {
      case "premium":
        return "bg-green-100 text-green-800 border-green-300"
      case "standard":
        return "bg-blue-100 text-blue-800 border-blue-300"
      case "basic":
        return "bg-gray-100 text-gray-800 border-gray-300"
      default:
        return "bg-gray-100 text-gray-800 border-gray-300"
    }
  }

  const partnerAssets = filteredAssets.filter((asset) => {
    const route = routes.find((r) => r.id === asset.routeId)
    return route && (route.from === selectedPartner.id || route.to === selectedPartner.id)
  })

  const totalValue = partnerAssets.reduce((sum, asset) => sum + asset.value, 0)

  return (
    <div className="fixed inset-y-0 right-0 w-96 z-30 transform transition-transform duration-300 ease-in-out">
      <Card className="h-full glassmorphism border-l border-slate-200 rounded-none overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center gap-3">
              <Building2 className="w-8 h-8 text-[#52AAA3]" />
              <div>
                <h2 className="text-xl font-bold text-slate-900">{selectedPartner.name}</h2>
                <p className="text-slate-600 text-sm">{selectedPartner.country}</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-slate-600 hover:text-slate-900 hover:bg-slate-100"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Status Badges */}
          <div className="flex gap-2 mb-6">
            <Badge className={getTierBadgeColor(selectedPartner.tier)}>{selectedPartner.tier.toUpperCase()}</Badge>
            <Badge
              className={
                selectedPartner.streamlnkIntegrated
                  ? "bg-green-100 text-green-800 border-green-300"
                  : "bg-orange-100 text-orange-800 border-orange-300"
              }
            >
              {selectedPartner.streamlnkIntegrated ? (
                <>
                  <CheckCircle className="w-3 h-3 mr-1" /> StreamLnk Integrated
                </>
              ) : (
                <>
                  <AlertCircle className="w-3 h-3 mr-1" /> Integration Pending
                </>
              )}
            </Badge>
          </div>

          {/* Real-time Partner Stats */}
          <div className="mb-6">
            <h3 className="text-slate-900 font-semibold mb-3">Live Statistics</h3>
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-slate-50 rounded-lg p-3 border border-slate-200">
                <p className="text-slate-600 text-xs">Active Shipments</p>
                <p className="text-[#52AAA3] text-lg font-bold">{partnerAssets.length}</p>
              </div>
              <div className="bg-slate-50 rounded-lg p-3 border border-slate-200">
                <p className="text-slate-600 text-xs">Total Value</p>
                <p className="text-[#52AAA3] text-lg font-bold">${Math.round(totalValue / 1000000)}M</p>
              </div>
            </div>
          </div>

          {/* Description */}
          <div className="mb-6">
            <h3 className="text-slate-900 font-semibold mb-2">About</h3>
            <p className="text-slate-600 text-sm leading-relaxed">{selectedPartner.description}</p>
          </div>

          <Separator className="bg-slate-200 mb-6" />

          {/* Services */}
          <div className="mb-6">
            <h3 className="text-slate-900 font-semibold mb-3">Services Offered</h3>
            <div className="grid grid-cols-2 gap-2">
              {selectedPartner.services.map((serviceId) => {
                const service = serviceTypes.find((s) => s.id === serviceId)
                if (!service) return null
                const Icon = service.icon
                return (
                  <div
                    key={serviceId}
                    className="flex items-center gap-2 p-2 bg-slate-50 rounded-lg border border-slate-200"
                  >
                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: service.color }} />
                    <Icon className="w-4 h-4 text-slate-600" />
                    <span className="text-slate-900 text-xs">{service.name}</span>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Coverage */}
          <div className="mb-6">
            <h3 className="text-slate-900 font-semibold mb-3">Geographic Coverage</h3>
            <div className="flex flex-wrap gap-2">
              {selectedPartner.coverage.map((area) => (
                <Badge key={area} variant="outline" className="border-[#52AAA3] text-[#52AAA3]">
                  {area}
                </Badge>
              ))}
            </div>
          </div>

          <Separator className="bg-slate-200 mb-6" />

          {/* Contact Information */}
          <div className="mb-6">
            <h3 className="text-slate-900 font-semibold mb-3">Contact Information</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Phone className="w-4 h-4 text-[#52AAA3]" />
                <span className="text-slate-600 text-sm">{selectedPartner.contact.phone}</span>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="w-4 h-4 text-[#52AAA3]" />
                <span className="text-slate-600 text-sm">{selectedPartner.contact.email}</span>
              </div>
              <div className="flex items-center gap-3">
                <ExternalLink className="w-4 h-4 text-[#52AAA3]" />
                <a
                  href={`https://${selectedPartner.contact.website}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-[#52AAA3] text-sm hover:underline"
                >
                  {selectedPartner.contact.website}
                </a>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button className="flex-1 bg-[#52AAA3] hover:bg-[#52AAA3]/90 text-white">Contact Partner</Button>
            <Button variant="outline" className="border-[#52AAA3] text-[#52AAA3] hover:bg-[#52AAA3]/10">
              View Details
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}
