import { Alert<PERSON>rian<PERSON>, Clock, Layers, BarChart3 } from "lucide-react"

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Tired of Customs Delays and Compliance Headaches?
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            Clearing industrial materials through international customs is a notoriously challenging aspect of global trade, often leading to:
          </p>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Challenge 1 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <AlertTriangle className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  Complex and ever-changing country-specific regulations and documentation requirements.
                </p>
              </div>
            </div>

            {/* Challenge 2 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <Clock className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  Time-consuming manual preparation and submission of customs declarations.
                </p>
              </div>
            </div>

            {/* Challenge 3 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <AlertTriangle className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  Risk of errors in HS code classification, valuation, and origin documentation, leading to delays, fines, or seizures.
                </p>
              </div>
            </div>

            {/* Challenge 4 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <Layers className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  Difficulty coordinating with multiple parties (suppliers, freight forwarders, customs brokers).
                </p>
              </div>
            </div>

            {/* Challenge 5 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <BarChart3 className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  Lack of real-time visibility into clearance status and potential issues.
                </p>
              </div>
            </div>

            {/* Challenge 6 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <Clock className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  Unpredictable clearance times impacting supply chain planning.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}