import { CheckCircle } from "lucide-react";

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Navigating the Complex World of Cross-Border Finance?
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            For businesses engaged in global industrial trade, managing payments across different currencies and banking systems can be a significant hurdle, often involving:
          </p>
          
          <div className="space-y-4">
            {[
              "High foreign exchange (FX) conversion fees and unfavorable rates from traditional banks.",
              "Lack of transparency in the FX rates applied to transactions.",
              "Delays in international wire transfers and payment settlements.",
              "Complexities in managing multiple currency bank accounts.",
              "Administrative overhead of reconciling payments in different currencies.",
              "Risk of payment failures or rejections due to international banking protocols."
            ].map((challenge, index) => (
              <div key={index} className="flex items-start">
                <div className="bg-[#028475]/10 p-2 rounded-full mr-4 mt-1">
                  <CheckCircle className="h-5 w-5 text-[#028475]" />
                </div>
                <p className="text-gray-700">{challenge}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}