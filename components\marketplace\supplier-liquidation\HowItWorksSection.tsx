import React from 'react';

export default function HowItWorksSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">A Simple Process to Convert Stock to Cash</h2>

          <div className="space-y-6">
            <div className="flex items-start">
              <div className="bg-[#004235] text-white w-8 h-8 rounded-full flex items-center justify-center font-bold mr-4 flex-shrink-0 mt-1">1</div>
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">Login to E-Stream</h3>
                <p className="text-gray-700">Access your supplier portal.</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-[#004235] text-white w-8 h-8 rounded-full flex items-center justify-center font-bold mr-4 flex-shrink-0 mt-1">2</div>
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">Select Inventory</h3>
                <p className="text-gray-700">Choose products from your existing catalog or add new lots specifically for auction.</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-[#004235] text-white w-8 h-8 rounded-full flex items-center justify-center font-bold mr-4 flex-shrink-0 mt-1">3</div>
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">Configure Auction</h3>
                <p className="text-gray-700">Set auction type, duration, starting bid/reserve price (with AI guidance), minimum bid increments, and regions open for bidding.</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-[#004235] text-white w-8 h-8 rounded-full flex items-center justify-center font-bold mr-4 flex-shrink-0 mt-1">4</div>
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">Launch Auction</h3>
                <p className="text-gray-700">Make your listing live to targeted buyers.</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-[#004235] text-white w-8 h-8 rounded-full flex items-center justify-center font-bold mr-4 flex-shrink-0 mt-1">5</div>
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">Monitor Bids</h3>
                <p className="text-gray-700">Track bidding activity in real-time (if desired).</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-[#004235] text-white w-8 h-8 rounded-full flex items-center justify-center font-bold mr-4 flex-shrink-0 mt-1">6</div>
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">Auction Ends & Winner Confirmed</h3>
                <p className="text-gray-700">System identifies the highest bidder.</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-[#004235] text-white w-8 h-8 rounded-full flex items-center justify-center font-bold mr-4 flex-shrink-0 mt-1">7</div>
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">Order Processed</h3>
                <p className="text-gray-700">The winning bid automatically converts to a sales order, triggering logistics and payment workflows.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}