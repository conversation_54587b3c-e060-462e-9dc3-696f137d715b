import { FileText, CheckCircle2, Repeat, Database } from "lucide-react"

export function PoaManagementSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
              POA Template Automation & Management
            </h2>
            <p className="mx-auto max-w-[700px] text-gray-600 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              StreamLnk simplifies the Power of Attorney process
            </p>
          </div>
        </div>
        <div className="mx-auto max-w-4xl mt-12">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="relative h-[400px] rounded-xl overflow-hidden">
              <img
                src="/images/partner/streamglobe-agent/streamglobe-agent.webp"
                alt="POA document management"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="space-y-6 flex flex-col justify-center">
              {[
                {
                  icon: <FileText className="h-6 w-6 text-[#028475]" />,
                  title: "Pre-Populated POA Options",
                  description: "Utilize templates that can be pre-populated with your branding and details.",
                },
                {
                  icon: <CheckCircle2 className="h-6 w-6 text-[#028475]" />,
                  title: "Advance Buyer Authorization",
                  description: "Buyer POA signatures are often collected during their account setup on StreamLnk.",
                },
                {
                  icon: <Repeat className="h-6 w-6 text-[#028475]" />,
                  title: "Flexible Agreements",
                  description: "POAs can be per shipment or established as long-term standing agreements.",
                },
                {
                  icon: <Database className="h-6 w-6 text-[#028475]" />,
                  title: "Centralized Access",
                  description: "Access stored and active POAs directly from your StreamGlobe portal.",
                },
              ].map((item, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="rounded-full bg-[#F2F2F2] p-2">{item.icon}</div>
                  <div>
                    <h3 className="text-xl font-bold text-[#004235]">{item.title}</h3>
                    <p className="text-gray-600">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
