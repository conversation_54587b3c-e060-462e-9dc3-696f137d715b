import { Briefcase, Search, Building, Users, Truck } from "lucide-react"

export function IdealApplicantsSection() {
  const applicants = [
    {
      icon: <Briefcase className="h-12 w-12 text-white" />,
      title: "Experienced Industrial Sales Agents",
      description:
        "Professionals with an established portfolio of clients in polymers, chemicals, packaging, or related industrial sectors.",
    },
    {
      icon: <Search className="h-12 w-12 text-white" />,
      title: "Procurement Consultants & Supply Chain Experts",
      description: "Individuals who advise businesses on sourcing and supply chain optimization.",
    },
    {
      icon: <Building className="h-12 w-12 text-white" />,
      title: "Country or Regional Distributors",
      description:
        "Established distribution businesses looking to digitize their operations and expand their offerings.",
    },
    {
      icon: <Users className="h-12 w-12 text-white" />,
      title: "Freelance B2B Sales Professionals",
      description: "Enterprising individuals seeking new, flexible opportunities in industrial sales.",
    },
    {
      icon: <Truck className="h-12 w-12 text-white" />,
      title: "Former Employees of Manufacturers or Logistics Providers",
      description: "Individuals with strong client contacts and industry knowledge.",
    },
  ]

  return (
    <section className="py-20 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Ideal Applicants for MyStreamLnk+ Include
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {applicants.map((applicant, index) => (
            <div
              key={index}
              className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300"
            >
              <div className="bg-gradient-to-r from-[#004235] to-[#028475] p-4 flex justify-center">
                {applicant.icon}
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-[#004235] mb-3">{applicant.title}</h3>
                <p className="text-gray-700">{applicant.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
