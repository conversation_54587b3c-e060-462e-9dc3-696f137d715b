"use client"

import Image from "next/image"
import Link from "next/link"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { MainNav } from "@/components/main-nav"
import { BottomFooter } from "@/components/bottom-footer"
import { Shield, ChevronRight } from "lucide-react";
import { FaqSection } from "@/components/faq-section";

export default function CustomerServicePage() {
  const [trackingNumber, setTrackingNumber] = useState("")

  const handleTrackingSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle tracking number submission
    console.log("Tracking number submitted:", trackingNumber)
    // In a real implementation, this would redirect to a tracking results page
  }

  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      {/* Main Content */}
      <main className="flex-grow bg-[#F2F2F2]">
        {/* Two-column layout container */}
        <div className="container mx-auto px-4 py-12">
          <div className="flex flex-col md:flex-row md:gap-8">
            {/* Left Column - 60% */}
            <div className="md:w-[60%]">
              {/* Hero Section */}
              <section className="pb-8">
                <h1 className="text-4xl font-bold text-[#004235] mb-6">Customer Service</h1>
                <p className="text-gray-700">
                  Welcome to the StreamLnk customer support page. This portal serves
                  customers, agents, and suppliers using MyStreamLnk, MyStreamLnk+,
                  and E-Stream. If you are a freight, customs, or packaging partner,
                  please refer to your designated portal for assistance
                </p>
              </section>

              {/* Tracking Section */}
              <section className="py-8 border-t border-gray-200">
                <h2 className="text-2xl font-bold text-[#004235] mb-4">Enter Your Tracking Number(s)</h2>
                <p className="text-gray-700 mb-6">
                  Use your shipment tracking number to automatically route your support
                  request to the correct StreamLnk division (customer service, supplier
                  support, or logistics).
                </p>
                <form onSubmit={handleTrackingSubmit}>
                  <div className="flex">
                    <Input
                      type="text"
                      placeholder="Enter your tracking number"
                      className="flex-grow rounded-r-none border-gray-300 focus:ring-0 focus:ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0"
                      value={trackingNumber}
                      onChange={(e) => setTrackingNumber(e.target.value)}
                    />
                    <Button 
                      type="submit" 
                      className="bg-[#028475] hover:bg-[#006a5f] rounded-l-none px-4"
                    >
                      <ChevronRight className="h-5 w-5 text-white" />
                    </Button>
                  </div>
                </form>
              </section>
            </div>

            {/* Right Column - 30% */}
            <div className="md:w-[30%] mt-8 md:mt-0"> {/* Adjust top padding to align with image */} 
              {/* Support Cards Section */}
              <div className="space-y-6">
              {/* Business Account Card */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 relative overflow-hidden">
                <div className="absolute top-0 right-0 w-10 h-10 bg-[#028475] transform rotate-45 translate-x-5 -translate-y-5"></div>
                <h3 className="text-lg font-semibold text-[#004235] mb-3">
                  <Link href="/business-account" className="text-[#028475] hover:underline flex items-center">
                    Request a Business Account
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Link>
                </h3>
                <p className="text-sm text-gray-600 mb-3">
                  Shipping or supplying regularly through StreamLnk? Request a business account and
                  unlock exclusive benefits:
                </p>
                <ul className="space-y-1 text-sm">
                  <li className="flex items-start">
                    <div className="text-[#028475] mr-2 mt-1">
                      <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-gray-600">Personalized dashboard and support</span>
                  </li>
                  <li className="flex items-start">
                    <div className="text-[#028475] mr-2 mt-1">
                      <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-gray-600">Access to auctions, BNPL, and pricing insights</span>
                  </li>
                  <li className="flex items-start">
                    <div className="text-[#028475] mr-2 mt-1">
                      <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-gray-600">Participation in tiered rewards and partner recognition programs</span>
                  </li>
                </ul>
              </div>

              {/* Suspicious Email Card */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 relative overflow-hidden">
                <div className="absolute top-0 right-0 w-10 h-10 bg-[#028475] transform rotate-45 translate-x-5 -translate-y-5"></div>
                <h3 className="text-lg font-semibold text-[#004235] mb-3">
                  <Link href="/report-phishing" className="text-[#028475] hover:underline flex items-center">
                    Report a Suspicious Email
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Link>
                </h3>
                <p className="text-sm text-gray-600 mb-3">
                  Think you received a phishing or fraudulent message claiming to be from StreamLnk?
                </p>
                <ul className="space-y-1 text-sm">
                  <li className="text-gray-600">
                    • Please forward <NAME_EMAIL>
                  </li>
                  <li className="text-gray-600">
                    • Our cybersecurity team will investigate immediately.
                  </li>
                </ul>
                <div className="mt-3 flex items-center text-[#028475] text-sm">
                  <Shield className="h-4 w-4 mr-2" />
                  <span className="font-medium">Your data security is our priority</span>
                </div>
              </div>
              </div>
            </div>
          </div>
        </div>

        <FaqSection />

        {/* Contact Info */}
        <section className="py-12 bg-white border-t border-gray-200">
          <div className="container mx-auto px-4 text-center">
            <p className="text-gray-700 mb-2">
              Need help with something not listed above? Contact <a href="mailto:<EMAIL>" className="text-[#028475] hover:underline"><EMAIL></a> or use your portal's live
              chat feature for immediate assistance.
            </p>
            <p className="text-gray-700">
              Thank you for being a part of the StreamLnk network – your gateway to smarter global trade.
            </p>
          </div>
        </section>
      </main>

      <BottomFooter />
    </div>
  )
}