import type { Metadata } from "next";
import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";
import HeroSection from "@/components/why-us/global-network/hero-section";
import LimitationsSection from "@/components/why-us/global-network/limitations-section";
import SolutionSection from "@/components/why-us/global-network/solution-section";
import BenefitsSection from "@/components/why-us/global-network/benefits-section";
import CtaSection from "@/components/why-us/global-network/cta-section";

export const metadata: Metadata = {
  title: "Why StreamLnk? Access a Truly Global Network for Industrial Trade | StreamLnk",
  description:
    "Connect with StreamLnk's vetted global network of suppliers, buyers, logistics providers, and industry experts. Expand your reach and optimize your industrial trade operations.",
};

export default function GlobalNetworkPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <MainNav />
      <main>
        <HeroSection />
        <LimitationsSection />
        <SolutionSection />
        <BenefitsSection />
        <CtaSection />
      </main>
      <MainFooter />
    </div>
  );
}