import SecurityFeature from "@/components/finance-payments/upload-proof-payment/security-feature"
import { Lock, Shield, FileCheck } from "lucide-react"

export default function SecurityComplianceSection() {
  return (
    <section className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Security & Compliance</h2>
          <p className="text-gray-600 max-w-3xl">We take the security of your financial documents seriously.</p>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <SecurityFeature
            icon={<Lock className="h-8 w-8 text-[#028475]" />}
            title="Encrypted Storage"
            description="All uploads are encrypted and stored securely"
          />
          <SecurityFeature
            icon={<Shield className="h-8 w-8 text-[#028475]" />}
            title="Restricted Access"
            description="Only verified users and assigned parties can access uploaded POPs"
          />
          <SecurityFeature
            icon={<FileCheck className="h-8 w-8 text-[#028475]" />}
            title="Audit Trail"
            description="Audit trail is automatically logged for financial compliance"
          />
        </div>
      </div>
    </section>
  )
}