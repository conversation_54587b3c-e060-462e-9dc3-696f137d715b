"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, DownloadCloud, ExternalLink, LogIn } from "lucide-react";
import Link from "next/link";

export default function HowToLeverageSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]" id="hub"> {/* Added id for internal linking */}
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            How to Leverage StreamIndex™ for Your Business
          </h2>
          <p className="text-xl text-gray-700 mb-10 text-center">
            Accessing and Using the StreamIndex™ Tool
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {/* For StreamResources+ Subscribers */}
            <div className="bg-white p-8 rounded-lg shadow-md border border-gray-200">
              <h3 className="text-2xl font-semibold text-[#004235] mb-4">For StreamResources+ Subscribers</h3>
              <ul className="space-y-3 text-gray-700 mb-6">
                <li className="flex items-start">
                  <LogIn className="text-[#028475] h-5 w-5 mr-3 mt-1 flex-shrink-0" />
                  <span>Click the "Login to StreamResources+" button (typically in the header or a dedicated access point).</span>
                </li>
                <li className="flex items-start">
                  <LogIn className="text-[#028475] h-5 w-5 mr-3 mt-1 flex-shrink-0" />
                  <span>Enter your secure credentials.</span>
                </li>
                <li className="flex items-start">
                  <LogIn className="text-[#028475] h-5 w-5 mr-3 mt-1 flex-shrink-0" />
                  <span>Navigate to the "StreamIndex™ Dashboards" section within StreamResources+.</span>
                </li>
                <li className="flex items-start">
                  <LogIn className="text-[#028475] h-5 w-5 mr-3 mt-1 flex-shrink-0" />
                  <span>Utilize the filters, interactive charts, and data export features to gather the intelligence you need.</span>
                </li>
                <li className="flex items-start">
                  <LogIn className="text-[#028475] h-5 w-5 mr-3 mt-1 flex-shrink-0" />
                  <span>Set up custom alerts for specific index movements.</span>
                </li>
              </ul>
              <Button
                variant="link"
                className="text-[#028475] hover:text-[#004235] px-0"
                asChild
              >
                <Link href="/path-to-login-guide.pdf" target="_blank"> {/* Replace with actual link */}
                  StreamResources+ Login Guide (PDF)
                  <DownloadCloud className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* For Non-Subscribers / Interested Parties */}
            <div className="bg-white p-8 rounded-lg shadow-md border border-gray-200">
              <h3 className="text-2xl font-semibold text-[#004235] mb-4">For Non-Subscribers / Interested Parties</h3>
              <p className="text-gray-700 mb-6">
                StreamIndex™ is a premium tool. To gain full access, you need a subscription to an eligible StreamResources+ tier.
                We invite you to:
              </p>
              <div className="space-y-4">
                <Button
                  className="w-full bg-[#004235] hover:bg-[#028475] text-white"
                  size="lg"
                  asChild
                >
                  <Link href="/request-demo?tool=streamindex&source=how-to-leverage">
                    REQUEST PERSONALIZED DEMO
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button
                  variant="link"
                  className="text-[#028475] hover:text-[#004235] px-0 block text-left w-full justify-start"
                  asChild
                >
                  <Link href="/solutions/streamresources#subscription-plans"> {/* Assuming this link structure */}
                    COMPARE STREAMRESOURCES+ PLANS
                    <ExternalLink className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button
                  variant="link"
                  className="text-[#028475] hover:text-[#004235] px-0 block text-left w-full justify-start"
                  asChild
                >
                  <Link href="/tools/streamindex/methodology"> {/* Assuming this link will be created */}
                    LEARN ABOUT METHODOLOGY
                    <ExternalLink className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}