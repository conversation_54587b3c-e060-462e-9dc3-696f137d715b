import { Users, TrendingUp, DollarSign, Zap, CheckSquare, Award } from "lucide-react";

const benefits = [
  {
    icon: <Users className="h-10 w-10 text-[#004235] mb-4" />,
    title: "Access a Growing B2B User Base",
    description: "Offer your solutions to thousands of active industrial buyers, suppliers, and service providers on the StreamLnk platform."
  },
  {
    icon: <TrendingUp className="h-10 w-10 text-[#004235] mb-4" />,
    title: "Enhance Your Product Value",
    description: "Provide your existing customers with seamless integration to a leading global trade ecosystem."
  },
  {
    icon: <DollarSign className="h-10 w-10 text-[#004235] mb-4" />,
    title: "Create New Revenue Opportunities",
    description: "Through referral programs, co-marketing, or by offering premium integrated features."
  },
  {
    icon: <Zap className="h-10 w-10 text-[#004235] mb-4" />,
    title: "Stay at the Forefront of Industrial Digitization",
    description: "Align your technology with an innovative, AI-powered platform."
  },
  {
    icon: <CheckSquare className="h-10 w-10 text-[#004235] mb-4" />,
    title: "Standardized & Supported APIs",
    description: "Work with our well-documented APIs and dedicated developer support team."
  },
  {
    icon: <Award className="h-10 w-10 text-[#004235] mb-4" />,
    title: "Co-Marketing & Visibility",
    description: "Potential for featured partner status and joint marketing initiatives."
  }
];

export function WhyPartnerSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="w-10 h-1 bg-[#028475]"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            Why Partner with StreamLnk's Technology Ecosystem?
          </h2>
          <p className="text-xl text-[#028475]">Benefits of Integrating with StreamLnk</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <div key={index} className="flex flex-col items-center text-center p-6 border-l-4 border-[#028475] shadow-md hover:shadow-lg transition-shadow bg-white rounded-r-lg">
              {benefit.icon}
              <h3 className="text-xl font-bold text-[#004235] mb-2">{benefit.title}</h3>
              <p className="text-gray-600 text-sm">{benefit.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}