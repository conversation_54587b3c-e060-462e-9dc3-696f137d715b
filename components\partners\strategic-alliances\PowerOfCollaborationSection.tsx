"use client";

import { Zap, TrendingUp, Users, Shield<PERSON>heck, Award, Lightbulb } from 'lucide-react'; // Choose icons that best fit

const allianceBenefits = [
  {
    icon: <TrendingUp className="h-8 w-8 text-[#028475] mb-3" />,
    title: "Accelerated Market Penetration",
    description: "Combine StreamLnk's digital ecosystem with your established market presence, customer base, or specialized expertise to rapidly scale new solutions."
  },
  {
    icon: <Lightbulb className="h-8 w-8 text-[#028475] mb-3" />,
    title: "Co-Innovation & Product Development",
    description: "Jointly develop and launch innovative products, services, or data solutions tailored to specific industry needs or new verticals."
  },
  {
    icon: <Users className="h-8 w-8 text-[#028475] mb-3" />,
    title: "Access to a Unique B2B Ecosystem",
    description: "Leverage StreamLnk's network of verified suppliers, buyers, and logistics providers for new business opportunities."
  },
  {
    icon: <Award className="h-8 w-8 text-[#028475] mb-3" />,
    title: "Enhanced Brand Leadership",
    description: "Position your organization at the forefront of supply chain digitization and industrial trade innovation through a high-profile partnership."
  },
  {
    icon: <ShieldCheck className="h-8 w-8 text-[#028475] mb-3" />,
    title: "Shared Risk, Shared Reward",
    description: "Collaborate on ambitious projects with a shared investment in resources and a mutual stake in success."
  },
  {
    icon: <Zap className="h-8 w-8 text-[#028475] mb-3" />,
    title: "Influence on Industry Standards",
    description: "Work together to shape the future standards for digital trade, compliance, and data exchange in industrial sectors."
  }
];

export default function PowerOfCollaborationSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            The Power of Strategic Collaboration in a Transforming Industry
          </h2>
          <p className="text-xl text-gray-700 leading-relaxed">
            The scale and complexity of transforming global industrial supply chains demand more than individual effort; they require deep, strategic collaboration.
          </p>
        </div>

        <div className="max-w-5xl mx-auto">
          <h3 className="text-2xl md:text-3xl font-semibold text-[#004235] mb-10 text-center">
            Why Forge a Strategic Alliance with StreamLnk?
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {allianceBenefits.map((benefit) => (
              <div key={benefit.title} className="bg-[#f3f4f6] p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center text-center">
                {benefit.icon}
                <h4 className="text-xl font-semibold text-[#004235] mb-2">{benefit.title}</h4>
                <p className="text-gray-600 text-sm leading-relaxed">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}