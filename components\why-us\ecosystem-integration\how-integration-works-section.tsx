import { Zap, Database, Users, FileText, ShieldCheck, Share2, Workflow } from "lucide-react";

interface IntegrationFeatureProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const IntegrationFeatureCard: React.FC<IntegrationFeatureProps> = ({ icon, title, description }) => (
  <div className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
    <div className="flex items-start mb-4">
      <div className="rounded-full bg-[#028475]/10 w-12 h-12 flex items-center justify-center mr-4 flex-shrink-0">
        {icon}
      </div>
      <div>
        <h3 className="text-xl font-semibold text-[#004235]">{title}</h3>
      </div>
    </div>
    <p className="text-sm text-gray-600 ml-16">{description}</p>
  </div>
);

export default function HowIntegrationWorksSection() {
  const features = [
    {
      icon: <Users className="h-6 w-6 text-[#028475]" />,
      title: "Interconnected Role-Specific Portals",
      description: "Data flows automatically between E-Stream (Suppliers), MyStreamLnk (Customers), MyStreamLnk+ (Agents), StreamFreight (Land Freight), StreamGlobe & StreamGlobe+ (Sea Freight & Customs), and StreamPak (Packaging/Warehousing). An order placed in MyStreamLnk instantly triggers processes across relevant logistics and supplier portals."
    },
    {
      icon: <Database className="h-6 w-6 text-[#028475]" />,
      title: "Centralized Data Hub (StreamResources+)",
      description: "All transactional, operational, compliance, and behavioral data from across the ecosystem is aggregated, anonymized where necessary, and analyzed by StreamResources+. This powers our AI, StreamIndex™, and iScore™."
    },
    {
      icon: <Workflow className="h-6 w-6 text-[#028475]" />,
      title: "Automated Workflow Orchestration",
      description: "Our AI engine acts as the conductor, automating handoffs, triggering notifications, and ensuring processes flow smoothly. For example, customs clearance (StreamGlobe+) is automatically initiated when an international shipment is confirmed, and packaging jobs (StreamPak) are assigned if a product is flagged as \"unfinished.\""
    },
    {
      icon: <FileText className="h-6 w-6 text-[#028475]" />,
      title: "Unified Communication & Documentation",
      description: "All critical documents are stored centrally and accessible to relevant parties. Communication (though often routed via StreamLnk Ops for neutrality) is logged and linked to specific transactions."
    },
    {
      icon: <ShieldCheck className="h-6 w-6 text-[#028475]" />,
      title: "Embedded Fintech & Compliance",
      description: "Payment solutions (BNPL, Escrow), KYC/AML verification, and compliance checks are not add-ons but integral parts of the transaction flow."
    },
    {
      icon: <Share2 className="h-6 w-6 text-[#028475]" />,
      title: "API-Driven Connectivity",
      description: "Robust APIs allow for integration with external systems (ERP, SCM software, carrier systems), further extending the reach and utility of the StreamLnk ecosystem."
    }
  ];

  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            One Ecosystem, Infinite Possibilities: The Power of StreamLnk Integration
          </h2>
          <p className="text-lg text-gray-700 mb-2">
            StreamLnk's Vision: A Unified Digital Infrastructure
          </p>
          <p className="text-gray-600">
            StreamLnk was built from the ground up with total ecosystem integration as a core design principle. We believe that true efficiency and value in global trade can only be achieved when all participants and processes are seamlessly connected on a single, intelligent platform.
          </p>
        </div>
        <div className="max-w-4xl mx-auto">
          <h3 className="text-2xl font-semibold text-[#004235] mb-8 text-center md:text-left">How Our Ecosystem Integration Works:</h3>
          <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
            {features.map((feature, index) => (
              <IntegrationFeatureCard 
                key={index} 
                icon={feature.icon} 
                title={feature.title} 
                description={feature.description} 
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}