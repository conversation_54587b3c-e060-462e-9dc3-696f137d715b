import { Accordion } from "@/components/ui/accordion"
import PortalItem from "@/components/portals/portal-item"
import { Zap, FileCheck, Shield, FileText, Truck, Smartphone, Laptop, Server } from "lucide-react"
import { FeatureCard, PlatformBadge, BenefitItem } from "@/components/portals/portal-components"
import Link from "next/link"

export default function SupplyPortals() {
  return (
    <Accordion type="single" collapsible className="w-full space-y-6">
      <PortalItem
        id="estream"
        title="E-STREAM"
        subtitle="For Energy & Industrial Material Suppliers"
        icon={<Zap className="h-8 w-8" />}
        color="#004235"
        imageSrc="/images/portals/e-stream.webp"
        imageAlt="Energy Supply Portal"
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Portal Overview</h4>
            <p className="text-[#404040] mb-6">
              E-STREAM is designed for energy and industrial material suppliers to streamline their operations and
              connect with global customers through the StreamLnk network. Our platform helps you register, list
              products with AI-driven readiness checks, manage compliance documentation, respond to quotes, and
              coordinate logistics for global fulfillment.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Key Features</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <FeatureCard icon={<FileCheck />} title="Product Registration">
                Register and list products with AI-driven readiness checks for optimal market positioning
              </FeatureCard>
              <FeatureCard icon={<Shield />} title="Compliance Management">
                Efficiently manage compliance documentation for all your products and services
              </FeatureCard>
              <FeatureCard icon={<FileText />} title="Quote Response">
                Respond to quotes from potential customers quickly and accurately
              </FeatureCard>
              <FeatureCard icon={<Truck />} title="Logistics Coordination">
                Coordinate logistics for global fulfillment through the StreamLnk network
              </FeatureCard>
            </div>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Available Platforms</h4>
            <div className="flex flex-wrap gap-4 mb-6">
              <PlatformBadge icon={<Laptop />} platform="Web Portal" />
              <PlatformBadge icon={<Server />} platform="API Access" />
            </div>
          </div>

          <div className="bg-gray-50 p-6 rounded-lg">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Who is this for?</h4>
            <p className="text-[#404040] mb-6">
              E-STREAM is specifically designed for energy and industrial material suppliers who want to expand their
              market reach and streamline their operations. Whether you're a small supplier or a large industrial
              manufacturer, our platform provides the tools you need.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Benefits</h4>
            <ul className="space-y-3 mb-6">
              <BenefitItem>Expanded market reach through our global network</BenefitItem>
              <BenefitItem>Streamlined compliance management</BenefitItem>
              <BenefitItem>Efficient quote response process</BenefitItem>
              <BenefitItem>Integrated logistics coordination</BenefitItem>
              <BenefitItem>AI-driven product optimization</BenefitItem>
            </ul>

            <Link href="/signup?portal=streampak" className="block w-full">
              <button className="w-full  bg-[#004235] hover:bg-[#004235]/90 text-white py-2 px-4 rounded-md font-medium mt-4">
                Request Access
              </button>
            </Link>
          </div>
        </div>
      </PortalItem> 
    </Accordion>
  )
}
