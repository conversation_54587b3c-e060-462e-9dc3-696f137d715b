"use client";

import { CheckCircle, LucideIcon } from "lucide-react";

interface MetricItem {
  label: string;
  value: string;
  icon: LucideIcon;
  color?: string;
}

interface ResultsBenefitsSectionProps {
  metrics: MetricItem[];
  qualitativeBenefits: string[];
}

export default function ResultsBenefitsSection({
  metrics,
  qualitativeBenefits,
}: ResultsBenefitsSectionProps) {
  return (
    <section className="mb-12 p-8 bg-[#f3f4f6] rounded-lg">
      <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
        Results & Benefits
      </h2>
      {metrics.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6 mb-10">
          {metrics.map((metric, index) => {
            const IconComponent = metric.icon;
            return (
              <div
                key={index}
                className="bg-white p-6 rounded-lg shadow-lg flex items-center space-x-4 transform hover:scale-105 transition-transform duration-300 ease-in-out"
              >
                <IconComponent
                  className={`h-10 w-10 ${metric.color || 'text-[#028475]'} flex-shrink-0`}
                />
                <div>
                  <div className="text-2xl font-bold text-[#004235]">
                    {metric.value}
                  </div>
                  <p className="text-sm text-gray-600">{metric.label}</p>
                </div>
              </div>
            );
          })}
        </div>
      )}
      {qualitativeBenefits.length > 0 && (
        <>
          <h3 className="text-xl font-semibold text-[#004235] mb-4">
            Qualitative Benefits:
          </h3>
          <ul className="space-y-3 mb-6">
            {qualitativeBenefits.map((benefit, index) => (
              <li
                key={index}
                className="flex items-start text-gray-700 text-lg"
              >
                <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                {benefit}
              </li>
            ))}
          </ul>
        </>
      )}
    </section>
  );
}