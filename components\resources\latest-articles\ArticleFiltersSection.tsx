"use client";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search } from "lucide-react";

const categories = [
  "All Categories",
  "Supply Chain Management",
  "Logistics & Freight",
  "Technology & AI",
  "Market Analysis & Trends",
  "Sustainability & ESG",
  "Compliance & Risk",
  "StreamLnk Platform Updates",
  "Case Studies",
  "Expert Interviews",
];

const industries = [
  "All Industries", // Optional
  "Polymers & Plastics",
  "Industrial Chemicals",
  "Energy",
  // Add more industries as needed
];

const sortOptions = [
  { value: "recent", label: "Most Recent" },
  { value: "popular", label: "Most Popular" },
  { value: "relevance", label: "Relevance" },
];

export default function ArticleFiltersSection() {
  return (
    <section className="py-10 md:py-12 bg-white border-b border-gray-200">
      <div className="container mx-auto px-4">
        <h2 className="text-2xl md:text-3xl font-bold text-[#004235] mb-6 text-center md:text-left">
          Explore Our Insights
        </h2>
        <div className="bg-[#f3f4f6] p-6 rounded-lg shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end mb-6">
            <div className="col-span-1 md:col-span-2 lg:col-span-2">
              <label htmlFor="search-articles" className="block text-sm font-medium text-gray-700 mb-1">
                Search Articles
              </label>
              <div className="flex">
                <Input
                  type="text"
                  id="search-articles"
                  placeholder="Search by keyword, title, author..."
                  className="flex-grow rounded-r-none border-gray-300 focus:border-[#004235] focus:ring-[#004235]"
                />
                <Button className="bg-[#004235] hover:bg-[#028475] text-white rounded-l-none px-4">
                  <Search className="h-5 w-5" />
                  <span className="sr-only">Search</span>
                </Button>
              </div>
            </div>
            <div>
              <label htmlFor="filter-category" className="block text-sm font-medium text-gray-700 mb-1">
                Filter by Category
              </label>
              <Select defaultValue="All Categories">
                <SelectTrigger id="filter-category" className="w-full border-gray-300 focus:border-[#004235] focus:ring-[#004235]">
                  <SelectValue placeholder="Select Category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category.toLowerCase().replace(/\s+/g, '-')}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label htmlFor="sort-by" className="block text-sm font-medium text-gray-700 mb-1">
                Sort By
              </label>
              <Select defaultValue="recent">
                <SelectTrigger id="sort-by" className="w-full border-gray-300 focus:border-[#004235] focus:ring-[#004235]">
                  <SelectValue placeholder="Sort articles" />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          {/* Optional Industry Filter */}
          {/* <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="filter-industry" className="block text-sm font-medium text-gray-700 mb-1">
                Filter by Industry (Optional)
              </label>
              <Select defaultValue="All Industries">
                <SelectTrigger id="filter-industry" className="w-full border-gray-300 focus:border-[#004235] focus:ring-[#004235]">
                  <SelectValue placeholder="Select Industry" />
                </SelectTrigger>
                <SelectContent>
                  {industries.map((industry) => (
                    <SelectItem key={industry} value={industry.toLowerCase().replace(/\s+/g, '-')}>
                      {industry}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div> */}
        </div>
      </div>
    </section>
  );
}