import Link from "next/link"
import { BottomFooter } from "@/components/bottom-footer"

export function MainFooter() {
  const footerData = {
    "columns": [
      {
        "title": "Get to Know Us",
        "items": [
          {"label": "About StreamLnk", "targetDescription": "/about-us"},
          {"label": "Careers at StreamLnk", "targetDescription": "/careers"},
          {"label": "Press Center & Media", "targetDescription": "/press-center"},
          {"label": "Investor Relations", "targetDescription": "/news"},
          {"label": "StreamLnk Blog / Delivered Magazine", "targetDescription": "/news/latest-announcement"},
          {"label": "ESG & Sustainability", "targetDescription": "/resources/esg-reporting"},
          {"label": "Supplier Diversity Commitment", "targetDescription": "/partners/opportunities"},
          {"label": "Innovation at StreamLnk", "targetDescription": "/resources/tech-trends"}
        ]
      },
      {
        "title": "Partner With StreamLnk",
        "items": [
          {"label": "Sell Industrial Products on E-Stream", "targetDescription": "/partners/e-stream-supplier"},
          {"label": "Offer Freight Services", "targetDescription": "/partners/stream-freight"},
          {"label": "Provide Sea Freight Services", "targetDescription": "/partners/streamglobe-carrier"},
          {"label": "Become a Customs Clearance Agent", "targetDescription": "/partners/streamglobe-agent"},
          {"label": "List Packaging & Warehousing Services", "targetDescription": "/partners/streampak-partner"},
          {"label": "Join as an Agent/Distributor", "targetDescription": "/partners/partner-with-us"},
          {"label": "Become a Data or API Partner", "targetDescription": "/partners/api-integration"},
          {"label": "Participate in StreamLnk Auctions", "targetDescription": "/partners/auctions"},
          {"label": "View All Partnership Opportunities", "targetDescription": "/partners"}
        ]
      },
      {
        "title": "StreamLnk for Your Business",
        "items": [
          {"label": "Source Industrial Materials", "targetDescription": "/login"},
          {"label": "Request a Business Account", "targetDescription": "/signup"},
          {"label": "Global Logistics Solutions", "targetDescription": "/solutions/global-logistics"},
          {"label": "Trade Finance & Payments", "targetDescription": "/solutions/trade-finance"},
          {"label": "Data Insights & Market Intelligence", "targetDescription": "/solutions/data-analytics"},
          {"label": "Compliance & Risk Management Tools", "targetDescription": "/solutions/risk-compliance"},
          {"label": "Auction Marketplace for Buyers", "targetDescription": "/marketplace/buyer-sourcing"},
          {"label": "Solutions by Industry", "targetDescription": "/industries"}
        ]
      },
      {
        "title": "Customer & Partner Support",
        "items": [
          {"label": "Your StreamLnk Account & Portals", "targetDescription": "/login"},
          {"label": "Track Your Shipment", "targetDescription": "/track"},
          {"label": "Manage Your Orders & Quotes", "targetDescription": "/user-portal/my-orders"},
          {"label": "Billing & Payment Support", "targetDescription": "/support/billing"},
          {"label": "Document Management Help", "targetDescription": "/support/documents"},
          {"label": "Compliance & Onboarding Assistance", "targetDescription": "/support/onboarding-guides"},
          {"label": "API & Developer Support", "targetDescription": "/developer-portal"},
          {"label": "Report an Issue / Contact Support", "targetDescription": "/contact-us"},
          {"label": "Frequently Asked Questions", "targetDescription": "/support/faqs"}
        ]
      },
      {
        "title": "StreamLnk Tools & Resources",
        "metadata": {
          "isOptional": true,
          "focus": "Resources/Tools"
        },
        "items": [
          {"label": "StreamIndex™ Market Intelligence", "targetDescription": "/resources/streamindex"},
          {"label": "iScore™ Partner Ratings", "targetDescription": "/resources/iscore"},
          {"label": "ESG Reporting Tools for Business", "targetDescription": "/resources/esg-reporting"},
          {"label": "Mobile App Access", "targetDescription": "/mobile-app"},
          {"label": "StreamLnk Tier & Rewards Program", "targetDescription": "/rewards-program"},
          {"label": "Glossary of Trade Terms", "targetDescription": "/resources/trade-glossary"},
          {"label": "Whitepapers & Case Studies", "targetDescription": "/resources/whitepapers"}
        ]
      }
    ]
  }

  return (
    <footer className="bg-[#172d2d] text-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8">
          {footerData.columns.map((column, colIndex) => (
            <div key={colIndex}>
              <h3 className="text-lg font-semibold mb-6">{column.title}</h3>
              <ul className="space-y-4">
                {column.items.map((item, itemIndex) => (
                  <li key={itemIndex}>
                    <Link href={item.targetDescription || "#"} className="text-gray-400 hover:text-white transition-colors">
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-[#004c45]">
        <BottomFooter />
      </div>
    </footer>
  )
}