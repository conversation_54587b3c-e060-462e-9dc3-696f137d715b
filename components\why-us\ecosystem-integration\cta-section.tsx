import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowRight } from "lucide-react";

export default function CtaSection() {
  return (
    <section className="py-16 bg-white"> {/* Changed background, standardized padding */}
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            The Whole is Greater
          </h2>
          <p className="text-xl text-gray-700 mb-8"> {/* Matched reference P style */}
            StreamLnk is a connected solution, not just tools. We've built the entire puzzle for a superior experience and unmatched efficiencies that siloed solutions cannot offer.
          </p>
          {/* Merged and shortened paragraph content */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg"
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto" // Matched reference primary button
              asChild
            >
              <Link href="/request-demo"> 
                EXPERIENCE INTEGRATION
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button 
              variant="outline" 
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/solutions">
                EXPLORE SOLUTIONS
              </Link>
            </Button>
          </div>
          <div className="mt-6">
            <Button 
              variant="link" 
              className="text-[#028475] hover:text-[#004235]"
              asChild
            >
              <Link href="/contact-us">
                TALK TO EXPERT
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}