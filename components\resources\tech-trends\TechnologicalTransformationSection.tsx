"use client";

import { CheckCircle } from 'lucide-react';

export default function TechnologicalTransformationSection() {
  const drivers = [
    "The need for greater operational efficiency and cost reduction.",
    "Demands for enhanced supply chain visibility and resilience post-pandemic.",
    "The imperative for data-driven decision-making in volatile markets.",
    "The rise of sustainability and the need for tools to track ESG metrics.",
    "The ongoing convergence of physical operations with digital intelligence.",
  ];

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            The Technological Transformation of Industry
          </h2>
          <p className="text-xl text-gray-700">
            Why Embracing Tech Innovation is No Longer Optional for Industrial Businesses
          </p>
        </div>
        <div className="max-w-4xl mx-auto">
          <p className="text-lg text-gray-600 mb-8">
            The industrial sector is undergoing a profound technological shift. Businesses that fail to adapt risk being left behind. Key drivers for tech adoption include:
          </p>
          <ul className="space-y-4">
            {drivers.map((driver, index) => (
              <li key={index} className="flex items-start">
                <CheckCircle className="h-6 w-6 text-[#028475] mr-3 flex-shrink-0 mt-1" />
                <span className="text-gray-700 text-lg">{driver}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  );
}