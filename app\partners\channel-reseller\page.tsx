import type { Metada<PERSON> } from "next";
import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";

import HeroSection from "@/components/channel-partner/hero-section";
import WhyPartnerSection from "@/components/channel-partner/why-partner-section";
import PartnerProgramsSection from "@/components/channel-partner/partner-programs-section";
import WhatWeProvideSection from "@/components/channel-partner/what-we-provide-section";
import IdealCandidatesSection from "@/components/channel-partner/ideal-candidates-section";
import CtaSection from "@/components/channel-partner/cta-section";

export const metadata: Metadata = {
  title: "StreamLnk Channel Partner Program | Expand Your Business",
  description:
    "Join the StreamLnk Channel Partner Program as an Independent Agent or Official Regional Distributor. Leverage your network to bring our B2B ecosystem to your market and build recurring revenue.",
};

export default function ChannelResellerPage() {
  return (
    <div className="flex flex-col min-h-screen bg-white">
      <MainNav />
      <main>
        <HeroSection />
        <WhyPartnerSection />
        <PartnerProgramsSection />
        <WhatWeProvideSection />
        <IdealCandidatesSection />
        <CtaSection />
      </main>
      <MainFooter />
    </div>
  );
}