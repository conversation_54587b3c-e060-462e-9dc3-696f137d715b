// Placeholder for fetching solution-specific content based on slug
// In a real app, this data would come from a CMS or database
const getSolutionContent = async (slug: string) => {
  // Simulate fetching data
  await new Promise(resolve => setTimeout(resolve, 100)); 
  // Example content structure - adapt as needed
  if (slug === "sample-case-study-1") {
    return {
      title: "The StreamLnk Transformation",
      paragraphs: [
        "To address Acme Corp's multifaceted challenges, StreamLnk implemented its comprehensive Buyer Portal and Logistics Management module. The Buyer Portal provided a centralized platform for procurement, order tracking, and supplier communication, replacing fragmented email chains and spreadsheets. Key features like real-time shipment visibility and automated alerts were pivotal in giving Acme Corp unprecedented control over their inbound and outbound logistics.",
        "The StreamLnk solution was tailored to integrate seamlessly with Acme Corp's existing ERP system, ensuring data consistency and eliminating manual data entry errors. Our platform's robust analytics and reporting tools empowered Acme to identify bottlenecks, optimize routes, and make data-driven decisions. The implementation of digital document management within the portal significantly streamlined compliance processes, reducing paperwork and improving audit readiness.",
        "The onboarding process was managed collaboratively, with dedicated StreamLnk support ensuring a smooth transition for Acme's team and their key suppliers. Training sessions were customized to different user roles, fostering quick adoption and maximizing the platform's benefits from day one. Regular check-ins and performance reviews helped fine-tune the solution to Acme's evolving needs."
      ],
      portalsUsed: ["Buyer Portal", "Logistics Management Module"],
      keyFeatures: ["Real-time Shipment Visibility", "Automated Alerts", "ERP Integration", "Digital Document Management", "Advanced Analytics & Reporting"]
    };
  }
  // Fallback or default content
  return {
    title: "The StreamLnk Solution",
    paragraphs: [
      "This section details which StreamLnk portal(s) and key features were implemented or used by the client. It should clearly explain how the platform specifically addressed the challenges outlined previously.",
      "Describe the process of onboarding and adoption. Was it a phased rollout? What kind of support and training was provided? Highlighting a smooth and effective implementation process adds credibility.",
      "Focus on the 'how' – how did StreamLnk's technology and services make a tangible difference for this client? Be specific about features and their direct impact on the client's problems."
    ],
    portalsUsed: ["Relevant Portal A", "Relevant Portal B"],
    keyFeatures: ["Key Feature 1", "Key Feature 2", "Key Feature 3"]
  };
};

interface SolutionSectionProps {
  caseStudySlug: string;
}

const SolutionSection: React.FC<SolutionSectionProps> = async ({ caseStudySlug }) => {
  const content = await getSolutionContent(caseStudySlug);

  return (
    <section className="py-12 md:py-16 lg:py-20 bg-gray-50">
      <div className="container mx-auto px-4 md:px-6">
        <h2 className="text-3xl md:text-4xl font-semibold text-[#004235] mb-8 text-center">
          {content.title}
        </h2>
        <div className="max-w-3xl mx-auto">
          <div className="space-y-6 text-gray-700 leading-relaxed mb-10">
            {content.paragraphs.map((paragraph, index) => (
              <p key={index} className="text-lg">
                {paragraph}
              </p>
            ))}
          </div>

          {content.portalsUsed && content.portalsUsed.length > 0 && (
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-[#028475] mb-3">Portals Leveraged:</h3>
              <ul className="list-disc list-inside space-y-1 text-gray-700">
                {content.portalsUsed.map((portal, index) => (
                  <li key={index} className="text-lg">{portal}</li>
                ))}
              </ul>
            </div>
          )}

          {content.keyFeatures && content.keyFeatures.length > 0 && (
            <div>
              <h3 className="text-xl font-semibold text-[#028475] mb-3">Key Features Utilized:</h3>
              <ul className="list-disc list-inside space-y-1 text-gray-700">
                {content.keyFeatures.map((feature, index) => (
                  <li key={index} className="text-lg">{feature}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default SolutionSection;