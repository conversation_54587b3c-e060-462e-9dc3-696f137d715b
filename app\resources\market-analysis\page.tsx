"use client"

import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import HeroSection from "@/components/resources/market-analysis/HeroSection";
import PowerOfDataSection from "@/components/resources/market-analysis/PowerOfDataSection";
import AnalysisProductsSection from "@/components/resources/market-analysis/AnalysisProductsSection";
import AnalyticalApproachSection from "@/components/resources/market-analysis/AnalyticalApproachSection";
import CTASection from "@/components/resources/market-analysis/CTASection";

export default function MarketAnalysisPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <PowerOfDataSection />
      <AnalysisProductsSection />
      <AnalyticalApproachSection />
      <CTASection />

      <MainFooter />
    </div>
  );
}