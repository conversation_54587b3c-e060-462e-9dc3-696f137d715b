import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ChevronRight } from 'lucide-react';

const legalDocuments = [
  {
    title: 'Terms of Service (or User Agreement)',
    description: 'The primary agreement outlining the terms and conditions for using the StreamLnk platform and its associated services. Covers user responsibilities, platform use limitations, intellectual property rights, and more.',
    buttonText: 'READ TERMS OF SERVICE',
    href: '/legal/terms-of-service',
  },
  {
    title: 'Privacy Policy',
    description: 'Details how StreamLnk collects, uses, stores, shares, and protects your personal and business data in compliance with global data privacy regulations (e.g., GDPR, CCPA).',
    buttonText: 'VIEW PRIVACY POLICY',
    href: '/legal/privacy-policy', // Placeholder
  },
  {
    title: 'Cookie Policy',
    description: 'Explains how we use cookies and similar tracking technologies on our websites and portals to enhance user experience, analyze site traffic, and for marketing purposes. Includes information on how to manage your cookie preferences.',
    buttonText: 'UNDERSTAND OUR COOKIE USE',
    href: '/legal/cookie-policy', // Placeholder
  },
  {
    title: 'Acceptable Use Policy',
    description: 'Outlines prohibited activities and conduct on the StreamLnk platform to ensure a safe, respectful, and lawful environment for all users.',
    buttonText: 'REVIEW ACCEPTABLE USE POLICY',
    href: '/legal/acceptable-use-policy', // Placeholder
  },
  {
    title: 'StreamLnk Partner Agreements (General Information)',
    description: 'Information regarding specific agreements for Suppliers, Agents, Logistics Providers, and other partners (actual agreements provided during onboarding).',
    buttonText: 'LEARN ABOUT PARTNER TERMS',
    href: '/legal/partner-agreements', // Placeholder
  },
  {
    title: 'Intellectual Property Notice',
    description: 'Information regarding StreamLnk\'s trademarks, copyrights, patents (if applicable), and the protection of our intellectual property.',
    buttonText: 'VIEW IP NOTICE',
    href: '/legal/ip-notice', // Placeholder
  },
  {
    title: 'Dispute Resolution Policy',
    description: 'Outlines the processes and mechanisms for resolving disputes that may arise between users, or between users and StreamLnk, in connection with platform transactions or services.',
    buttonText: 'UNDERSTAND DISPUTE RESOLUTION',
    href: '/legal/dispute-resolution', // Placeholder
  },
  {
    title: 'Fraud Awareness & Reporting',
    description: 'Information and resources to help users identify and report suspicious or fraudulent activity, and how StreamLnk addresses such concerns.',
    buttonText: 'VISIT FRAUD AWARENESS CENTER',
    href: '/legal/fraud-awareness', // Placeholder
  },
  {
    title: 'Data Security Overview',
    description: 'A high-level overview of the security measures StreamLnk implements to protect user data and platform integrity.',
    buttonText: 'LEARN ABOUT OUR SECURITY MEASURES',
    href: '/legal/data-security', // Placeholder
  },
  {
    title: 'Accessibility Statement',
    description: 'Our commitment to making the StreamLnk platform accessible to users of all abilities.',
    buttonText: 'READ ACCESSIBILITY STATEMENT',
    href: '/legal/accessibility-statement', // Placeholder
  },
];

export default function LegalCenterPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="bg-[#F2F2F2] py-16 md:py-24">
          <div className="container mx-auto px-4 md:px-6 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              StreamLnk Legal & Compliance Center
            </h1>
            <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto">
              Your resource for understanding the terms, policies, and legal frameworks that govern your use of the StreamLnk platform and services. We are committed to transparency and operating with integrity.
            </p>
          </div>
        </section>

        <div className="container mx-auto px-4 md:px-6 py-12 md:py-16">
          {/* Welcome Section */}
          <section className="mb-12 md:mb-16">
            <h2 className="text-3xl font-semibold text-[#004235] mb-4">
              Welcome to the StreamLnk Legal Center
            </h2>
            <p className="text-gray-700 leading-relaxed mb-3">
              Understanding Your Rights and Responsibilities
            </p>
            <p className="text-gray-600 leading-relaxed">
              StreamLnk Inc. and its affiliates ("StreamLnk") provide this Legal Center as a central repository for important legal documents, policies, and notices that apply to all users of our websites, portals, and services. We encourage you to review these documents carefully to understand your rights and obligations when interacting with our ecosystem. Our commitment is to provide a secure, compliant, and trustworthy environment for global industrial trade.
            </p>
          </section>

          <Separator className="my-8 md:my-12 bg-gray-200" />

          {/* Key Legal Documents & Policies */}
          <section className="mb-12 md:mb-16">
            <h2 className="text-3xl font-semibold text-[#004235] mb-2">
              Navigate Our Legal Framework
            </h2>
            <p className="text-xl text-gray-700 mb-8">
              Key Legal Documents & Policies
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
              {legalDocuments.map((doc, index) => (
                <div key={index} className="bg-gray-50 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col justify-between">
                  <div>
                    <h3 className="text-xl font-semibold text-[#028475] mb-3">{doc.title}</h3>
                    <p className="text-gray-600 text-sm leading-relaxed mb-4">
                      {doc.description}
                    </p>
                  </div>
                  <Link href={doc.href} passHref legacyBehavior>
                    <Button variant="outline" className="w-full mt-auto border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white">
                      {doc.buttonText} <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              ))}
            </div>
          </section>

          <Separator className="my-8 md:my-12 bg-gray-200" />

          {/* Regional Legal Information */}
          <section className="mb-12 md:mb-16">
            <h2 className="text-3xl font-semibold text-[#004235] mb-4">
              Regional Legal Information
            </h2>
            <p className="text-gray-700 leading-relaxed mb-3">
              Country-Specific Notices & Addenda
            </p>
            <p className="text-gray-600 leading-relaxed">
              Depending on your region, additional terms or notices may apply. Please select your region if specific addenda are available.
            </p>
            <p className="text-gray-600 leading-relaxed mt-3 p-4 bg-blue-50 border-l-4 border-[#028475]">
              Currently, our global Terms of Service and Privacy Policy apply to all users. Region-specific addenda will be posted here as developed.
            </p>
          </section>

          <Separator className="my-8 md:my-12 bg-gray-200" />

          {/* Staying Informed */}
          <section className="mb-12 md:mb-16">
            <h2 className="text-3xl font-semibold text-[#004235] mb-4">
              Staying Informed
            </h2>
            <p className="text-gray-700 leading-relaxed mb-3">
              Updates to Our Legal Terms
            </p>
            <p className="text-gray-600 leading-relaxed">
              StreamLnk may update these legal documents from time to time. We will notify users of material changes as required by law or through platform announcements. We encourage you to review these pages periodically. The date of the last update for each document will be clearly indicated.
            </p>
          </section>

          <Separator className="my-8 md:my-12 bg-gray-200" />

          {/* Legal Inquiries */}
          <section>
            <h2 className="text-3xl font-semibold text-[#004235] mb-4">
              Legal Inquiries
            </h2>
            <p className="text-gray-700 leading-relaxed mb-3">
              Contact Our Legal Department
            </p>
            <p className="text-gray-600 leading-relaxed mb-4">
              For specific legal inquiries not addressed by the documents provided, or to report a legal concern, please contact our legal department via:
            </p>
            <ul className="list-disc list-inside text-gray-600 leading-relaxed space-y-2 mb-4">
              <li>Email: <a href="mailto:<EMAIL>" className="text-[#028475] hover:text-[#004235] hover:underline"><EMAIL></a></li>
              <li>Mailing Address: [StreamLnk Inc. Legal Department, HQ Address]</li>
            </ul>
            <p className="text-sm text-gray-500">
              (Note: For general support or platform issues, please use the Customer Service channels.)
            </p>
          </section>
        </div>
      </main>
      <BottomFooter />
    </div>
  );
}