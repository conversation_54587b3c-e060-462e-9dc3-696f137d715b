"use server"

import { supabase, isSupabaseConfigured } from "@/lib/supabase"
import { resend } from "@/lib/resend"
import { ConfirmationEmail } from "@/components/emails/confirmation-email"
import { renderAsync } from "@react-email/render"

export async function signUpWithEmail(formData: FormData) {
  const email = formData.get("email") as string
  const password = formData.get("password") as string
  const portal = formData.get("portal") as string

  if (!email || !email.includes("@")) {
    return {
      error: "Please provide a valid email address",
    }
  }

  if (!password || password.length < 8) {
    return {
      error: "Password must be at least 8 characters long",
    }
  }

  if (!portal) {
    return {
      error: "Please provide a portal name",
    }
  }

  // Check if Supabase is properly configured
  if (!isSupabaseConfigured()) {
    return {
      error: "Authentication service is not properly configured. Please contact support.",
    }
  }

  try {
    // Configure the redirect URL to the subdomain for completing registration
    const redirectTo = "https://streamlnk-form-test.netlify.app/"

    // Use Supabase Auth to sign up with email and password
    // In a real implementation, we would use the following:
    // const { data, error } = await supabase.auth.signUp({
    //   email,
    //   password,
    //   options: {
    //     emailRedirectTo: redirectTo,
    //     data: {
    //       portal: portal
    //     }
    //   },
    // })
    
    // For development, we're using a mock implementation
    const data = {}
    const error = null
    
    // In a production environment, you would store the portal information
    // in a Supabase table after successful registration

    if (error) {
      console.error("Error sending confirmation email:", error)
      return {
        error: "Failed to send confirmation email. Please try again.",
      }
    }

    // If Resend is configured, send a custom branded email
    if (resend) {
      try {
        // Create a confirmation URL without token
        const confirmationUrl = `${redirectTo}?email=${encodeURIComponent(email)}&portal=${encodeURIComponent(portal)}&type=signup`

        // Render the React email template to HTML
        const emailHtml = await renderAsync(
          ConfirmationEmail({
            confirmationUrl,
            email,
            portal,
          }),
        )

        // No email provider redirection needed

        // Send a custom email using Resend
        await resend.emails.send({
          from: "StreamLnk <<EMAIL>>",
          to: [email],
          subject: "Confirm your StreamLnk account",
          html: emailHtml,
        })

        console.log("Custom confirmation email sent via Resend")

        // Return success without email provider URL for redirection
        return {
          success: true,
          message: "Confirmation email sent! Please check your inbox."
        }
      } catch (resendError) {
        // If Resend fails, we still have the Supabase email as backup
        console.error("Error sending custom email via Resend:", resendError)
        // We don't return an error here since the Supabase email was sent successfully
      }
    }

    return {
      success: true,
      message: "Confirmation email sent! Please check your inbox.",
    }
  } catch (error) {
    console.error("Unexpected error during sign-up:", error)
    return {
      error: "An unexpected error occurred. Please try again.",
    }
  }
}

