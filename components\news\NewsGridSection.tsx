"use client";

import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, ArrowRight } from "lucide-react";

interface NewsCardProps {
  slug: string;
  imageUrl: string;
  category: string;
  headline: string;
  excerpt: string;
  publicationDate: string;
}

const NewsCard: React.FC<NewsCardProps> = ({ slug, imageUrl, category, headline, excerpt, publicationDate }) => {
  return (
    <div className="bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden flex flex-col">
      <Link href={`/news/${slug}`} className="block group">
        <div className="relative w-full h-48 md:h-56">
          <Image
            src={imageUrl}
            alt={headline}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </div>
        <div className="p-5 flex flex-col flex-grow">
          <span className="inline-block bg-[#028475] text-white text-xs font-semibold px-2 py-1 rounded-full mb-3 self-start">
            {category}
          </span>
          <h3 className="text-lg md:text-xl font-semibold text-[#004235] mb-2 group-hover:text-[#028475] transition-colors duration-300">
            {headline}
          </h3>
          <p className="text-gray-700 text-sm mb-4 line-clamp-3 flex-grow">
            {excerpt}
          </p>
          <p className="text-xs text-gray-500 mb-4">
            {publicationDate}
          </p>
        </div>
      </Link>
      <div className="p-5 pt-0 mt-auto">
        <Button
          className="bg-[#004235] hover:bg-[#028475] text-white w-full text-sm" 
          asChild
        >
          <Link href={`/news/${slug}`}> 
            READ FULL STORY
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </div>
    </div>
  );
};

// Example News Data (to be replaced with actual data source/API call)
const sampleNewsItems: NewsCardProps[] = [
  {
    slug: "next-gen-ai-platform-enhancements",
    imageUrl: "/images/placeholder-ai-graphic.jpg", // Placeholder
    category: "Product Launches & Updates",
    headline: "StreamLnk Rolls Out Next-Generation AI and Platform Enhancements to Further Optimize Global Industrial Trade",
    excerpt: "Major updates to StreamIndex™, iScore™, API capabilities, and portal user experience aim to deliver unprecedented speed, intelligence, and efficiency.",
    publicationDate: "October 28, 2023", // Example Date
  },
  {
    slug: "sustainable-sourcing-initiative",
    imageUrl: "/images/placeholder-green-industrial.jpg", // Placeholder
    category: "Sustainability Initiatives",
    headline: "StreamLnk Champions Sustainable Future with New Global Sourcing Initiative for Industrial Materials",
    excerpt: "Platform enhancements and strategic partnerships aim to boost transparency, traceability, and adoption of recycled and low-carbon materials.",
    publicationDate: "October 25, 2023", // Example Date
  },
  {
    slug: "strategic-alliance-logistics-partner",
    imageUrl: "/images/placeholder-handshake-map.jpg", // Placeholder
    category: "Partnerships & Collaborations",
    headline: "StreamLnk Announces Strategic Alliance with LogiCorp to Enhance Global Logistics Capabilities",
    excerpt: "This collaboration will integrate LogiCorp's advanced logistics network, offering StreamLnk users even more comprehensive freight solutions.",
    publicationDate: "October 22, 2023", // Example Date
  },
  {
    slug: "milestone-10k-verified-businesses",
    imageUrl: "/images/placeholder-diverse-team.jpg", // Placeholder
    category: "Company Milestones",
    headline: "StreamLnk Celebrates Surpassing 10,000 Verified Businesses in Its Global Ecosystem",
    excerpt: "A testament to the platform's growing impact and the increasing demand for digitized industrial trade solutions.",
    publicationDate: "October 19, 2023", // Example Date
  },
  // Add more sample news items if needed for pagination demonstration
  {
    slug: "industry-leadership-award-2023",
    imageUrl: "/images/placeholder-award.jpg", // Placeholder
    category: "Awards & Recognition",
    headline: "StreamLnk Recognized for Industry Leadership in Trade Technology",
    excerpt: "Honored for innovative solutions and significant contributions to the digitalization of industrial commerce.",
    publicationDate: "October 15, 2023",
  },
  {
    slug: "new-api-documentation-launch",
    imageUrl: "/images/placeholder-api-docs.jpg", // Placeholder
    category: "Product Launches & Updates",
    headline: "Comprehensive API Documentation Launched for Developers",
    excerpt: "Empowering developers to build powerful integrations with the StreamLnk platform.",
    publicationDate: "October 12, 2023",
  },
];

export default function NewsGridSection() {
  // Basic pagination logic (can be expanded with state management)
  const currentPage = 1;
  const itemsPerPage = 6; // Show 6 news items per page
  const totalPages = Math.ceil(sampleNewsItems.length / itemsPerPage);
  const paginatedNewsItems = sampleNewsItems.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

  return (
    <section className="py-12 md:py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-[#004235] mb-10 text-center">
          Latest News & Announcements
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {paginatedNewsItems.map((newsItem) => (
            <NewsCard key={newsItem.slug} {...newsItem} />
          ))}
        </div>

        {/* Pagination */} 
        {totalPages > 1 && (
          <div className="mt-12 flex justify-center items-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white disabled:opacity-50"
              disabled={currentPage === 1}
              // onClick={() => setCurrentPage(currentPage - 1)} // Add state management for this
            >
              <ChevronLeft className="h-5 w-5" />
              <span className="sr-only">Previous Page</span>
            </Button>
            {[...Array(totalPages)].map((_, i) => (
              <Button
                key={i + 1}
                variant={currentPage === i + 1 ? "default" : "outline"}
                className={ 
                    currentPage === i + 1
                      ? "bg-[#004235] text-white hover:bg-[#028475]"
                      : "border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
                  }
                // onClick={() => setCurrentPage(i + 1)} // Add state management for this
              >
                {i + 1}
              </Button>
            ))}
            <Button
              variant="outline"
              size="icon"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white disabled:opacity-50"
              disabled={currentPage === totalPages}
              // onClick={() => setCurrentPage(currentPage + 1)} // Add state management for this
            >
              <ChevronRight className="h-5 w-5" />
              <span className="sr-only">Next Page</span>
            </Button>
          </div>
        )}
      </div>
    </section>
  );
}