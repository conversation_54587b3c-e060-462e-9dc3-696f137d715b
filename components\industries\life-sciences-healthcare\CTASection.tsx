"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"

export default function CTASection() {
  return (
    <section className="bg-[#f3f4f6] py-20 md:py-28">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6 max-w-2xl mx-auto">
          Building a Healthier Future, Together, Through Digital Innovation.
        </h2>
        <p className="text-lg text-gray-700 mb-10 max-w-xl mx-auto">
          Partner with StreamLnk for Your Critical Healthcare Supply Needs
        </p>
        <div className="flex flex-col sm:flex-row justify-center items-center gap-4">
          <Button className="bg-[#004235] hover:bg-[#028475] text-white px-8 py-3 text-lg w-full sm:w-auto" asChild>
            <Link href="/request-demo">Request a Demo</Link>
          </Button>
          <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-8 py-3 text-lg w-full sm:w-auto" asChild>
            <Link href="/suppliers/e-stream">List Your Materials</Link>
          </Button>
        </div>
      </div>
    </section>
  )
}