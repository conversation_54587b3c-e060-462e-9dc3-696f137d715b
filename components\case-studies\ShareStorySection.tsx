"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Send } from "lucide-react";

export default function ShareStorySection() {
  return (
    <section id="share-story" className="py-16 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center bg-white p-10 rounded-lg shadow-xl">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">
            Want to Share Your StreamLnk Success?
          </h2>
          <p className="text-xl text-[#028475] font-semibold mb-6">
            Become a StreamLnk Success Story
          </p>
          <p className="text-lg text-gray-700 mb-8">
            Are you achieving great results with the StreamLnk platform? We'd love to hear about it and potentially feature your business. Sharing your story can inspire others and showcase your leadership in adopting innovative solutions.
          </p>
          <Button
            className="bg-[#028475] hover:bg-[#00564e] text-white px-8 py-3 text-lg w-full sm:w-auto"
            size="lg"
            asChild
          >
            <Link href="/contact-us?subject=Share My StreamLnk Success Story">
              SHARE YOUR STORY WITH US
              <Send className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}