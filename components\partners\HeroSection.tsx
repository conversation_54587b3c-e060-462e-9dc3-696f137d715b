"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#004235] mb-6">
              Unlock New Opportunities: Partner with StreamLnk
            </h1>
            <p className="text-lg md:text-xl text-gray-700 mb-10">
              StreamLnk is building a powerful, interconnected ecosystem for the industrial materials supply chain. We invite innovative suppliers, logistics experts, technology providers, sales agents, and data partners to join us in creating a smarter, more efficient, and trusted future for global commerce.
            </p>
            <div className="flex">
              <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white transition-colors text-lg px-8 py-3" asChild>
                <Link href="#partnership-pathways"> {/* Placeholder link, update as needed */}
                  EXPLORE OPPORTUNITIES
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[450px] rounded-lg overflow-hidden shadow-xl mt-12 lg:mt-0">
            <Image
              src="/images/placeholder-partners-general.webp" // TODO: User to replace with a relevant image for general partnerships
              alt="Partner with StreamLnk"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: User to replace with a relevant image for general partnerships */}
          </div>
        </div>
      </div>
    </section>
  );
}