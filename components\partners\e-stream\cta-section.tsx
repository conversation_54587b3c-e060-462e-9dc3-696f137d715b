import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export function CtaSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-[#004235]">Start Selling Smarter</h2>
        <p className="text-xl max-w-3xl mx-auto mb-8 text-gray-700">
          E-Stream isn't just a marketplace. It's a modern B2B operating system for the next generation of global
          suppliers, designed to help you reach global buyers with full delivery automation, compliance tracking, and
          bidding visibility.
        </p>
        <Button size="lg" className="bg-[#004235] text-white hover:bg-[#028475] transition-colors" asChild>
          <Link href="/signup?portal=e-stream">Apply Today to Become a Verified E-Stream Supplier</Link>
        </Button>
        <p className="text-sm mt-4 text-gray-600">Portal Access: E-Stream</p>
      </div>
    </section>
  )
}
