import type { Metada<PERSON> } from "next"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import HeroSection from "@/components/solutions/risk-compliance-insights/HeroSection"
import ChallengesSection from "@/components/solutions/risk-compliance-insights/ChallengesSection"
import FeaturesSection from "@/components/solutions/risk-compliance-insights/FeaturesSection"
import BenefitsSection from "@/components/solutions/risk-compliance-insights/BenefitsSection"
import IntegrationSection from "@/components/solutions/risk-compliance-insights/IntegrationSection"
import CTASection from "@/components/solutions/risk-compliance-insights/CTASection"

export const metadata: Metadata = {
  title: "Risk & Compliance Insights | StreamLnk",
  description:
    "Minimize uncertainty in your global industrial trade operations. StreamLnk's StreamResources+ and iScore™ deliver unparalleled insights into counterparty risk, operational hazards, and compliance landscapes, empowering you to make safer, more informed decisions.",
}

export default function RiskComplianceInsightsPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <FeaturesSection />

      <BenefitsSection />

      <IntegrationSection />

      <CTASection />

      <MainFooter />
    </div>
  );
}