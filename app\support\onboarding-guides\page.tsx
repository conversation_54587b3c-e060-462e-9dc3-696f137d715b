import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Link from 'next/link';
import { ChevronRight, User, Factory, Users, Truck, Globe, Package, HelpCircle, LifeBuoy } from 'lucide-react';
import Image from "next/image";

const onboardingGuides = [
  {
    icon: <User className="w-12 h-12 text-[#028475] mb-4" />,
    title: "Getting Started on MyStreamLnk: A Buyer's Guide",
    description: "Covers: Account registration, profile completion, navigating product discovery, submitting RFQs, understanding quotes, placing orders, tracking shipments, managing payments & documents, understanding iScore™ & ESG features.",
    buttonText: "VIEW BUYER ONBOARDING GUIDE",
    buttonLink: "/support/onboarding-guides/buyer" // Placeholder
  },
  {
    icon: <Factory className="w-12 h-12 text-[#028475] mb-4" />,
    title: "Launching Your Global Storefront: An E-Stream Supplier's Guide",
    description: "Covers: Account registration, company verification, creating compelling product listings (with specs, certifications, pricing using StreamIndex™ assist), managing inventory, responding to RFQs/orders, participating in auctions, coordinating fulfillment, understanding payouts & fees, managing logistics/technical contacts, leveraging Tier & Rewards.",
    buttonText: "VIEW SUPPLIER ONBOARDING GUIDE",
    buttonLink: "/support/onboarding-guides/supplier" // Placeholder
  },
  {
    icon: <Users className="w-12 h-12 text-[#028475] mb-4" />,
    title: "Building Your Business with MyStreamLnk+: An Agent's Guide",
    description: "Covers: Registration & agreement, setting up your agent profile, onboarding new customers, using the quoting tools, managing your client portfolio, tracking orders and commissions, understanding the Tier & Rewards program.",
    buttonText: "VIEW AGENT ONBOARDING GUIDE",
    buttonLink: "/support/onboarding-guides/agent" // Placeholder
  },
  {
    icon: <Truck className="w-12 h-12 text-[#028475] mb-4" />,
    title: "Hauling Success: A StreamFreight Carrier's Onboarding Guide",
    description: "Covers: Registration & document submission (DOT, insurance, etc.), navigating the job board, bidding on loads, accepting assignments, providing real-time status updates (including POD scanning), submitting invoices, understanding performance KPIs & Tier program.",
    buttonText: "VIEW LAND FREIGHT ONBOARDING GUIDE",
    buttonLink: "/support/onboarding-guides/land-freight" // Placeholder
  },
  {
    icon: <Globe className="w-12 h-12 text-[#028475] mb-4" />,
    title: "Clearing the Way: A StreamGlobe+ Customs Agent's Guide",
    description: "Covers: Registration & license verification, managing assignments, accessing automated document packages, POA management, updating clearance statuses, billing for services, understanding Tier & Rewards.",
    buttonText: "VIEW CUSTOMS AGENT ONBOARDING GUIDE",
    buttonLink: "/support/onboarding-guides/customs-agent" // Placeholder
  },
  {
    icon: <Package className="w-12 h-12 text-[#028475] mb-4" />,
    title: "Your Role in Product Readiness: A StreamPak Partner's Guide",
    description: "Covers: Registration & facility verification, accepting packaging/storage jobs, inventory management (shortage reporting), status updates, QC and document uploads, understanding SLAs & Tier program.",
    buttonText: "VIEW PACKAGING/WAREHOUSE ONBOARDING GUIDE",
    buttonLink: "/support/onboarding-guides/packaging-warehouse" // Placeholder
  }
];

const generalTopics = [
  "Navigating Your Dashboard",
  "Setting Up Your Company Profile",
  "Managing User Permissions (for company accounts)",
  "Understanding StreamLnk Fees & Billing",
  "Our Commitment to Security & Data Privacy",
  "Introduction to StreamIndex™ and iScore™",
  "How to Use the StreamLnk Auction Marketplace"
];

const OnboardingGuidesPage = () => {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="bg-[#F2F2F2] py-16 md:py-24">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
                  Welcome to StreamLnk: Your Step-by-Step Guide to Getting Started
                </h1>
                <p className="text-xl text-gray-700 mb-8">
                  Begin your journey on the StreamLnk global industrial trade ecosystem with confidence. Our comprehensive onboarding guides provide clear instructions and best practices to help you set up your account, understand key features, and start transacting efficiently.
                </p>
                <Button
                  className="bg-[#004235] hover:bg-[#028475] text-white px-6"
                  size="lg"
                  asChild
                >
                  <Link href="/contact-us?subject=onboarding-assistance">
                    GET ASSISTANCE
                    <HelpCircle className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
              </div>
              <div className="relative w-full h-[300px] md:h-[450px] rounded-lg overflow-hidden shadow-xl">
                <Image
                  src="/images/placeholder-onboarding-hero.webp" // TODO: User to replace with a relevant image for onboarding
                  alt="StreamLnk Onboarding Guide Hero Image"
                  fill
                  className="object-cover"
                  priority
                />
                {/* TODO: User to replace with a relevant image for onboarding guides */}
              </div>
            </div>
          </div>
        </section>

        {/* Why Use Our Onboarding Guides? Section */}
        <section className="py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] text-center mb-6">
              Ensuring a Smooth & Successful Start on StreamLnk
            </h2>
            <p className="text-lg text-gray-600 text-center mb-10 max-w-2xl mx-auto">
              Why Use Our Onboarding Guides?
            </p>
            <div className="max-w-3xl mx-auto space-y-4 text-gray-700">
              <p>We want your initial experience with StreamLnk to be seamless and productive. Our onboarding guides are designed to:</p>
              <ul className="list-disc list-inside space-y-2 pl-4">
                <li>Provide clear, step-by-step instructions for account registration and profile setup.</li>
                <li>Help you understand the core functionalities of your specific StreamLnk portal.</li>
                <li>Guide you through essential initial tasks (e.g., listing your first product, submitting your first RFQ, completing compliance verification).</li>
                <li>Offer tips and best practices for maximizing your success on the platform.</li>
                <li>Reduce your learning curve and get you transacting faster.</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Onboarding Guides by User Role Section */}
        <section className="bg-[#f3f4f6] py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] text-center mb-4">
              Select Your Role to Access Your Tailored Onboarding Guide
            </h2>
            <p className="text-lg text-gray-600 text-center mb-12 max-w-2xl mx-auto">
              Onboarding Guides by User Role / Portal
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {onboardingGuides.map((guide, index) => (
                <Card key={index} className="flex flex-col bg-white hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="items-center text-center">
                    {guide.icon}
                    <CardTitle className="text-[#004235] text-xl leading-tight">{guide.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="flex-grow text-center">
                    <p className="text-gray-600 text-sm mb-6">
                      {guide.description}
                    </p>
                  </CardContent>
                  <div className="p-6 pt-0 text-center">
                    <Link href={guide.buttonLink} passHref>
                      <Button className="w-full bg-[#004235] hover:bg-[#028475] text-white">
                        {guide.buttonText} <ChevronRight className="ml-2 h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* General Platform Onboarding Topics Section */}
        <section className="py-12 md:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] text-center mb-6">
              Understanding Key StreamLnk Concepts
            </h2>
            <p className="text-lg text-gray-600 text-center mb-10 max-w-2xl mx-auto">
              General Platform Onboarding Topics
            </p>
            <div className="max-w-2xl mx-auto bg-gray-50 p-6 md:p-8 rounded-lg shadow">
              <ul className="space-y-3">
                {generalTopics.map((topic, index) => (
                  <li key={index} className="flex items-center text-gray-700">
                    <ChevronRight className="h-5 w-5 text-[#028475] mr-2 flex-shrink-0" />
                    {topic}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </section>

        {/* Need Further Assistance Section */}
        <section className="py-16 bg-[#F2F2F2]">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
                Support For Your Start
              </h2>
              <p className="text-xl text-gray-700 mb-8">
                Questions during onboarding not covered in guides? Our support teams are ready to help you achieve a successful start.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
                  size="lg"
                  asChild
                >
                  <Link href="/contact-us"> {/* Assuming contact-us page exists */}
                    CONTACT SUPPORT
                    <HelpCircle className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button 
                  variant="outline" 
                  className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
                  size="lg"
                  asChild
                >
                  <Link href="/support/faqs">
                    BROWSE FAQS
                    <HelpCircle className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
              </div>
              <div className="mt-6">
                <Button 
                  variant="link" 
                  className="text-[#028475] hover:text-[#004235]"
                  asChild
                >
                  <Link href="/support">
                    VISIT SUPPORT HUB
                    <LifeBuoy className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

      </main>
      <BottomFooter />
    </div>
  );
};

export default OnboardingGuidesPage;