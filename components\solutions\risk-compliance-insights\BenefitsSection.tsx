import { CheckCircle } from "lucide-react";

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Making Informed Decisions, Mitigating Exposure
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Utilize StreamLnk insights for strategic advantage:
          </p>

          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Due Diligence</h3>
                  <p className="text-gray-600">Use iScore™ reports to thoroughly vet new suppliers, buyers, or logistics partners before engagement.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Risk-Based Sourcing</h3>
                  <p className="text-gray-600">Prioritize or avoid certain suppliers/regions based on their iScore™ and StreamIndex™ risk indicators.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Optimizing Trade Finance</h3>
                  <p className="text-gray-600">Leverage iScore™ data to negotiate better terms with BNPL providers or assess the need for Escrow.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Proactive Disruption Management</h3>
                  <p className="text-gray-600">Use predictive alerts to anticipate potential delays or compliance issues and take mitigating action.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Strengthening Compliance Programs</h3>
                  <p className="text-gray-600">Use platform insights to identify areas for improvement in your own internal compliance processes.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Strategic Market Entry</h3>
                  <p className="text-gray-600">Assess the risk profile of new geographic markets before committing resources.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}