"use client";

import { CalendarDays, BookMarked, Map<PERSON>inned, FileJson, Settings2, LockKeyhole, TestTubeDiagonal } from 'lucide-react';

const apiFeatures = [
  {
    icon: <CalendarDays className="h-10 w-10 text-[#004235]" />,
    title: "Vessel Schedules & Routes",
    details: [
      "Your System to StreamLnk: Provide regular feeds of your vessel schedules, port rotations, cut-off times, and transit times via API.",
      "Benefit: Your services become visible and selectable within StreamLnk's AI-powered logistics planning and quoting engine."
    ]
  },
  {
    icon: <BookMarked className="h-10 w-10 text-[#004235]" />,
    title: "Booking Requests & Confirmations",
    details: [
      "StreamLnk to Your System: Send standardized booking requests (container type, size, POL, POD, commodity details).",
      "Your System to StreamLnk: Return booking confirmations, booking numbers, vessel/voyage details, and container assignments via API."
    ]
  },
  {
    icon: <MapPinned className="h-10 w-10 text-[#004235]" />,
    title: "Shipment Tracking & Milestones",
    details: [
      "Your System to StreamLnk: Push real-time container tracking updates (e.g., Gate In, Loaded on Vessel, Sailed, Transshipment, ETA Updates, Discharged, Gate Out) via API.",
      "Benefit: Provides end-to-end visibility for StreamLnk users (buyers, suppliers, agents)."
    ]
  },
  {
    icon: <FileJson className="h-10 w-10 text-[#004235]" />,
    title: "Electronic Documentation Exchange",
    details: [
      "Your System to StreamLnk: API transmission of electronic Bill of Lading (eB/L) or Sea Waybill data, and potentially Arrival Notices.",
      "Benefit: Accelerates document flow for customs clearance and cargo release."
    ]
  }
];

const supportedTechnologies = [
  { icon: <Settings2 className="h-7 w-7 text-[#028475]" />, name: "RESTful APIs, EDI (EDIFACT, ANSI X12), XML, JSON." },
  { icon: <LockKeyhole className="h-7 w-7 text-[#028475]" />, name: "Secure authentication (OAuth 2.0, API Keys)." },
  { icon: <TestTubeDiagonal className="h-7 w-7 text-[#028475]" />, name: "Dedicated sandbox environment for testing." },
  { icon: <FileJson className="h-7 w-7 text-[#028475]" />, name: "Comprehensive API documentation and technical support from StreamLnk." },
];

export default function ApiConnectivitySection() {
  return (
    <section className="py-12 md:py-20 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">
            Seamless Connectivity: Our API-Driven Approach
          </h2>
          <p className="text-lg text-[#028475] font-semibold">
            How StreamGlobe Integration Works – A Technical Overview
          </p>
          <div className="w-20 h-1 bg-[#028475] mx-auto mt-2 mb-6"></div>
          <p className="text-lg text-gray-700 leading-relaxed">
            StreamLnk's StreamGlobe is designed for robust and reliable API integration with ocean carrier systems. We support industry-standard data formats and protocols to ensure smooth and efficient data exchange:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {apiFeatures.map((feature, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-[#e0f2f1] rounded-full mr-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-[#004235]">{feature.title}</h3>
              </div>
              <ul className="space-y-2 text-gray-600 text-sm leading-relaxed">
                {feature.details.map((detail, i) => (
                  <li key={i} className={`pl-4 relative before:content-[''] before:absolute before:left-0 before:top-1.5 before:h-1.5 before:w-1.5 before:rounded-full ${detail.startsWith('Benefit:') ? 'before:bg-green-500 font-medium text-gray-700' : 'before:bg-[#028475]'}`}>
                    {detail.startsWith('Benefit:') ? detail.substring(8) : detail}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="max-w-3xl mx-auto bg-white p-8 rounded-xl shadow-lg">
          <h3 className="text-2xl font-semibold text-[#004235] mb-6 text-center">
            Supported Technologies & Protocols
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {supportedTechnologies.map((tech, index) => (
              <div key={index} className="flex items-start p-4 bg-slate-50 rounded-md">
                <div className="flex-shrink-0 mr-3 mt-0.5">
                  {tech.icon}
                </div>
                <p className="text-gray-700 text-sm leading-relaxed">{tech.name}</p>
              </div>
            ))}
          </div>
        </div>

      </div>
    </section>
  );
}