"use client";

import { BarChart3, Globe, Package, Shield, Truck } from "lucide-react";
import { Timeline } from "@/components/ui/timeline";

export default function JourneySection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">Your Sourcing Journey on MyStreamLnk – Simplified</h2>
        </div>

        <div className="max-w-4xl mx-auto">
          <Timeline steps={[
            {
              title: "Discover & Filter",
              description: "Log in to MyStreamLnk and use advanced search to find the exact materials you need.",
              icon: <Globe className="h-8 w-8 text-white" />,
            },
            {
              title: "Compare & Evaluate",
              description: "Review product specifications, supplier iScores™, pricing indications, and ESG data.",
              icon: <BarChart3 className="h-8 w-8 text-white" />,
            },
            {
              title: "Request Quotes",
              description: "Submit RFQs or receive instant AI-generated quotes with landed cost estimates.",
              icon: <Shield className="h-8 w-8 text-white" />,
            },
            {
              title: "Select & Order",
              description: "Choose the best offer and place your order securely with flexible payment options.",
              icon: <Package className="h-8 w-8 text-white" />,
            },
            {
              title: "Track Fulfillment",
              description: "Monitor your shipment in real-time from origin to your doorstep.",
              icon: <Truck className="h-8 w-8 text-white" />,
            },
            {
              title: "Manage Documents & Payments",
              description: "Access all paperwork and manage invoices centrally.",
              icon: <BarChart3 className="h-8 w-8 text-white" />,
            },
          ]} />
        </div>
      </div>
    </section>
  );
}