import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, CheckCircle, Globe, DollarSign, CreditCard, Shield } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

// Challenges Section Component
export function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Navigating the Complex World of Cross-Border Finance?
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            For businesses engaged in global industrial trade, managing payments across different currencies and banking systems can be a significant hurdle, often involving:
          </p>
          
          <div className="space-y-4">
            {[
              "High foreign exchange (FX) conversion fees and unfavorable rates from traditional banks.",
              "Lack of transparency in the FX rates applied to transactions.",
              "Delays in international wire transfers and payment settlements.",
              "Complexities in managing multiple currency bank accounts.",
              "Administrative overhead of reconciling payments in different currencies.",
              "Risk of payment failures or rejections due to international banking protocols."
            ].map((challenge, index) => (
              <div key={index} className="flex items-start">
                <div className="bg-[#028475]/10 p-2 rounded-full mr-4 mt-1">
                  <CheckCircle className="h-5 w-5 text-[#028475]" />
                </div>
                <p className="text-gray-700">{challenge}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

// Solution Overview Section Component
export function SolutionOverviewSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Global Trade, Simplified Payments: How StreamLnk Handles Multiple Currencies
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            StreamLnk is engineered to facilitate smooth and cost-effective international financial transactions. Our platform integrates multi-currency functionality directly into your procurement and sales workflows:
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold text-[#004235] mb-4 flex items-center">
                <Globe className="h-6 w-6 text-[#028475] mr-3" />
                Multi-Currency Quoting & Invoicing
              </h3>
              <ul className="space-y-2 text-gray-700">
                <li>• Suppliers (on E-Stream) can list products and issue quotes in their preferred local currency or major trade currencies (USD, EUR, etc.).</li>
                <li>• Buyers (on MyStreamLnk) can view indicative prices in their local currency and receive final invoices in agreed-upon transaction currencies.</li>
              </ul>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold text-[#004235] mb-4 flex items-center">
                <DollarSign className="h-6 w-6 text-[#028475] mr-3" />
                Transparent FX Management
              </h3>
              <ul className="space-y-2 text-gray-700">
                <li>• StreamLnk partners with leading global payment processors and FX providers to offer competitive, near-real-time exchange rates.</li>
                <li>• Indicative FX rates are displayed during the quoting process where applicable, providing transparency before commitment.</li>
              </ul>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold text-[#004235] mb-4 flex items-center">
                <CreditCard className="h-6 w-6 text-[#028475] mr-3" />
                Centralized Treasury Operations
              </h3>
              <p className="text-gray-700">StreamLnk manages secure multi-currency accounts to facilitate efficient collection from buyers and payouts to suppliers and service providers globally.</p>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold text-[#004235] mb-4 flex items-center">
                <Shield className="h-6 w-6 text-[#028475] mr-3" />
                Localized Payment Instructions
              </h3>
              <p className="text-gray-700">Buyers receive clear payment instructions tailored to their country and the transaction currency, minimizing transfer errors.</p>
            </div>
          </div>
          
          <div className="mt-8 space-y-4">
            {[
              "Automated Reconciliation: Our system helps reconcile payments across different currencies, simplifying accounting for both buyers and suppliers.",
              "Reduced Transaction Fees: By leveraging specialized FX partners and optimizing payment routing, StreamLnk aims to reduce the overall cost of cross-border transactions compared to traditional banking methods.",
              "Support for Major Trade Currencies: Full support for transactions in USD, EUR, GBP, JPY, CNY, AED, and a growing list of other key global and regional currencies."
            ].map((feature, index) => (
              <div key={index} className="flex items-start">
                <div className="bg-[#028475]/10 p-2 rounded-full mr-4 mt-1">
                  <CheckCircle className="h-5 w-5 text-[#028475]" />
                </div>
                <p className="text-gray-700">{feature}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

// Workflow Section Component
export function WorkflowSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Seamless Flow from Global Quote to Local Settlement
          </h2>
          
          <div className="relative">
            {/* Vertical line */}
            <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-[#028475] hidden md:block"></div>
            
            <div className="space-y-8">
              {[
                {
                  step: "Supplier (E-Stream)",
                  description: "Lists product in EUR."
                },
                {
                  step: "Buyer (MyStreamLnk - USA)",
                  description: "Views product with indicative price in USD (converted at a near-real-time rate)."
                },
                {
                  step: "Quote & Order",
                  description: "Final transaction agreed in EUR (or USD, as negotiated). Invoice generated in the agreed currency."
                },
                {
                  step: "Buyer Payment",
                  description: "Buyer in USA pays the USD equivalent (if invoiced in USD) or initiates a EUR wire transfer to StreamLnk's designated multi-currency account."
                },
                {
                  step: "StreamLnk Treasury",
                  description: "Receives funds, manages FX conversion (if necessary) transparently through partners."
                },
                {
                  step: "Supplier Payout",
                  description: "Supplier in Europe receives payout in EUR to their local bank account, minus StreamLnk platform fees."
                },
                {
                  step: "All Parties",
                  description: "View clear transaction records and FX details (where applicable) in their respective portals."
                }
              ].map((item, index) => (
                <div key={index} className="flex">
                  <div className="md:w-1/3 pr-4 font-semibold text-[#004235] flex items-start">
                    <div className="bg-[#028475] text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 flex-shrink-0 relative z-10">
                      {index + 1}
                    </div>
                    <span>{item.step}</span>
                  </div>
                  <div className="md:w-2/3 text-gray-700">
                    {item.description}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Benefits Section Component
export function BenefitsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Expand Globally with Financial Confidence
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[
              {
                title: "Reduced FX Costs",
                description: "Access more competitive exchange rates than typically offered by traditional banks."
              },
              {
                title: "Increased Transparency",
                description: "Clear visibility into exchange rates used for transactions."
              },
              {
                title: "Simplified Operations",
                description: "Manage international payments without needing multiple foreign currency bank accounts."
              },
              {
                title: "Faster Settlements",
                description: "Optimized payment routing can lead to quicker receipt of funds."
              },
              {
                title: "Enhanced Global Reach",
                description: "Easily transact with buyers and suppliers in their preferred currencies, removing a key barrier to international trade."
              },
              {
                title: "Improved Cash Flow Management",
                description: "Better predictability of landed costs and received amounts."
              },
              {
                title: "Reduced Administrative Burden",
                description: "Automated reconciliation and clear reporting simplify accounting."
              }
            ].map((benefit, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-start">
                  <div className="bg-[#028475]/10 p-2 rounded-full mr-4 mt-1">
                    <CheckCircle className="h-5 w-5 text-[#028475]" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-[#004235] text-lg mb-2">{benefit.title}</h3>
                    <p className="text-gray-700">{benefit.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

// Call to Action Section Component
export function CallToActionSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Ready to Simplify Your International Payments?
          </h2>
          <p className="text-xl text-gray-700 mb-8">
            Go Global with StreamLnk – We Handle the Currency Complexities.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                REQUEST A DEMO TO SEE MULTI-CURRENCY IN ACTION
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            
            <Button 
              variant="outline" 
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
              size="lg"
              asChild
            >
              <Link href="/solutions/trade-finance">
                LEARN MORE ABOUT OUR SECURE PAYMENT SOLUTIONS
              </Link>
            </Button>
          </div>
          
          <div className="mt-6">
            <Button 
              variant="link" 
              className="text-[#028475] hover:text-[#004235]"
              asChild
            >
              <Link href="/contact-us">
                TALK TO A GLOBAL TRADE SPECIALIST
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}