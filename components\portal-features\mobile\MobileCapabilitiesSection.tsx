// components/portal-features/mobile/MobileCapabilitiesSection.tsx
import { Smartphone, Zap, Database } from "lucide-react";

export default function MobileCapabilitiesSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">StreamLnk on Your Device: Seamless Access, Full Functionality</h2>
          <h3 className="text-xl font-semibold text-[#028475] mb-6">StreamLnk's Approach: Mobile-First Design & Future Native Apps</h3>
          <p className="text-lg text-gray-700">
            We are committed to providing a seamless and powerful mobile experience for all StreamLnk users:
          </p>
        </div>

        <div className="max-w-5xl mx-auto">
          <h3 className="text-2xl font-bold text-[#004235] mb-6 border-b-2 border-[#028475] pb-2 inline-block">Current Mobile Capabilities:</h3>

          {/* Mobile-Responsive Web Portals */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <Smartphone className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">Mobile-Responsive Web Portals:</h4>
            </div>
            <ul className="list-disc pl-8 space-y-2 text-gray-700">
              <li>All StreamLnk portals (MyStreamLnk, E-Stream, MyStreamLnk+, StreamFreight, StreamGlobe+, StreamPak) are designed with a responsive layout, ensuring they adapt and function effectively on smartphone and tablet browsers.</li>
              <li>Access key features: view dashboards, track orders, manage quotes, update statuses, access documents, and communicate.</li>
            </ul>
          </div>

          {/* Key Mobile-Optimized Workflows */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <Zap className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">Key Mobile-Optimized Workflows:</h4>
            </div>
            <ul className="list-disc pl-8 space-y-2 text-gray-700">
              <li><span className="font-semibold">StreamFreight POD Scanning:</span> Drivers can use their smartphone camera via the web portal to instantly scan and upload Proof of Delivery documents.</li>
              <li><span className="font-semibold">Real-Time Notifications:</span> Receive critical alerts (new orders, shipment delays, compliance reminders) directly on your mobile device via email or browser notifications.</li>
              <li><span className="font-semibold">Quick Status Updates:</span> Simplified interfaces for logistics partners to update shipment milestones from the field.</li>
            </ul>
          </div>

          <h3 className="text-2xl font-bold text-[#004235] mb-6 border-b-2 border-[#028475] pb-2 inline-block">Future Dedicated Mobile Apps (iOS & Android - Phase 5 of Roadmap):</h3>

          {/* Enhanced User Experience */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <Smartphone className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">Enhanced Mobile Experience:</h4>
            </div>
            <ul className="list-disc pl-8 space-y-2 text-gray-700">
              <li><span className="font-semibold">Enhanced User Experience:</span> Native apps will offer an even more optimized and intuitive experience tailored for mobile devices.</li>
              <li><span className="font-semibold">Offline Capabilities (Planned):</span> Potential for some offline data access or task queuing for areas with limited connectivity.</li>
              <li><span className="font-semibold">Push Notifications:</span> Instant, native app notifications for critical alerts.</li>
              <li><span className="font-semibold">Device Feature Integration:</span> Deeper integration with device features like GPS for location-based services, enhanced camera functionality, and biometric login.</li>
            </ul>
          </div>

          {/* Portal-Specific Apps */}
          <div className="mb-10 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center mb-4">
              <div className="bg-[#004235] p-3 rounded-full mr-4">
                <Database className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-[#004235]">Portal-Specific Apps (Potential):</h4>
            </div>
            <ul className="list-disc pl-8 space-y-2 text-gray-700">
              <li><span className="font-semibold">MyStreamLnk Mobile:</span> For buyers to track orders, approve quotes, and communicate.</li>
              <li><span className="font-semibold">E-Stream Mobile:</span> For suppliers to manage urgent orders, respond to RFQs, and get inventory alerts.</li>
              <li><span className="font-semibold">StreamFreight Mobile:</span> Enhanced tools for drivers, including route optimization, POD capture, and direct communication.</li>
              <li><span className="font-semibold">MyStreamLnk+ Mobile:</span> For agents to manage clients, quotes, and commissions on the go.</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}