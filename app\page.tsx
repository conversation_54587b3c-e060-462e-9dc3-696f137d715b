import { CountrySelector } from "@/components/country-selector"
import { MainNav } from "@/components/main-nav"
import { WhoWeAre } from "@/components/home-page/who-we-are"
import { WhatWeDo } from "@/components/home-page/what-we-do"
import { Hero<PERSON>lider } from "@/components/home-page/hero-slider"
import { MainFooter } from "@/components/main-footer"
import { IndustrySolutionsSection } from "@/components/home-page/IndustrySolutionsSection"
import { ReportsPoliciesSection } from "@/components/home-page/ReportsPoliciesSection"
import { IndustryInsightsNewsSection } from "@/components/home-page/IndustryInsightsNewsSection"
import { BePartOfOurStorySection } from "@/components/home-page/BePartOfOurStorySection"

export default function HomePage() {
  return (
    <div className="flex min-h-screen flex-col">
      <CountrySelector />
      {/* MainNav hidden on homepage since hero includes its own navigation */}

      <main>
        {/* Hero Section with Video Slider */}
        <HeroSlider />

        {/* Who We Are Section */}
        <WhoWeAre />

        {/* What We Do Section */}
        <WhatWeDo />

        {/* Industry Solutions Section */}
        <IndustrySolutionsSection />

        {/* Reports and Policies Section */}
        <ReportsPoliciesSection />

        {/* Industry insights and StreamLnk news Section */}
        <IndustryInsightsNewsSection />

        {/* Be a Part of Our Story Section */}
        <BePartOfOurStorySection />

        {/* CTA Section removed as requested */}
      </main>

      {/* Footer */}
      <MainFooter />
    </div>
  )
}
