// components/portal-features/mobile/BenefitsSection.tsx
import { Smartphone, Zap, Users, Bell, BarChart2, FileCheck } from "lucide-react";

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">Productivity, Responsiveness, and Control – Anytime, Anywhere</h2>
          <h3 className="text-xl font-semibold text-[#028475] mb-8 text-center">Benefits of StreamLnk Mobile Access</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <Bell className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Stay Connected 24/7</h4>
              </div>
              <p className="text-gray-700">Never miss a critical update or urgent request.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <Zap className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Act Faster</h4>
              </div>
              <p className="text-gray-700">Respond to RFQs, approve orders, or update statuses immediately.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <Users className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Increase Field Efficiency</h4>
              </div>
              <p className="text-gray-700">Empower your on-the-ground teams (drivers, sales agents) with essential tools.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <Smartphone className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Enhanced Convenience</h4>
              </div>
              <p className="text-gray-700">Manage key aspects of your industrial trade operations from the palm of your hand.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <BarChart2 className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Improved Decision-Making</h4>
              </div>
              <p className="text-gray-700">Access real-time data and alerts to make timely choices.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <FileCheck className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Streamlined Workflows</h4>
              </div>
              <p className="text-gray-700">Perform essential tasks like POD scanning directly through the app.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}