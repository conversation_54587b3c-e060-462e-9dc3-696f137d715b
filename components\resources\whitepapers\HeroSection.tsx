"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";
import { DownloadCloud } from "lucide-react"; // Assuming you want an icon for the button

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Deep Dive into the Future of Industrial Trade: StreamLnk Whitepapers
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Access our comprehensive research, expert analysis, and strategic insights on the critical topics shaping the global industrial materials supply chain. Download our free whitepapers to inform your strategy and operations.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
                size="lg"
                asChild
              >
                <Link href="#all-whitepapers">
                  BROWSE WHITEPAPERS
                  <DownloadCloud className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              {/* You can add a secondary button here if needed, following the case-studies example */}
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/resources/whitepapers-hero.webp" // Placeholder - suggest user to replace
              alt="StreamLnk Whitepapers Hero Image"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: Consider adding a more relevant image or illustration for whitepapers */}
          </div>
        </div>
      </div>
    </section>
  );
}