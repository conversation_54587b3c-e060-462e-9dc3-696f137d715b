"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import FeaturedArticleSection from "@/components/resources/latest-articles/FeaturedArticleSection";
import ArticleFiltersSection from "@/components/resources/latest-articles/ArticleFiltersSection";
import LatestArticlesGridSection from "@/components/resources/latest-articles/LatestArticlesGridSection";
import TrendingTopicsSection from "@/components/resources/latest-articles/TrendingTopicsSection";
import SubscriptionCtaSection from "@/components/resources/latest-articles/SubscriptionCtaSection";

export default function LatestArticlesPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      {/* Placeholder for page title and intro */}
      <section className="py-16 md:py-20 bg-[#F2F2F2]">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
            Delivered by StreamLnk: Your Source for Cutting-Edge Industrial Trade Insights
          </h1>
          <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto">
            Stay informed and ahead of the curve. Explore our latest articles, in-depth analyses, expert interviews, and success stories covering the evolving landscape of global sourcing, logistics, technology, and sustainability.
          </p>
        </div>
      </section>

      <FeaturedArticleSection />
      <ArticleFiltersSection />
      <LatestArticlesGridSection />
      <TrendingTopicsSection />
      <SubscriptionCtaSection />

      <BottomFooter />
    </div>
  );
}