"use client"

import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ChevronRight } from "lucide-react";
import { JobCard } from "./JobCard";

// Dummy data for job listings - replace with actual data fetching
const jobListingsData = [
  {
    title: "Assembler",
    category: "Manufacturing",
    jobId: "P-100790",
    location: "Dammam, Eastern Province, Saudi Arabia",
    date: "07/03/2024",
    description: "Job Requirements. Assembler. Would you like to work with the latest products in the energy industry? Would you enjoy being part of a team that puts quality first? Join our completion and well intervention..."
  },
  {
    title: "Trainee",
    category: "Engineering/Technology",
    jobId: "P-100742",
    location: "Coimbatore, Tamil Nadu, India",
    date: "02/26/2024",
    description: "Job Requirements. Looking for candidates with Diploma with 1 year experience in. Control valves manufacturing / SS basics. Assy & testing of instruments. Preventive Maintenance check..."
  },
  {
    title: "Sales Account Manager - Wireline Services",
    category: "Sales",
    jobId: "P-100792",
    location: "[Location not specified in text, but multiple available]",
    date: "07/14/2024",
    locationsAvailable: 2,
    description: "Job Requirements. Sales Account Manager – Wireline Services. Are you looking for a new challenge? Do you enjoy Monitoring projects? Be part of a successful team. Our Team provides support for regional..."
  },
  {
    title: "Workshop Mechanic – Process & Pipeline Services",
    category: "Services",
    jobId: "P-100760",
    location: "Welshpool, Western Australia, Australia",
    date: "06/04/2024",
    description: "Job Requirements. “At StreamLnk we are always searching for great talent. While we may not have a specific job for your today, we want to know about you when we do. As actual openings become available,..."
  }
];

export function JobListings() {
  return (
    <main className="w-full md:w-2/3 lg:w-3/4">
      <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        {/* Search Bar */}
        <div className="relative flex-grow">
          <Input type="text" placeholder="Search from below list" className="w-full"/>
        </div>

        {/* Job Count & Sort */}
        <div className="flex items-center justify-between sm:justify-end gap-4 flex-shrink-0">
          <span className="text-sm text-gray-600">{jobListingsData.length} Jobs</span>
          <span className="text-sm text-gray-400">|</span>
          <Select defaultValue="most-relevant">
            <SelectTrigger className="w-[180px] h-10">
              <span className="text-sm text-gray-500 mr-1">Sort by</span>
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="most-relevant">Most relevant</SelectItem>
              <SelectItem value="date-posted">Date posted</SelectItem>
              <SelectItem value="title-az">Title (A-Z)</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Job Cards List */}
      <div className="space-y-0">
        {jobListingsData.map((job, index) => (
          <JobCard key={index} {...job} />
        ))}
      </div>

      {/* Pagination */}
      <div className="mt-8 flex items-center justify-center space-x-1">
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" disabled></Button>
        <Button variant="default" size="sm" className="h-8 w-8 p-0 bg-[#00A991] hover:bg-[#008a75] text-white">1</Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-600 hover:bg-gray-100">2</Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-600 hover:bg-gray-100">3</Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-600 hover:bg-gray-100">4</Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-600 hover:bg-gray-100">5</Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-600 hover:bg-gray-100">6</Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-600 hover:bg-gray-100">7</Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-600 hover:bg-gray-100">8</Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-600 hover:bg-gray-100">9</Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-600 hover:bg-gray-100">10</Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-600 hover:bg-gray-100">
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </main>
  );
}