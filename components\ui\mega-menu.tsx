"use client"

import * as React from "react"
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu"

import { cn } from "@/lib/utils"

const MegaMenu = DropdownMenuPrimitive.Root

const MegaMenuTrigger = DropdownMenuPrimitive.Trigger

const MegaMenuGroup = DropdownMenuPrimitive.Group

const MegaMenuPortal = DropdownMenuPrimitive.Portal

const MegaMenuContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <DropdownMenuPrimitive.Portal>
    <DropdownMenuPrimitive.Content
      ref={ref}
      sideOffset={sideOffset}
      className={cn(
        "z-50 w-screen overflow-hidden border-b bg-popover p-4 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        className
      )}
      {...props}
    />
  </DropdownMenuPrimitive.Portal>
))
MegaMenuContent.displayName = DropdownMenuPrimitive.Content.displayName

const MegaMenuSection = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    title?: string
  }
>(({ className, title, children, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5", className)}
    {...props}
  >
    {title && (
      <h3 className="text-sm font-medium leading-none tracking-tight text-foreground mb-2">
        {title}
      </h3>
    )}
    {children}
  </div>
))
MegaMenuSection.displayName = "MegaMenuSection"

const MegaMenuColumns = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("grid grid-cols-3 gap-6", className)}
    {...props}
  >
    {children}
  </div>
))
MegaMenuColumns.displayName = "MegaMenuColumns"

const MegaMenuItem = React.forwardRef<
  HTMLAnchorElement,
  React.AnchorHTMLAttributes<HTMLAnchorElement> & {
    icon?: React.ReactNode
    description?: string
  }
>(({ className, children, icon, description, onClick, ...props }, ref) => {
  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    // Stop event propagation to prevent triggering parent menu events
    e.stopPropagation();
    // Call the original onClick handler if provided
    onClick?.(e);
  };

  return (
    <a
      ref={ref}
      className={cn(
        "flex cursor-pointer items-start gap-2 rounded-md px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
        className
      )}
      onClick={handleClick}
      {...props}
    >
      {icon && <span className="flex-shrink-0">{icon}</span>}
      <div className="flex flex-col">
        <span className="font-medium">{children}</span>
        {description && (
          <span className="text-xs text-muted-foreground">{description}</span>
        )}
      </div>
    </a>
  );
})
MegaMenuItem.displayName = "MegaMenuItem"

const MegaMenuSeparator = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <DropdownMenuPrimitive.Separator
    ref={ref}
    className={cn("-mx-1 my-3 h-px bg-muted", className)}
    {...props}
  />
))
MegaMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName

const MegaMenuFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("mt-4 pt-2 border-t", className)}
    {...props}
  >
    {children}
  </div>
))
MegaMenuFooter.displayName = "MegaMenuFooter"

export {
  MegaMenu,
  MegaMenuTrigger,
  MegaMenuContent,
  MegaMenuSection,
  MegaMenuColumns,
  MegaMenuItem,
  MegaMenuSeparator,
  MegaMenuGroup,
  MegaMenuPortal,
  MegaMenuFooter,
}