"use client"

import Link from "next/link"
import { MainNav } from "@/components/main-nav"
import { BottomFooter } from "@/components/bottom-footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import HeroSection from "@/components/solutions/main-solutions/HeroSection"
import OverviewSection from "@/components/solutions/main-solutions/OverviewSection"
import CoreSolutionsSection from "@/components/solutions/main-solutions/CoreSolutionsSection"
import EcosystemIntegrationSection from "@/components/solutions/main-solutions/EcosystemIntegrationSection"
import CallToActionSection from "@/components/solutions/main-solutions/CallToActionSection"
import { ArrowRight, CheckCircle, BarChart3, Globe, ShieldCheck, Truck, CreditCard } from "lucide-react"

export default function SolutionsPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <OverviewSection />

      <CoreSolutionsSection />

      <EcosystemIntegrationSection />

      <CallToActionSection />

      <BottomFooter />
    </div>
  )
}