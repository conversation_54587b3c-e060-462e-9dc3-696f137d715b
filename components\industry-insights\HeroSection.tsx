"use client";

import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Search, BarChartBig } from "lucide-react";
import Link from "next/link";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6 lg:text-left">
              Navigate Your Industry with Deeper Insight: Intelligence by StreamLnk
            </h1>
            <p className="text-xl text-gray-700 mb-10 lg:text-left">
              Stay ahead in the dynamic world of industrial trade. Access expert analysis, data-driven market trends, best practices, and strategic insights powered by StreamLnk's global ecosystem and our data intelligence arm, StreamResources+.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 lg:justify-start">
          <Button 
            className="bg-[#004235] hover:bg-[#028475] text-white px-6 w-full sm:w-auto"
            size="lg"
            asChild
          >
            <Link href="/industry-insights/latest"> {/* Placeholder link, to be updated */}
              EXPLORE INSIGHTS
              <Search className="ml-2 h-5 w-5" />
            </Link>
          </Button>
          <Button 
            variant="outline"
            className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-6 w-full sm:w-auto"
            size="lg"
            asChild
          >
            <Link href="/solutions/streamresources"> {/* Link to StreamResources+ page */}
              DISCOVER STREAMRESOURCES
              <BarChartBig className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
      <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl mt-8 lg:mt-0">
        <Image
          src="/images/industry-insights/industry-insights-hero.webp" // Placeholder - suggest user to replace
          alt="StreamLnk Industry Insights Hero Image"
          fill
          className="object-cover"
          priority
        />
        {/* TODO: Consider adding a more relevant image or illustration for industry insights */}
      </div>
    </div>
  </div>
</section>
  );
}