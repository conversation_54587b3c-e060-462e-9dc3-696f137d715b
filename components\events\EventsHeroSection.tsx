"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { CalendarDays, Archive } from 'lucide-react';

export default function EventsHeroSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4 text-center">
        <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
          Join the Conversation: StreamLnk Events, Webinars & Industry Engagements
        </h1>
        <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto mb-10">
          Connect with the StreamLnk team, learn from industry thought leaders, explore our latest platform innovations, and discuss the future of global industrial trade. Find all our upcoming and past events here.
        </p>
        <div className="flex flex-col sm:flex-row justify-center items-center gap-4 md:gap-6">
          <Button 
            className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto text-base md:text-lg py-3 px-8"
            size="lg"
            asChild
          >
            <Link href="#upcoming-events"> {/* Link to upcoming events section on the page */}
              <CalendarDays className="mr-2 h-5 w-5" />
              VIEW UPCOMING EVENTS
            </Link>
          </Button>
          <Button 
            variant="outline"
            className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto text-base md:text-lg py-3 px-8"
            size="lg"
            asChild
          >
            <Link href="#past-events"> {/* Link to past events section on the page */}
              <Archive className="mr-2 h-5 w-5" />
              ACCESS ON-DEMAND ARCHIVES
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}