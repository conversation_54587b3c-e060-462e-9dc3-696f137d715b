import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"

export default function HeroSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4 grid md:grid-cols-2 gap-8 items-center">
        <div className="space-y-6">
          <h1 className="text-4xl md:text-5xl font-bold text-[#004235] leading-tight">
            Automate Your AR & AP: Gain Control Over Your Industrial Trade Finances
          </h1>
          <p className="text-lg text-gray-700">
            Stop chasing payments and drowning in manual invoice reconciliation. StreamLnk integrates powerful Accounts Receivable and Payable management tools directly into your trade workflow, improving cash flow and reducing administrative burdens.
          </p>
          <Button 
            className="bg-[#004235] hover:bg-[#028475] text-white px-8 mt-4"
            size="lg"
            asChild
          >
            <Link href="/request-demo">
              DISCOVER SMARTER FINANCIAL MANAGEMENT – REQUEST DEMO
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
        <div className="relative h-[400px] rounded-lg overflow-hidden shadow-xl">
          <Image 
            src="/images/solutions/ar-ap-management-hero.svg" 
            alt="AR & AP Management Dashboard" 
            fill
            className="object-cover"
            priority
          />
        </div>
      </div>
    </section>
  )
}