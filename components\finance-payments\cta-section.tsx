import { Button } from "@/components/ui/button";

interface CtaSectionProps {
  title?: string;
  description?: string;
  primaryButtonText?: string;
  secondaryButtonText?: string;
  primaryButtonHref?: string;
  secondaryButtonHref?: string;
  bgColor?: string;
  textColor?: string;
}

export default function CtaSection({
  title = "Ready to Optimize Your Financial Operations?",
  description = "Discover how StreamLnk's integrated finance and payment solutions can transform your global trade business.",
  primaryButtonText = "Schedule a Demo",
  secondaryButtonText = "Learn More",
  primaryButtonHref = "#",
  secondaryButtonHref = "#",
  bgColor = "bg-[#F2F2F2]",
  textColor = "text-[#004235]",
}: CtaSectionProps) {
  return (
    <section className={`py-16 md:py-24 ${bgColor} ${textColor}`}>
      <div className="container px-4 md:px-6 text-center">
        <h2 className="text-3xl font-bold tracking-tight mb-6">{title}</h2>
        <p className="text-xl mb-8 max-w-2xl mx-auto">{description}</p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button className={`${bgColor === "bg-[#004235]" ? "bg-white text-[#004235] hover:bg-gray-100" : bgColor === "bg-[#F2F2F2]" ? "bg-[#004235] text-white hover:bg-[#004235]/90" : "bg-[#028475] hover:bg-[#004235] text-white"}`}>
            {primaryButtonText}
          </Button>
          <Button 
            variant="outline" 
            className={`${textColor === "text-white" ? "border-white text-white hover:bg-white/10" : bgColor === "bg-[#F2F2F2]" ? "border-[#028475] text-[#028475] hover:bg-[#028475]/10" : "border-[#004235] text-[#004235] hover:bg-[#004235]/10"}`}
          >
            {secondaryButtonText}
          </Button>
        </div>
      </div>
    </section>
  );
}