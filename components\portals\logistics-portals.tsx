import { Accordion } from "@/components/ui/accordion"
import PortalItem from "@/components/portals/portal-item"
import {
  Globe,
  Building,
  Clipboard,
  Ship,
  Shield,
  FileCheck,
  FileText,
  MapPin,
  Smartphone,
  Laptop,
  Server,
} from "lucide-react"
import { FeatureCard, PlatformBadge, BenefitItem } from "@/components/portals/portal-components"
import Link from "next/link"

export default function LogisticsPortals() {
  return (
    <Accordion type="single" collapsible className="w-full space-y-6">
      <PortalItem
        id="streamglobe"
        title="STREAMGLOBE"
        subtitle="For Global Sea Freight Carriers"
        icon={<Globe className="h-8 w-8" />}
        color="#004235"
        imageSrc="/images/portals/stream-globe.webp"
        imageAlt="Sea Freight Portal"
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Portal Overview</h4>
            <p className="text-[#404040] mb-6">
              STREAMGLOBE is your integration point with StreamLnk for global sea freight operations. Our platform helps
              you manage booking requests, sync vessel schedules via API, exchange shipping documents, and provide
              real-time tracking updates for seamless global operations.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Key Features</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <FeatureCard icon={<Clipboard />} title="Booking Management">
                Efficiently manage booking requests from customers around the world
              </FeatureCard>
              <FeatureCard icon={<Ship />} title="Schedule Synchronization">
                Sync vessel schedules via API for real-time updates and coordination
              </FeatureCard>
              <FeatureCard icon={<FileText />} title="Document Exchange">
                Securely exchange shipping documents with customers and partners
              </FeatureCard>
              <FeatureCard icon={<MapPin />} title="Tracking Updates">
                Provide real-time tracking updates for all shipments in transit
              </FeatureCard>
            </div>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Available Platforms</h4>
            <div className="flex flex-wrap gap-4 mb-6">
              <PlatformBadge icon={<Laptop />} platform="Web Portal" />
              <PlatformBadge icon={<Server />} platform="API Access" />
            </div>
          </div>

          <div className="bg-gray-50 p-6 rounded-lg">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Who is this for?</h4>
            <p className="text-[#404040] mb-6">
              STREAMGLOBE is designed for global sea freight carriers who want to integrate with the StreamLnk network.
              Our platform provides the tools you need to manage bookings, schedules, documents, and tracking
              information efficiently.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Benefits</h4>
            <ul className="space-y-3 mb-6">
              <BenefitItem>Streamlined booking management</BenefitItem>
              <BenefitItem>Automated schedule synchronization</BenefitItem>
              <BenefitItem>Secure document exchange</BenefitItem>
              <BenefitItem>Real-time tracking capabilities</BenefitItem>
              <BenefitItem>Integrated communication tools</BenefitItem>
            </ul>

            <Link href="/signup?portal=streamglobe" className="block w-full">
              <button className="w-full  bg-[#004235] hover:bg-[#004235]/90 text-white py-2 px-4 rounded-md font-medium mt-4">
                Request Access
              </button>
            </Link>
          </div>
        </div>
      </PortalItem>

      <PortalItem
        id="streamglobeplus"
        title="STREAMGLOBE+"
        subtitle="For Customs Clearance Agents"
        icon={<Building className="h-8 w-8" />}
        color="#023025"
        imageSrc="/images/portals/stream-globe-plus.webp"
        imageAlt="Customs Clearance Portal"
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Portal Overview</h4>
            <p className="text-[#404040] mb-6">
              STREAMGLOBE+ provides customs clearance agents with the tools they need to manage shipments,
              documentation, and clearance processes. Our platform helps you manage assigned shipments, securely access
              documentation, submit declarations, update clearance statuses, and handle invoicing.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Key Features</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <FeatureCard icon={<Clipboard />} title="Shipment Management">
                Efficiently manage assigned shipments in one central location
              </FeatureCard>
              <FeatureCard icon={<Shield />} title="Secure Documentation">
                Securely access and manage all shipment documentation
              </FeatureCard>
              <FeatureCard icon={<FileCheck />} title="Declaration Submission">
                Submit declarations and update clearance statuses in real-time
              </FeatureCard>
              <FeatureCard icon={<FileText />} title="Invoice Handling">
                Efficiently handle invoicing for all your customs clearance services
              </FeatureCard>
            </div>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Available Platforms</h4>
            <div className="flex flex-wrap gap-4 mb-6">
              <PlatformBadge icon={<Smartphone />} platform="Mobile App" />
              <PlatformBadge icon={<Laptop />} platform="Web Portal" />
            </div>
          </div>

          <div className="bg-gray-50 p-6 rounded-lg">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Who is this for?</h4>
            <p className="text-[#404040] mb-6">
              STREAMGLOBE+ is designed for customs clearance agents who want to streamline their operations and
              integrate with the StreamLnk network. Our platform provides the tools you need to manage customs clearance
              processes efficiently.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Benefits</h4>
            <ul className="space-y-3 mb-6">
              <BenefitItem>Centralized shipment management</BenefitItem>
              <BenefitItem>Secure document access and handling</BenefitItem>
              <BenefitItem>Streamlined declaration submission</BenefitItem>
              <BenefitItem>Efficient invoice processing</BenefitItem>
              <BenefitItem>Real-time status updates</BenefitItem>
            </ul>

            <Link href="/signup?portal=streamglobe-plus" className="block w-full">
              <button className="w-full bg-[#004235] hover:bg-[#004235]/900 text-white py-2 px-4 rounded-md font-medium mt-4">
                Request Access
              </button>
            </Link>
          </div>
        </div>
      </PortalItem>


    </Accordion>
  )
}
