import type React from "react"
// Removed Image, Shield, ArrowRight, CheckCircle2, Clock, FileCheck, Users, Building2, <PERSON>rief<PERSON>, LockKeyhole from lucide-react as they are now in child components
// Removed Button, Card, CardContent as they are now in child components
// Removed MilestoneCard, BenefitCard, UserTypeCard, ActivationStep as they are now in child components

import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import HeroSection from "@/components/finance-payments/escrow-payment/hero-section"
import HowEscrowWorksSection from "@/components/finance-payments/escrow-payment/how-escrow-works-section"
import CommonMilestonesSection from "@/components/finance-payments/escrow-payment/common-milestones-section"
import WhyUseEscrowSection from "@/components/finance-payments/escrow-payment/why-use-escrow-section"
import EscrowBenefitsSection from "@/components/finance-payments/escrow-payment/escrow-benefits-section"
import WhoShouldUseEscrowSection from "@/components/finance-payments/escrow-payment/who-should-use-escrow-section"
import HowToActivateSection from "@/components/finance-payments/escrow-payment/how-to-activate-section"
import CtaSection from "@/components/finance-payments/escrow-payment/cta-section"

export default function EscrowPaymentPage() {
  return (
    <main className="flex flex-col min-h-screen">
      <MainNav />
      <HeroSection />
      <HowEscrowWorksSection />
      <CommonMilestonesSection />
      <WhyUseEscrowSection />
      <EscrowBenefitsSection />
      <WhoShouldUseEscrowSection />
      <HowToActivateSection />
      <CtaSection />
      <MainFooter />
    </main>
  )
}

// Removed Globe SVG component as it's now in EscrowBenefitsSection.tsx
