"use client"

import { useState, useEffect, useRef } from "react"
import { ChevronDown, Menu, X, Search, Bell, Globe, Phone } from "lucide-react"

interface DropdownItem {
  title: string
  description: string
  href: string
}

interface NavItem {
  label: string
  href: string
  hasDropdown?: boolean
  dropdownItems?: DropdownItem[]
}

const primaryNavigationItems: NavItem[] = [
  { label: "Prices", href: "#" },
  {
    label: "Book",
    href: "#",
    hasDropdown: true,
    dropdownItems: [
      { title: "Book a Shipment", description: "Schedule your cargo shipment", href: "#" },
      { title: "Instant Booking", description: "Quick booking for standard routes", href: "#" },
      { title: "Contract Rates", description: "Access your negotiated rates", href: "#" },
      { title: "Booking History", description: "View your previous bookings", href: "#" },
    ],
  },
  { label: "Schedules", href: "#" },
  { label: "Tracking", href: "#" },
  {
    label: "Manage",
    href: "#",
    hasDropdown: true,
    dropdownItems: [
      { title: "My Shipments", description: "Track and manage your shipments", href: "#" },
      { title: "Documentation", description: "Access shipping documents", href: "#" },
      { title: "Invoices", description: "View and download invoices", href: "#" },
      { title: "Reports", description: "Generate shipping reports", href: "#" },
    ],
  },
  {
    label: "Services",
    href: "#",
    hasDropdown: true,
    dropdownItems: [
      { title: "Ocean Freight", description: "Global container shipping", href: "#" },
      { title: "Inland Services", description: "Trucking and rail transport", href: "#" },
      { title: "Warehousing", description: "Storage and distribution", href: "#" },
      { title: "Supply Chain", description: "End-to-end logistics solutions", href: "#" },
    ],
  },
  {
    label: "Company",
    href: "#",
    hasDropdown: true,
    dropdownItems: [
      { title: "About Maersk", description: "Learn about our company", href: "#" },
      { title: "Sustainability", description: "Our environmental commitment", href: "#" },
      { title: "Careers", description: "Join our global team", href: "#" },
      { title: "News & Media", description: "Latest company updates", href: "#" },
    ],
  },
]

export default function DynamicHeader() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [scrollProgress, setScrollProgress] = useState(0)
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const headerRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY
      const threshold = 50
      const maxScroll = 100

      // Calculate scroll progress for the animation (0 to 1)
      const progress = Math.min(Math.max(scrollPosition - threshold, 0) / (maxScroll - threshold), 1)
      setScrollProgress(progress)

      // Set isScrolled state for other conditional rendering
      setIsScrolled(scrollPosition > threshold)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Determine if header should have white background
  const shouldHaveWhiteBackground = isScrolled || hoveredItem !== null

  const handleMouseEnter = (itemLabel: string, hasDropdown: boolean) => {
    if (hasDropdown) {
      setHoveredItem(itemLabel)
    }
  }

  const handleMouseLeave = () => {
    setHoveredItem(null)
  }

  return (
    <>
      <header
        ref={headerRef}
        className={`fixed top-0 left-0 right-0 z-50 ${hoveredItem !== null ? "bg-white shadow-md" : ""}`}
        onMouseLeave={handleMouseLeave}
      >
        {/* Sliding background for scroll effect */}
        {isScrolled && (
          <div
            className="absolute inset-x-0 top-0 bg-white shadow-md z-[-1]"
            style={{
              height: `${scrollProgress * 100}%`,
              transition: "height 0.3s ease-out",
            }}
          />
        )}

        {/* TOP TIER - Logo and Utility Navigation */}
        <div className="border-b border-opacity-20 border-gray-300 relative z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-12">
              {/* Logo */}
              <div className="flex-shrink-0">
                <a
                  href="#"
                  className={`text-2xl font-bold ${shouldHaveWhiteBackground ? "text-blue-600" : "text-white"}`}
                >
                  MAERSK
                </a>
              </div>

              {/* Utility Navigation */}
              <div className="hidden md:flex items-center space-x-6">
                <button
                  className={`flex items-center space-x-1 text-sm hover:opacity-80 ${
                    shouldHaveWhiteBackground ? "text-gray-700" : "text-white"
                  }`}
                >
                  <Globe className="w-4 h-4" />
                  <span>EN</span>
                  <ChevronDown className="w-3 h-3" />
                </button>

                <button
                  className={`p-1 hover:opacity-80 ${shouldHaveWhiteBackground ? "text-gray-700" : "text-white"}`}
                >
                  <Search className="w-4 h-4" />
                </button>

                <button
                  className={`p-1 hover:opacity-80 ${shouldHaveWhiteBackground ? "text-gray-700" : "text-white"}`}
                >
                  <Bell className="w-4 h-4" />
                </button>

                <a
                  href="#"
                  className={`text-sm hover:opacity-80 ${shouldHaveWhiteBackground ? "text-gray-700" : "text-white"}`}
                >
                  Support
                </a>

                <a
                  href="#"
                  className={`flex items-center space-x-1 text-sm hover:opacity-80 ${
                    shouldHaveWhiteBackground ? "text-gray-700" : "text-white"
                  }`}
                >
                  <Phone className="w-3 h-3" />
                  <span>Contact Us</span>
                </a>
              </div>

              {/* Mobile menu button */}
              <div className="md:hidden">
                <button
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  className={`p-2 rounded-md ${shouldHaveWhiteBackground ? "text-gray-900" : "text-white"}`}
                >
                  {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* BOTTOM TIER - Primary Navigation and Auth Buttons */}
        <div className="relative z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              {/* Primary Navigation */}
              <nav className="hidden md:flex items-center space-x-8">
                {primaryNavigationItems.map((item) => (
                  <div
                    key={item.label}
                    className="relative"
                    onMouseEnter={() => handleMouseEnter(item.label, item.hasDropdown || false)}
                  >
                    <a
                      href={item.href}
                      className={`flex items-center space-x-1 px-3 py-2 text-sm font-medium hover:opacity-80 ${
                        shouldHaveWhiteBackground ? "text-gray-900" : "text-white"
                      }`}
                    >
                      <span>{item.label}</span>
                      {item.hasDropdown && (
                        <ChevronDown className={`w-4 h-4 ${hoveredItem === item.label ? "rotate-180" : ""}`} />
                      )}
                    </a>
                  </div>
                ))}
              </nav>

              {/* Authentication Buttons */}
              <div className="hidden md:flex items-center space-x-4">
                <a
                  href="#"
                  className={`px-4 py-2 text-sm font-medium hover:opacity-80 ${
                    shouldHaveWhiteBackground ? "text-gray-700" : "text-white"
                  }`}
                >
                  Register
                </a>
                <a
                  href="#"
                  className={`px-4 py-2 text-sm font-medium rounded-md ${
                    shouldHaveWhiteBackground
                      ? "bg-blue-600 text-white hover:bg-blue-700"
                      : "bg-white text-gray-900 hover:bg-gray-100"
                  }`}
                >
                  Login
                </a>
              </div>
            </div>
          </div>

          {/* Dropdown Menus - Positioned below the entire header */}
          {hoveredItem && (
            <div className="absolute top-full left-0 right-0 z-40">
              {primaryNavigationItems
                .filter((item) => item.label === hoveredItem && item.dropdownItems)
                .map((item) => (
                  <div key={item.label} className="bg-white shadow-lg border-t border-gray-200">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {item.dropdownItems?.map((dropdownItem) => (
                          <a
                            key={dropdownItem.title}
                            href={dropdownItem.href}
                            className="block p-4 rounded-lg hover:bg-gray-50"
                          >
                            <div className="text-sm font-semibold text-gray-900 mb-2">{dropdownItem.title}</div>
                            <div className="text-xs text-gray-600">{dropdownItem.description}</div>
                          </a>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          )}
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden bg-white border-t border-gray-200">
            <div className="px-4 py-4 space-y-3">
              {/* Primary Navigation */}
              <div className="space-y-2">
                <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide">Navigation</h3>
                {primaryNavigationItems.map((item) => (
                  <a
                    key={item.label}
                    href={item.href}
                    className="block px-3 py-2 text-base font-medium text-gray-900 hover:bg-gray-50 rounded-md"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.label}
                  </a>
                ))}
              </div>

              {/* Utility Navigation */}
              <div className="pt-4 border-t border-gray-200 space-y-2">
                <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide">Utility</h3>
                <a href="#" className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 rounded-md">
                  Support
                </a>
                <a href="#" className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 rounded-md">
                  Contact Us
                </a>
              </div>

              {/* Authentication */}
              <div className="pt-4 border-t border-gray-200 space-y-3">
                <a
                  href="#"
                  className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 rounded-md"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Register
                </a>
                <a
                  href="#"
                  className="block px-3 py-2 text-base font-medium bg-blue-600 text-white hover:bg-blue-700 rounded-md text-center"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Login
                </a>
              </div>
            </div>
          </div>
        )}
      </header>

      {/* Demo Content */}
      <div className="relative">
        {/* Hero Section with Dark Background */}
        <section className="h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 flex items-center justify-center">
          <div className="text-center text-white px-4">
            <h1 className="text-5xl md:text-7xl font-bold mb-6">MAERSK</h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">Global Container Shipping & Logistics</p>
            <p className="text-lg mb-8 opacity-80">Scroll down to see the two-tier header transform</p>
            <div className="animate-bounce">
              <ChevronDown className="w-8 h-8 mx-auto" />
            </div>
          </div>
        </section>
      </div>
    </>
  )
}
