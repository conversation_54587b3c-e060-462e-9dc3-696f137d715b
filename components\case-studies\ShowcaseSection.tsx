"use client";

import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge"; // Assuming you have a Badge component or will create one
import { ArrowRight } from "lucide-react";

const caseStudies = [
  {
    id: "1",
    imageSrc: "/images/case-studies/placeholder-manufacturing.svg", // Placeholder - create relevant SVGs
    imageAlt: "Modern manufacturing facility",
    client: "PolymerSource Inc. (SME Manufacturer)",
    title: "SME Manufacturer Cuts Sourcing Time from Weeks to Days",
    challenge: "Limited supplier base and lengthy procurement cycles.",
    results: [
      "70% Reduction in Sourcing Time",
      "10% Average Cost Savings",
      "Access to Global Suppliers",
    ],
    tags: ["Buyer", "Polymers", "Cost Savings", "Efficiency"],
    fullStoryLink: "/case-studies/polymersource-inc", // Placeholder
  },
  {
    id: "2",
    imageSrc: "/images/case-studies/placeholder-shipping.svg", // Placeholder
    imageAlt: "Cargo ship and containers",
    client: "GlobalChem Suppliers Ltd. (International Producer)",
    title: "Polymer Producer Expands Export Reach & Optimizes Inventory",
    challenge: "Difficulty accessing new international markets and managing surplus stock.",
    results: [
      "Entered 2 New Continents",
      "$1.5M Additional Export Sales",
      "500MT Surplus Sold via Auction",
    ],
    tags: ["Supplier", "Polymers", "Market Expansion", "Inventory Management"],
    fullStoryLink: "/case-studies/globalchem-suppliers", // Placeholder
  },
  {
    id: "3",
    imageSrc: "/images/case-studies/placeholder-logistics.svg", // Placeholder
    imageAlt: "Truck on a highway",
    client: "LogiTrans Connect (Regional Freight Carrier)",
    title: "Freight Carrier Increases Load Volume & Reduces Empty Miles",
    challenge: "Inconsistent job flow and inefficient routing.",
    results: [
      "18% Increase in Monthly Loads",
      "10% Reduction in Empty Miles",
      "Faster Payment Cycles",
    ],
    tags: ["Freight Partner", "Logistics", "Efficiency"],
    fullStoryLink: "/case-studies/logitrans-connect", // Placeholder
  },
  // Add more case studies as they are developed
];

export default function ShowcaseSection() {
  // TODO: Implement filtering logic based on props from FilterSection
  const filteredCaseStudies = caseStudies;

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-[#004235] mb-12 text-center">
          Case Study Showcase
        </h2>

        {filteredCaseStudies.length === 0 ? (
          <p className="text-center text-gray-600 text-lg">
            No case studies match your current filter criteria. Please adjust your filters or check back later.
          </p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredCaseStudies.map((study) => (
              <div
                key={study.id}
                className="bg-[#f3f4f6] rounded-lg shadow-lg overflow-hidden flex flex-col border border-gray-200 hover:shadow-xl transition-shadow duration-300"
              >
                <div className="relative w-full h-56">
                  <Image
                    src={study.imageSrc}
                    alt={study.imageAlt}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="p-6 flex flex-col flex-grow">
                  <p className="text-sm text-[#028475] font-semibold mb-1">
                    {study.client}
                  </p>
                  <h3 className="text-xl font-bold text-[#004235] mb-2">
                    {study.title}
                  </h3>
                  <p className="text-sm text-gray-600 mb-3">
                    <strong className='text-gray-700'>Challenge:</strong> {study.challenge}
                  </p>
                  <div className="mb-4">
                    <p className="text-sm font-semibold text-gray-700 mb-1">Key Results:</p>
                    <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                      {study.results.map((result, i) => (
                        <li key={i}>{result}</li>
                      ))}
                    </ul>
                  </div>
                  <div className="mb-4 flex flex-wrap gap-2">
                    {study.tags.map((tag, i) => (
                      <Badge key={i} variant="secondary" className="bg-[#004235] text-white hover:bg-[#028475]">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  <div className="mt-auto">
                    <Button
                      variant="outline"
                      className="w-full border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
                      asChild
                    >
                      <Link href={study.fullStoryLink}>
                        READ FULL CASE STUDY
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Placeholder for Pagination if many case studies */}
        {/* <div className="mt-12 flex justify-center">
          <Button variant="outline" className="border-[#004235] text-[#004235]">Load More</Button>
        </div> */}
      </div>
    </section>
  );
}