"use client";

import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import * as Icons from "lucide-react";
import { Button } from "./ui/button";
import clsx from "clsx";

// Data Structure
interface NavItemBase {
  href: string;
  label: string;
  icon?: React.ReactElement;
}

const customerPortalLinks: NavItemBase[] = [
  { href: "/login?portal=mystreamlnk", label: "MyStreamLnk" },
  { href: "/login?portal=mystreamlnk-plus", label: "MyStreamLnk+" },
  { href: "/login?portal=e-stream", label: "E-Stream" },
  { href: "/login?portal=streampak", label: "StreamPak" },
  { href: "/login?portal=streamglobe", label: "StreamGlobe" },
  { href: "/login?portal=streamglobe-plus", label: "StreamGlobe+" },
  { href: "/login?portal=streamfreight", label: "StreamFreight" },
  { href: "/login?portal=streamresources-plus", label: "StreamResources+" },
];

// Customer Portal Logins Dropdown Component
export function CustomerPortalLogins({ variant = "subnav", className }: { variant?: "header" | "subnav", className?: string }) {
  const isHeader = variant === "header";

  return (
    <div className="hidden md:flex items-center h-full"> {/* Hides on mobile, aligns items, ensures full height */}
      <DropdownMenu>
        <DropdownMenuTrigger
          className={clsx(
            "relative flex items-center gap-1 text-sm font-medium transition-colors py-5 outline-none focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-primary h-full group",
            // Underline setup - exactly matching main-nav styling
            "after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[3px] after:w-full after:bg-primary after:transition-transform after:duration-300",
            // Underline visibility based on hover and open state
            "after:scale-x-0 hover:after:scale-x-100 data-[state=open]:after:scale-x-100",
            // Text and underline colors based on variant
            isHeader
              ? "text-white after:bg-white hover:text-white/80 data-[state=open]:text-white/80"
              : "text-foreground after:bg-primary hover:text-primary data-[state=open]:text-primary px-4 border-l border-border",
            className
          )}
        >
          {isHeader ? (
            <>
              <Icons.User className="h-5 w-5" />
              <Icons.ChevronDown className="h-4 w-4 opacity-70 transition-transform duration-200 group-data-[state=open]:rotate-180 ml-1" />
            </>
          ) : (
            <>
              <span>Portal Logins</span>
              <Icons.ChevronDown className="h-4 w-4 opacity-70 transition-transform duration-200 group-data-[state=open]:rotate-180 ml-1" />
            </>
          )}
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end" className="p-0 bg-[#f5f5f0] w-[350px] shadow-md rounded-md border border-border z-50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2">
          <div className="py-2">
            <h3 className="px-4 py-2 text-lg font-semibold">Our Portals</h3>

            <div className="flex flex-col">
              {customerPortalLinks.map(link => (
                <Link
                  key={link.label}
                  href={link.href}
                  className="flex items-center justify-between px-4 py-3 hover:bg-accent hover:text-accent-foreground transition-colors duration-200 border-t border-border/30 group"
                >
                  <span>{link.label}</span>
                  <Icons.ExternalLink className="h-4 w-4 text-primary transition-transform duration-200 group-hover:translate-x-0.5 flex-shrink-0" />
                </Link>
              ))}
            </div>

            <div className="p-4 pt-2 space-y-2">
              <Link href="/login" className="block w-full">
                <Button className="w-full bg-primary hover:bg-primary/90 text-primary-foreground py-2 rounded transition-colors duration-200">
                  Sign In to Portal
                </Button>
              </Link>
              <Link href="/signup" className="block text-center text-sm text-primary hover:underline mt-2">
                Create Account
              </Link>
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}