import type { Metadata } from "next";
import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";
import { HeroSection } from "@/components/tech-integrations/hero-section";
import { ValuePropSection } from "@/components/tech-integrations/value-prop-section";
import { ApproachSection } from "@/components/tech-integrations/approach-section";
import { WhyPartnerSection } from "@/components/tech-integrations/why-partner-section";
import { GetStartedSection } from "@/components/tech-integrations/get-started-section";
import { CtaSection } from "@/components/tech-integrations/cta-section";

export const metadata: Metadata = {
  title: "Technology Integrations with StreamLnk | StreamLnk",
  description:
    "Extend your reach and enhance your offering by integrating your technology solutions with the StreamLnk ecosystem. Explore API integration opportunities.",
};

export default function TechIntegrationsPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main>
        <HeroSection />
        <ValuePropSection />
        <ApproachSection />
        <WhyPartnerSection />
        <GetStartedSection />
        <CtaSection />
      </main>
      <MainFooter />
    </div>
  );
}