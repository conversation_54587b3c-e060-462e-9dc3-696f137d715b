import { CheckCircle } from "lucide-react";

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            How Do You Truly Know Who to Trust in B2B Trade?
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Choosing the right suppliers, buyers, or logistics partners is critical, but traditional vetting is often time-consuming, subjective, and based on limited information. Businesses struggle with:
          </p>

          <div className="space-y-4 max-w-3xl mx-auto">
            {[ "Difficulty in objectively assessing the past performance and reliability of potential partners.", "Lack of a standardized measure for compliance adherence across different entities.", "Uncertainty about the financial trustworthiness of new counterparties.", "Time wasted on due diligence that may not reveal underlying operational risks.", "Exposure to unreliable partners leading to costly delays, quality issues, or payment defaults." ].map((challenge, index) => (
              <div key={index} className="flex items-start p-4 bg-gray-50 rounded-lg">
                <CheckCircle className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
                <p className="text-gray-800">{challenge}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}