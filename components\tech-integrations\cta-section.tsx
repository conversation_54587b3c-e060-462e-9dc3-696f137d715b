import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowRight } from 'lucide-react';

export function CtaSection() {
  return (
    <section className="py-16 bg-white"> {/* Changed background to white, standardized padding */}
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center"> {/* Added standard wrapper */}
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6"> {/* Text color to primary */}
            Connect Your Tech?
          </h2>
          <p className="text-xl text-gray-700 mb-8"> {/* Text color to gray-700 */}
            Join us in building the future of industrial trade. Explore developer resources or reach out to discuss integration ideas.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto" // Standardized primary button
              asChild
            >
              <Link href="#developer-portal-final-cta"> 
                VISIT DEV PORTAL
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto" // Standardized outline button
              asChild
            >
              <Link href="#propose-integration-final-cta">PROPOSE INTEGRATION</Link>
            </Button>
          </div>
          {/* No link button in this original component, so not adding one to keep it closer to original intent while standardizing existing elements */}
        </div>
      </div>
    </section>
  );
}