"use client"

import { useState, useEffect, Suspense } from "react"
import { Globe } from "@/components/globe"
import { InformationPill } from "@/components/information-pill"

// Enhanced sample data structure for regions
const pillData = {
  US: {
    title: "United States",
    documentTypes: ["Passport", "Driver License", "Social Security Card", "Birth Certificate", "Visa"],
  },
  CA: {
    title: "Canada",
    documentTypes: ["Passport", "Health Card", "Driver License", "Birth Certificate"],
  },
  GB: {
    title: "United Kingdom",
    documentTypes: ["Passport", "Driving Licence", "National Insurance", "Birth Certificate"],
  },
  DE: {
    title: "Germany",
    documentTypes: ["Passport", "Personalausweis", "Driver License", "Birth Certificate"],
  },
  FR: {
    title: "France",
    documentTypes: ["Passport", "Carte d'identité", "Permis de conduire", "Birth Certificate"],
  },
  JP: {
    title: "Japan",
    documentTypes: ["Passport", "Driver License", "Residence Card", "My Number Card"],
  },
  AU: {
    title: "Australia",
    documentTypes: ["Passport", "Driver License", "Medicare Card", "Birth Certificate"],
  },
  BR: {
    title: "Brazil",
    documentTypes: ["Passport", "CPF", "Driver License", "Birth Certificate"],
  },
  IN: {
    title: "India",
    documentTypes: ["Passport", "Aadhaar Card", "Driver License", "PAN Card"],
  },
  CN: {
    title: "China",
    documentTypes: ["Passport", "National ID Card", "Driver License", "Hukou"],
  },
  RU: {
    title: "Russia",
    documentTypes: ["Passport", "Internal Passport", "Driver License", "SNILS"],
  },
  ZA: {
    title: "South Africa",
    documentTypes: ["Passport", "Smart ID Card", "Driver License", "Birth Certificate"],
  },
}

// Enhanced icons for document types
const icons = {
  Passport: "🛂",
  "Driver License": "🚗",
  "Driving Licence": "🚗",
  "Social Security Card": "🏛️",
  "Birth Certificate": "📋",
  Visa: "✈️",
  "Health Card": "🏥",
  "National Insurance": "🏛️",
  Personalausweis: "🆔",
  "Carte d'identité": "🆔",
  "Permis de conduire": "🚗",
  "Residence Card": "🏠",
  "My Number Card": "🔢",
  "Medicare Card": "🏥",
  CPF: "🆔",
  "Aadhaar Card": "🆔",
  "PAN Card": "💳",
  "National ID Card": "🆔",
  Hukou: "📄",
  "Internal Passport": "📘",
  SNILS: "🏛️",
  "Smart ID Card": "💳",
}

function LoadingSpinner() {
  return (
    <div className="flex items-center justify-center h-screen bg-gradient-to-b from-slate-900 via-slate-800 to-black">
      <div className="text-white text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
        <p>Loading Earth...</p>
      </div>
    </div>
  )
}

export default function InteractiveGlobePage() {
  const [selectedRegion, setSelectedRegion] = useState<string | null>(null)
  const [pillPosition, setPillPosition] = useState({ x: 0, y: 0 })
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 })
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)

    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      })
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  const handleRegionSelect = (countryCode: string, position: { x: number; y: number }) => {
    setSelectedRegion(countryCode)
    setPillPosition(position)
  }

  const handleGlobeClick = () => {
    setSelectedRegion(null)
  }

  const selectedData = selectedRegion ? pillData[selectedRegion as keyof typeof pillData] : null

  if (!isClient) {
    return <LoadingSpinner />
  }

  return (
    <div className="relative w-full h-screen bg-gradient-to-b from-slate-900 via-slate-800 to-black overflow-hidden">
      {/* Globe Container */}
      <div className="globe-parent transition-all duration-200 ease-out">
        <div className="h-[69vmin] overflow-hidden relative">
          <Suspense fallback={<LoadingSpinner />}>
            <Globe
              onRegionSelect={handleRegionSelect}
              onGlobeClick={handleGlobeClick}
              selectedRegion={selectedRegion}
            />
          </Suspense>
        </div>
      </div>

      {/* Information Pill */}
      {selectedRegion && selectedData && (
        <InformationPill
          title={selectedData.title}
          documentTypes={selectedData.documentTypes}
          icons={icons}
          position={pillPosition}
          onClose={() => setSelectedRegion(null)}
        />
      )}

      {/* Enhanced Instructions */}
      <div className="absolute top-4 left-4 text-white/90 text-sm bg-black/20 backdrop-blur-sm rounded-lg p-3">
        <p className="font-medium">🌍 Interactive Earth Globe</p>
        <p className="text-white/70 text-xs mt-1">Click on a country to view supported document types</p>
      </div>

      {/* Globe info */}
      <div className="absolute bottom-4 right-4 text-white/70 text-xs bg-black/20 backdrop-blur-sm rounded-lg p-2">
        <p>Drag to rotate • Scroll to zoom</p>
      </div>
    </div>
  )
}
