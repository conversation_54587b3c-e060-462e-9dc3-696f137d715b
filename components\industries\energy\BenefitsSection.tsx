"use client"

import { CheckCircle } from "lucide-react";

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Optimize Your Energy Trade, Enhance Compliance, Navigate Volatility
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
              Benefits for the Energy Industry
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-start mb-4">
                  <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-[#004235] text-lg">Improved Price Discovery & Hedging</h3>
                    <p className="text-gray-600">Leverage StreamIndex™ for better insights into volatile energy markets.</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-start mb-4">
                  <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-[#004235] text-lg">Efficient Global Sourcing & Sales</h3>
                    <p className="text-gray-600">Connect with a wider network of energy buyers and suppliers.</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-start mb-4">
                  <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-[#004235] text-lg">Streamlined & Secure Logistics</h3>
                    <p className="text-gray-600">Manage complex bulk energy transport and project cargo with greater ease.</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-start mb-4">
                  <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-[#004235] text-lg">Enhanced Regulatory & ESG Compliance</h3>
                    <p className="text-gray-600">Navigate international energy regulations and track sustainability metrics.</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-start mb-4">
                  <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-[#004235] text-lg">Reduced Counterparty Risk</h3>
                    <p className="text-gray-600">Utilize iScore™ and secure payment mechanisms for high-value energy transactions.</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-start mb-4">
                  <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-[#004235] text-lg">Data-Driven Strategic Planning</h3>
                    <p className="text-gray-600">Make informed decisions based on real-time market intelligence and forecasts.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
  );
}