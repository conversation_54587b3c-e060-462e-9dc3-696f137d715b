"use client";

import { <PERSON><PERSON>, <PERSON>ren<PERSON><PERSON>p, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react';

export default function InventorySection() {
  return (
    <section className="py-12 md:py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">
            Data-Driven Inventory Management & Forecasting
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center leading-relaxed">
            Balancing inventory levels is a critical challenge; overstocking ties up valuable capital, while understocking can lead to costly production stoppages and missed sales. Traditional forecasting methods often fail to accurately predict dynamic market shifts.
          </p>

          <div className="bg-[#f3f4f6] p-8 rounded-xl shadow-lg">
            <h3 className="text-2xl font-semibold text-[#004235] mb-3">
              StreamLnk Solution: Predictive Inventory Control
            </h3>
            <p className="text-gray-700 mb-6 leading-relaxed">
              StreamLnk combines real-time inventory visibility across your network (via E-Stream and StreamPak) with sophisticated AI-powered demand and supply forecasting (available in StreamResources+). This powerful combination allows businesses to anticipate market shifts, predict potential constraints, and proactively optimize stocking levels for maximum efficiency.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="flex items-start">
                <Coins className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Lower Holding Costs</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Minimize capital tied up in excess inventory.</p>
                </div>
              </div>
              <div className="flex items-start">
                <Warehouse className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Reduced Stockouts</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Ensure material availability and prevent disruptions.</p>
                </div>
              </div>
              <div className="flex items-start">
                <TrendingUp className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Improved Working Capital</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Optimize cash flow by efficient inventory management.</p>
                </div>
              </div>
              <div className="flex items-start">
                <PieChart className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Accurate Planning</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Leverage AI for precise demand and supply forecasts.</p>
                </div>
              </div>
            </div>
            <p className="text-md text-gray-800 font-medium leading-relaxed">
              Benefits include significantly lower inventory holding costs, a marked reduction in stockouts and associated disruptions, improved working capital efficiency, and more accurate, data-driven planning capabilities.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}