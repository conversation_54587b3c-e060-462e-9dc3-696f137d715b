import { Beaker, FileCheck, Factory, HardHat, Leaf, ShieldCheck, Truck } from "lucide-react";

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Facing Complexity in Chemical Sourcing and Distribution?
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            The industrial chemical supply chain is characterized by stringent regulatory requirements, specialized handling needs, and volatile market dynamics:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <ShieldCheck className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-[#004235] text-lg mb-2">Strict Regulatory Compliance</h3>
                <p className="text-gray-700">Adherence to REACH, GHS, TSCA, and country-specific chemical regulations is paramount and complex.</p>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <HardHat className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-[#004235] text-lg mb-2">Safety & Handling</h3>
                <p className="text-gray-700">Managing MSDS, specialized transport (hazmat), and storage requirements for diverse chemical products.</p>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <FileCheck className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-[#004235] text-lg mb-2">Supplier & Product Verification</h3>
                <p className="text-gray-700">Ensuring chemical purity, supplier certifications, and chain of custody.</p>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <Leaf className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-[#004235] text-lg mb-2">Price Volatility & Opacity</h3>
                <p className="text-gray-700">Difficulty in tracking real-time prices for various chemical grades and feedstocks.</p>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <Beaker className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-[#004235] text-lg mb-2">Global Sourcing Challenges</h3>
                <p className="text-gray-700">Finding reliable international suppliers for specific intermediates or specialty chemicals.</p>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <div className="bg-[#004235] p-3 rounded-full mr-4 flex-shrink-0">
                <Truck className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-[#004235] text-lg mb-2">Complex Logistics</h3>
                <p className="text-gray-700">Coordinating multi-modal transport, often involving tankers, ISO tanks, or specialized packaging.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}