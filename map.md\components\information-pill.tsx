"use client"

import { useEffect, useState } from "react"
import { X } from "lucide-react"

interface InformationPillProps {
  title: string
  documentTypes: string[]
  icons: Record<string, string>
  position: { x: number; y: number }
  onClose: () => void
}

export function InformationPill({ title, documentTypes, icons, position, onClose }: InformationPillProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024)
    }

    checkMobile()
    window.addEventListener("resize", checkMobile)

    // Trigger animation
    const timer = setTimeout(() => setIsVisible(true), 50)

    return () => {
      window.removeEventListener("resize", checkMobile)
      clearTimeout(timer)
    }
  }, [])

  const pillStyle = {
    left: isMobile ? "50%" : `${position.x}px`,
    top: isMobile ? "50%" : `${Math.max(100, position.y - 150)}px`,
    transform: isMobile ? "translate(-50%, -50%)" : "translateX(-100%)",
  }

  return (
    <div
      className={`pill-parent fixed z-50 transition-all duration-200 ${
        isVisible ? "opacity-100" : "opacity-0 scale-95"
      }`}
      style={pillStyle}
    >
      {/* Large Screen Layout */}
      <div className="hidden lg:flex items-center">
        {/* Arrow */}
        <div className="w-0 h-0 border-t-[12px] border-b-[12px] border-l-[20px] border-t-transparent border-b-transparent border-l-white mr-2"></div>

        {/* Pill Content */}
        <div className="bg-white rounded-2xl shadow-2xl p-6 min-w-[320px] max-w-[400px]">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600 transition-colors">
              <X size={20} />
            </button>
          </div>

          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700 mb-3">Supported Document Types:</p>
            <div className="space-y-2">
              {documentTypes.map((type, index) => (
                <div key={index} className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50">
                  <span className="text-lg">{icons[type] || "📄"}</span>
                  <span className="text-sm text-gray-700">{type}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="lg:hidden flex flex-col items-center">
        {/* Pill Content */}
        <div className="bg-white rounded-2xl shadow-2xl p-4 w-[90vw] max-w-[350px] max-h-[70vh] flex flex-col">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600 transition-colors">
              <X size={20} />
            </button>
          </div>

          <div className="flex-1 overflow-hidden">
            <p className="text-sm font-medium text-gray-700 mb-3">Supported Document Types:</p>
            <div className="custom-scroll overflow-y-auto max-h-[200px] space-y-2 pr-2">
              {documentTypes.map((type, index) => (
                <div key={index} className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50">
                  <span className="text-lg">{icons[type] || "📄"}</span>
                  <span className="text-sm text-gray-700">{type}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Arrow */}
        <div className="w-0 h-0 border-l-[12px] border-r-[12px] border-t-[20px] border-l-transparent border-r-transparent border-t-white mt-2"></div>
      </div>
    </div>
  )
}
