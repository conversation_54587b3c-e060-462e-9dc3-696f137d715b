import type { Metada<PERSON> } from "next";
import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";

import HeroSection from "@/components/get-started/transparency-trust/hero-section";
import CriticalNeedSection from "@/components/get-started/transparency-trust/critical-need-section";
import CommitmentSection from "@/components/get-started/transparency-trust/commitment-section";
import GettingStartedGuideSection from "@/components/get-started/transparency-trust/getting-started-guide-section";
import BenefitsSection from "@/components/get-started/transparency-trust/benefits-section";
import CtaSection from "@/components/get-started/transparency-trust/cta-section";

export const metadata: Metadata = {
  title: "Get Started with StreamLnk: Built on Transparency & Trust | StreamLnk",
  description:
    "Learn how StreamLnk's commitment to transparency and trust provides a secure platform for global B2B trade. Start your journey with confidence.",
};

export default function TransparencyTrustPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <MainNav />
      <main>
        <HeroSection />
        <CriticalNeedSection />
        <CommitmentSection />
        <GettingStartedGuideSection />
        <BenefitsSection />
        <CtaSection />
      </main>
      <MainFooter />
    </div>
  );
}