"use client"

import { <PERSON><PERSON><PERSON>, CheckCircle } from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function EnergyTransitionSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Facilitating the Shift to a Sustainable Energy Future
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Focus on the Energy Transition
          </p>

          <div className="bg-[#F2F2F2] p-8 rounded-lg">
            <p className="text-gray-700 mb-6">StreamLnk is committed to supporting the global energy transition by:</p>

            <div className="space-y-4">
              {[
                  "Providing a dedicated marketplace for renewable energy materials and credits (solar components, wind turbine materials, RECs, carbon offsets).",
                  "Enabling transparent sourcing of low-carbon fuels and feedstocks.",
                  "Offering ESG tracking tools to help businesses measure and report the carbon intensity of their energy supply chains.",
                  "Connecting innovators in green energy technology with global buyers and investors."
                ].map((point, index) => (
                  <div key={index} className="flex items-start">
                    <div className="bg-[#028475]/10 p-2 rounded-full mr-4 mt-1">
                      <CheckCircle className="h-5 w-5 text-[#028475]" />
                    </div>
                    <p className="text-gray-700">{point}</p>
                  </div>
                ))}
            </div>

            <div className="mt-8">
              <Button
                variant="outline"
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
                asChild
              >
                <Link href="/solutions/renewable-energy">
                  Learn More About Our Renewable Energy Solutions
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}