"use client";

import { CheckCircle, Globe, Settings, Zap, BarChart, <PERSON>hake, Map } from 'lucide-react';

const criteria = [
  { 
    icon: <Globe className="h-7 w-7 text-[#028475]" />,
    text: "Established global or significant regional ocean freight carriers and NVOCCs."
  },
  { 
    icon: <Settings className="h-7 w-7 text-[#028475]" />,
    text: "Robust API capabilities or a clear roadmap for API development."
  },
  { 
    icon: <Zap className="h-7 w-7 text-[#028475]" />,
    text: "Commitment to data accuracy and timely updates for schedules and tracking."
  },
  { 
    icon: <BarChart className="h-7 w-7 text-[#028475]" />,
    text: "Strong operational reliability and adherence to industry best practices."
  },
  { 
    icon: <Handshake className="h-7 w-7 text-[#028475]" />,
    text: "Willingness to collaborate on technical integration and process optimization."
  },
  { 
    icon: <Map className="h-7 w-7 text-[#028475]" />,
    text: "Coverage of key trade lanes relevant to industrial material flows."
  }
];

export default function PartnerCriteriaSection() {
  return (
    <section className="py-12 md:py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-[#004235] mb-4">
              Building a Network of Reliable Global Maritime Logistics
            </h2>
            <p className="text-lg text-[#028475] font-semibold">
              What We Look For in Sea Freight Carrier Partners
            </p>
            <div className="w-20 h-1 bg-[#028475] mx-auto mt-2"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
            {criteria.map((item, index) => (
              <div key={index} className="flex items-start p-4 bg-slate-50 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
                <div className="flex-shrink-0 mr-4 mt-1">
                  {item.icon}
                </div>
                <p className="text-gray-700 leading-relaxed">{item.text}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}