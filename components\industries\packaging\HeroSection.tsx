"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Innovating Packaging Supply Chains
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              From raw material resins for converters to finished packaging components for CPGs, StreamLnk provides an integrated digital platform to optimize procurement, logistics, and compliance in the dynamic packaging sector.
            </p>
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                Explore Solutions
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/industries/packaging/hero-placeholder.jpg" // Placeholder image - replace with actual image
              alt="StreamLnk Integrated Platform for the Packaging Sector"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}