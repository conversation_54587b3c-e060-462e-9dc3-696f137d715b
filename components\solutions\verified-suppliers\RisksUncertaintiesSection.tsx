"use client"

import { <PERSON>, ShieldCheck, FileCheck, Users } from "lucide-react"

export function RisksUncertaintiesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Navigating the Maze of Global Sourcing: Risks & Uncertainties
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Finding reliable, trustworthy suppliers in the global market can be fraught with challenges and expose your business to risk:
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <div className="bg-[#004235]/10 p-2 rounded-full flex items-center justify-center mr-4">
                  <Search className="text-[#028475] h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Time-Consuming Vetting</h3>
                  <p className="text-gray-600">Spending excessive resources verifying supplier credentials, certifications, and financial stability.</p>
                </div>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <div className="bg-[#004235]/10 p-2 rounded-full flex items-center justify-center mr-4">
                  <ShieldCheck className="text-[#028475] h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Quality Assurance Concerns</h3>
                  <p className="text-gray-600">Uncertainty about product quality and consistency from new or unproven suppliers.</p>
                </div>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <div className="bg-[#004235]/10 p-2 rounded-full flex items-center justify-center mr-4">
                  <FileCheck className="text-[#028475] h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Compliance Risks</h3>
                  <p className="text-gray-600">Difficulty ensuring suppliers meet complex international and regional regulatory standards (REACH, ISO, FDA, ESG).</p>
                </div>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <div className="bg-[#004235]/10 p-2 rounded-full flex items-center justify-center mr-4">
                  <Users className="text-[#028475] h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Limited Access</h3>
                  <p className="text-gray-600">Difficulty finding specialized suppliers or those in new geographic regions.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}