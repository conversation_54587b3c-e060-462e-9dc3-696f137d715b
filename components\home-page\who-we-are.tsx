import Image from "next/image"

export function WhoWeAre() {
  return (
    <div className="bg-white mb-16 md:mb-24">
      {/* Hero Section */}
      <section className="py-24 md:py-32">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <p className="text-[#18b793] text-sm font-semibold uppercase tracking-wider">Next-Generation Energy Platform</p>
              <h1 className="text-4xl lg:text-5xl font-bold text-[#004235] leading-tight">
                Revolutionizing Global Energy Trading
              </h1>
              <p className="text-gray-600 text-lg leading-relaxed">
                StreamLnk is the world's most advanced digital energy trading platform, connecting suppliers and buyers across the globe. We leverage cutting-edge technology to create a transparent, secure, and efficient marketplace that transforms how energy products are sourced, traded, and delivered worldwide.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-[#18b793] rounded-full"></div>
                  <span className="text-gray-600">Real-time market data</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-[#18b793] rounded-full"></div>
                  <span className="text-gray-600">Secure transactions</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-[#18b793] rounded-full"></div>
                  <span className="text-gray-600">Global network</span>
                </div>
              </div>
            </div>
            <div className="relative">
              {/* Main grid container with enhanced styling */}
              <div className="grid grid-cols-3 gap-4 h-[500px]">
                {/* Large left image spanning full height */}
                <div className="col-span-2 relative overflow-hidden rounded-2xl shadow-2xl border border-gray-200">
                  <Image
                    src="/images/homepage/Global Energy Network Visualization.png"
                    alt="Global energy network visualization"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#004235]/20 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <p className="text-sm font-semibold">Global Network</p>
                  </div>
                </div>

                {/* Right column with two stacked images */}
                <div className="flex flex-col gap-4">
                  {/* Top right image */}
                  <div className="flex-1 relative overflow-hidden rounded-xl shadow-xl border border-gray-200">
                    <Image
                      src="/images/homepage/Dynamic Digital Trading Dashboard.png"
                      alt="Dynamic digital trading dashboard"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-br from-[#18b793]/10 to-transparent"></div>
                    <div className="absolute bottom-2 left-2 text-white">
                      <p className="text-xs font-medium">Live Trading</p>
                    </div>
                  </div>

                  {/* Bottom right image */}
                  <div className="flex-1 relative overflow-hidden rounded-xl shadow-xl border border-gray-200">
                    <Image
                      src="/images/homepage/Integrated Energy Logistics & Efficiency.png"
                      alt="Integrated energy logistics and efficiency"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-tl from-[#004235]/10 to-transparent"></div>
                    <div className="absolute bottom-2 left-2 text-white">
                      <p className="text-xs font-medium">Smart Logistics</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

