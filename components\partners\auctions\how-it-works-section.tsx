import { ClipboardList, <PERSON>, Rocket, Users, CheckCircle, Truck } from "lucide-react";
import { Timeline } from "@/components/ui/timeline"; // Assuming timeline.tsx is in components/ui

import { StandardizedTimeline } from "@/components/ui/standardized-timeline";

export default function HowItWorksSection() {
  const auctionSteps = [
    {
      title: "Supplier Listing",
      description:
        'A supplier (via E-Stream) lists inventory as "auction-eligible," defining product details, quantity, and pricing rules (e.g., starting bid, reserve price).',
      icon: <ClipboardList className="h-8 w-8 text-white" />,
    },
    {
      title: "AI-Powered Scheduling",
      description:
        "StreamLnk's AI can forecast optimal timing for auctions based on seasonal demand, market conditions, or end-of-quarter/year considerations.",
      icon: <Brain className="h-8 w-8 text-white" />,
    },
    {
      title: "Auction Launch",
      description: "Auctions are launched, typically segmented by country, product group, or other relevant criteria.",
      icon: <Rocket className="h-8 w-8 text-white" />,
    },
    {
      title: "Buyer Bidding",
      description: "Buyers (via MyStreamLnk) participate by placing bids in real-time or setting up auto-bid parameters.",
      icon: <Users className="h-8 w-8 text-white" />,
    },
    {
      title: "Auction Conclusion & Order Finalization",
      description:
        "Once the auction concludes, winning bids are processed, and orders are automatically finalized. The supplier receives the Purchase Order.",
      icon: <CheckCircle className="h-8 w-8 text-white" />,
    },
    {
      title: "Automated Fulfillment",
      description:
        "Downstream processes like freight (StreamFreight), customs (StreamGlobe), and packaging (StreamPak) are auto-initiated as needed through integrated partner portals.",
      icon: <Truck className="h-8 w-8 text-white" />,
    },
  ];

  return (
    <StandardizedTimeline
      title="How StreamLnk Auctions Work"
      description="Our streamlined process ensures efficiency and transparency for all participants."
      steps={auctionSteps}
      bgColor="bg-[#F2F2F2]"
    />
  );
}
