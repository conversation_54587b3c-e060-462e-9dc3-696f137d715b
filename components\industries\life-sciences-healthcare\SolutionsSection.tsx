"use client"

import { <PERSON><PERSON><PERSON>, Zap, ShieldCheck, FileText, Link2, BarChart3 } from 'lucide-react';

const solutions = [
  {
    icon: Briefcase,
    title: "E-Stream for Specialized Chemical & Material Suppliers",
    points: [
      "List high-purity chemicals, APIs, excipients, medical-grade polymers, and other life science materials with detailed specifications, CoA (Certificates of Analysis), and relevant regulatory compliance documentation (e.g., GMP certificates).",
      "Manage batch traceability information and expiry dates for sensitive products."
    ]
  },
  {
    icon: Zap,
    title: "MyStreamLnk for Pharmaceutical, Biotech & Medical Device Companies (Buyers)",
    points: [
      "Source critical raw materials and components from a global network of pre-vetted suppliers with proven track records in the healthcare sector (validated by iScore™).",
      "Advanced search filters for specific purity grades, regulatory certifications (FDA, EMA approved sources), and temperature handling capabilities.",
      "Secure RFQ and order management with robust document control for audit trails."
    ]
  },
  {
    icon: ShieldCheck, 
    title: "Integrated Cold Chain Logistics (StreamFreight & StreamGlobe)",
    points: [
      "Connect with specialized freight carriers and logistics providers experienced in temperature-controlled shipping (refrigerated transport, validated packaging solutions, real-time temperature monitoring data feeds - future integration).",
      "Priority booking and real-time tracking for time-sensitive and temperature-sensitive shipments."
    ]
  },
  {
    icon: FileText,
    title: "Enhanced Compliance & Document Management",
    points: [
      "Centralized digital vault for all regulatory documents, CoAs, import/export permits, and GDP compliance records.",
      "Automated alerts for expiring certifications for both products and partners.",
      "Workflow tools to manage quality assurance documentation and deviation reporting."
    ]
  },
  {
    icon: Link2,
    title: "Traceability & Chain of Custody (Future Blockchain Integration)",
    points: [
      "Exploring blockchain solutions to provide an immutable record of material provenance and handling across the supply chain, crucial for combating counterfeiting and ensuring product integrity."
    ]
  },
  {
    icon: BarChart3,
    title: "StreamResources+ for Life Science Market Intelligence",
    points: [
      "Insights into pricing and availability trends for key pharmaceutical ingredients and medical materials.",
      "Analysis of regulatory changes impacting global healthcare supply chains."
    ]
  }
];

export default function SolutionsSection() {
  return (
    <section className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Your Compliant Digital Ecosystem for a Healthier Global Supply Chain
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            StreamLnk's Tailored Solutions for Life Sciences & Healthcare
          </p>
        </div>
        <div className="grid md:grid-cols-2 gap-8 lg:gap-10">
          {solutions.map((solution, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 flex flex-col">
              <div className="flex items-center mb-4">
                <solution.icon className="h-8 w-8 text-[#028475] mr-4 flex-shrink-0" />
                <h3 className="text-xl font-semibold text-[#004235]">{solution.title}</h3>
              </div>
              <ul className="space-y-2 text-sm text-gray-600 flex-grow">
                {solution.points.map((point, pIndex) => (
                  <li key={pIndex} className="flex">
                    <span className="text-[#028475] mr-2">✓</span>
                    <span>{point}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}