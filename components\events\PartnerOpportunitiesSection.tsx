"use client";

import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Handshake } from 'lucide-react';

export default function PartnerOpportunitiesSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-3">
            Partner with Us: Speaking Engagements & Joint Events
          </h2>
          <p className="text-lg text-gray-700">Speaker Opportunities & Event Collaboration</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {/* Request a Speaker Card */}
          <div className="bg-white p-8 rounded-lg shadow-lg text-center flex flex-col items-center">
            <MicVocal className="h-12 w-12 text-[#028475] mb-6" />
            <h3 className="text-2xl font-semibold text-[#004235] mb-3">
              Interested in a StreamLnk Speaker for Your Event?
            </h3>
            <p className="text-gray-700 mb-6 flex-grow">
              Our leadership and subject matter experts are available to speak on supply chain innovation, B2B tech, AI, global trade, and more.
            </p>
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white mt-auto w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/contact/request-speaker"> {/* Placeholder link */}
                REQUEST A STREAMLNK SPEAKER
              </Link>
            </Button>
          </div>

          {/* Propose Joint Event Card */}
          <div className="bg-white p-8 rounded-lg shadow-lg text-center flex flex-col items-center">
            <Handshake className="h-12 w-12 text-[#028475] mb-6" />
            <h3 className="text-2xl font-semibold text-[#004235] mb-3">
              Propose a Joint Webinar or Event
            </h3>
            <p className="text-gray-700 mb-6 flex-grow">
              We welcome collaboration with industry partners, associations, and thought leaders.
            </p>
            <Button 
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white mt-auto w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/contact/propose-event"> {/* Placeholder link */}
                DISCUSS EVENT PARTNERSHIP
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}