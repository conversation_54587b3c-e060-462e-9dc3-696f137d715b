import { <PERSON><PERSON><PERSON><PERSON>, Zap, TrendingDown, Handshake, Workflow, Award } from "lucide-react";

export default function BenefitsSection() {
  const benefits = [
    {
      icon: <ShieldAlert className="h-8 w-8 text-[#028475]" />,
      title: "Reduced Counterparty Risk",
      description: "Engage with new partners globally with greater assurance.",
    },
    {
      icon: <Zap className="h-8 w-8 text-[#028475]" />,
      title: "Faster Deal Closure",
      description: "Transparent information and secure processes accelerate negotiations and transactions.",
    },
    {
      icon: <TrendingDown className="h-8 w-8 text-[#028475]" />,
      title: "Lower Transaction Costs",
      description: "Minimized risk of fraud, disputes, and non-compliance saves money.",
    },
    {
      icon: <Handshake className="h-8 w-8 text-[#028475]" />,
      title: "Stronger Business Relationships",
      description: "Trust fosters long-term, mutually beneficial partnerships.",
    },
    {
      icon: <Workflow className="h-8 w-8 text-[#028475]" />,
      title: "Improved Operational Efficiency",
      description: "Clear processes and reliable data streamline your workflows.",
    },
    {
      icon: <Award className="h-8 w-8 text-[#028475]" />,
      title: "Enhanced Reputation",
      description: "Being part of a trusted, compliant global network enhances your own brand standing.",
    },
  ];

  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            What Transparency & Trust Mean for Your Business Growth
          </h2>
          <p className="text-xl text-[#028475] font-semibold">
            The Benefits of a Trust-Based Trading Ecosystem
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {benefits.map((benefit, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-lg flex flex-col items-center text-center hover:shadow-xl transition-shadow duration-300">
              <div className="rounded-full bg-[#004235]/10 p-4 mb-4">
                {benefit.icon}
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">{benefit.title}</h3>
              <p className="text-gray-700 text-sm flex-grow">{benefit.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}