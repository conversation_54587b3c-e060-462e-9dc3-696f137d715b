import { StandardizedTimeline } from "@/components/ui/standardized-timeline";
import { CheckCircle, CreditCard, FileText, Calendar } from "lucide-react";

export default function HowToUseBnplSection() {
  const steps = [
    {
      number: 1,
      icon: <CheckCircle className="h-8 w-8 text-white" />,
      title: "Confirm Eligibility",
      description: "Check your eligibility in your MyStreamLnk finance profile"
    },
    {
      number: 2,
      icon: <CreditCard className="h-8 w-8 text-white" />,
      title: "Select BNPL Option",
      description: "Choose BNPL during checkout or quote acceptance"
    },
    {
      number: 3,
      icon: <FileText className="h-8 w-8 text-white" />,
      title: "Agree to Terms",
      description: "Select payment terms (30, 45, or 60 days post-delivery)"
    },
    {
      number: 4,
      icon: <Calendar className="h-8 w-8 text-white" />,
      title: "Manage Repayment",
      description: "Track order and manage repayment via Treasury Center"
    }
  ];

  return (
    <StandardizedTimeline
      title="How to Use BNPL"
      description="Getting started with StreamLnk's Buy Now Pay Later option is simple and straightforward."
      steps={steps}
      bgColor="bg-white"
    />
  );
}